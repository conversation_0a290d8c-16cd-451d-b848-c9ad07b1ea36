<template>
  <el-drawer
    class="ms-drawer"
    title="采集报告"
    :visible.sync="drawerVisible"
    size="60%"
    append-to-body
    v-if="drawerVisible"
  >
    <el-row :gutter="10" style="height: 100%; padding: 10px;">
      <el-col :span="24" style="height: 60%; margin-bottom: 10px;">
        <el-card>
          <div slot="header">采集报告信息</div>
          <MsCrud
            ref="crudRef"
            :columns="columns"
            :isShowAdd="false"
            :operateBtns="[]"
            :operateWidth="120"
            api="system/etlLog/taskDetails"
          >
            <template #execStatus="{ row }">
              <i
                style="color: #d81e06; font-size: 20px"
                class="el-icon-error"
                v-if="row.execStatus == '2'"
              ></i>
              <i
                style="color: #0e932e; font-size: 20px"
                class="el-icon-success"
                v-else
              ></i>
            </template>

            <template #operate="{ row }">
              <el-link type="primary" :underline="false" icon="el-icon-s-order" @click="showLog(row)"
                >运行日志</el-link
              >
            </template>
          </MsCrud>
        </el-card>
      </el-col>
      <el-col :span="24" style="height: calc(40% - 10px)">
        <el-card>
          <div slot="header">采集报告信息</div>
          <pre class="content">{{ formattedJson }}</pre>
        </el-card>
      </el-col>
    </el-row>
  </el-drawer>
</template>

<script>
import MsCrud from "@/components/MsCrud";
export default {
  components: { MsCrud },
  data() {
    return {
      drawerVisible: false,
      columns: [
        {
          aliasName: "任务名称",
          elementName: "taskName",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "执行开始时间",
          elementName: "execStartTime",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "执行结束时间",
          elementName: "execEndTime",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "执行总耗时(秒)",
          elementName: "timeConsuming",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "执行状态",
          elementName: "execStatus",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "执行总行数",
          elementName: "syncNums",
          htmlType: "input",
          listFlag: "1",
        },
      ],
      execLog: ''
    };
  },
  computed: {
    formattedJson() {
      try {
        const parsed = JSON.parse(this.execLog);
        return JSON.stringify(parsed, null, 2);
      } catch (e) {
        return this.execLog; // 解析失败时返回原始数据
      }
    }
  },
  mounted() {},
  methods: {
    show(row) {
      this.execLog = ''
      this.drawerVisible = true;
      this.$nextTick(() => {
        this.$refs.crudRef.formData.taskId = row.taskId;
        this.$refs.crudRef.reload()
      });
    },
    showLog(row) {
      this.execLog = row.execLog
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  overflow: auto;
  padding: 10px;
  white-space: pre-wrap;
}
.el-card {
  height: 100%;
  ::v-deep {
    .el-card__body {
      height: calc(100% - 44px);
      padding: 0 !important;
    }
  }
}
</style>
