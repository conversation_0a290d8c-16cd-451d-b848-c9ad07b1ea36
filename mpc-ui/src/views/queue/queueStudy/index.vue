<template>
    <div class="studyCohortDetail-page">
        <div class="p-head-box" v-if="showSearch">
            <div class="fr-head">
                <el-form :model="searchForm" ref="form" label-width="72px" :inline="true" size="small">
                    <el-form-item label="切换队列">
                        <div class="sel-box">
                            <el-popover popper-class="pop-subject" placement="bottom" width="420" trigger="click"
                                @show="getSubjectAndGroupTree(false)" v-model="isShowPopper">
                                <el-table :data="subjectAndGroupTreeList" height="350px">
                                    <el-table-column width="150" property="subjectName" label="科研课题" align="center">
                                        <template slot-scope="scope">
                                            <div class="subject-item">
                                                <i class="el-icon-caret-right fr-icon"></i>
                                                {{scope.row.subjectName}}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="研究队列" align="center">
                                        <template slot-scope="scope">
                                            <div class="group-box"
                                                v-if="scope.row.groupList && scope.row.groupList.length > 0">
                                                <span class="group-item" :class="rgId == item.rgId ? 'active' : ''"
                                                    @click="selectGroup(scope.row, item.rgId, item.groupName, item.formCode)"
                                                    v-for="(item, index) in scope.row.groupList">
                                                    <el-tooltip class="item" effect="dark"
                                                        :content="`${item.groupName}(${item.researchQueueDataCount || 0})`"
                                                        placement="top-start">
                                                        <span>
                                                            {{item.groupName.length > 5 ?
                                                            item.groupName.substr(0,5)+'...' : item.groupName}}
                                                            ({{item.researchQueueDataCount || 0}})
                                                        </span>
                                                    </el-tooltip>
                                                </span>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div class="sel-div" slot="reference">
                                    <i class="arr-icon el-icon-arrow-down"></i>
                                    <div class="subject-text">{{selectGroupText}}</div>
                                </div>
                            </el-popover>
                            <el-button type="primary" plain size="mini" :disabled="lockStatus == '1'" icon="el-icon-edit" @click="toNanPai">纳排条件</el-button>
                        </div>                        
                    </el-form-item>
                    <div v-if="formCode" v-for="(row, rowIndex) in searchForm.fieldList" :key="rowIndex">
                        <el-form-item label="字段">
                            <el-select v-model="row.fieldName" @change="changeFieldName($event, rowIndex)">
                                <el-option :label="item.fieldLabel" :value="item.fieldName" :disabled="item.disabled"
                                    v-for="(item, index) in searchFieldList" :key="index"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="运算符">
                            <el-select v-model="row.condition" placeholder="" clearable filterable>
                                <el-option v-for="item in operativeSymbolList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>

                        </el-form-item>

                        <el-form-item label="参数" v-if="formCode">
                            <el-select class="set-width" v-if="row.fieldName && dictList[row.fieldName+'_dictCode']"
                                v-model="row.fieldValue"
                                :disabled="row.condition == 'empty' || row.condition == 'notEmpty'">
                                <el-option :label="item.label" :value="item.value"
                                    v-for="(item, index) in dictList[row.fieldName+'_dictCode']"
                                    :key="index"></el-option>
                            </el-select>
                            <el-date-picker unlink-panels  class="set-width" v-else-if="row.type == 'date'" v-model="row.fieldValue"
                                value-format="yyyy-MM-dd" type="date"
                                :disabled="row.condition == 'empty' || row.condition == 'notEmpty'" placeholder="选择日期">
                            </el-date-picker>
                            <el-input class="set-width" v-else
                                :disabled="row.condition == 'empty' || row.condition == 'notEmpty'"
                                v-model="row.fieldValue" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-button type="danger" icon="el-icon-minus" circle size="mini"
                                v-if="searchForm.fieldList.length > 1" @click="delSearchLine(rowIndex)"></el-button>
                            <el-button type="primary" icon="el-icon-plus" circle size="mini"
                                v-if="rowIndex == searchForm.fieldList.length - 1" @click="addSearchLine"></el-button>
                        </el-form-item>
                    </div>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="small" @click="search">查询</el-button>
                        <el-button type="primary" plain icon="el-icon-search" size="small" @click="reset">重置</el-button>                        
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="p-content mt10">
            <el-row :gutter="10" class="mb8" type="flex" justify="end">
                <el-button type="primary" plain size="small" @click="toTopic">创建队列</el-button>
                <el-button type="success" plain icon="el-icon-plus" size="small"
                    @click="toFormPage('add')">病例注册</el-button>
                <el-button type="warning" plain icon="el-icon-plus" size="small"
                    @click="exportList(2)">导出数据</el-button>
            </el-row>
            <el-table :data="studyList" v-loading="loading">
                <el-table-column label="队列信息" align="center">
                    <el-table-column label="患者姓名" align="center">
                        <template slot-scope="scope">
                            <el-link type="primary" @click="view(scope.row)">{{
                                scope.row.pat_name
                            }}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column label="性别" align="center">
                        <template slot-scope="scope">
                            <dict-tag
                                :options="dictGender"
                                :value="scope.row.gender"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column label="年龄" align="center" prop="age" />
                    <el-table-column label="电话" align="center" prop="phone" />
                    <el-table-column label="出生日期" align="center" prop="birthday" />
                    <el-table-column label="证件类型" align="center">
                        <template slot-scope="scope">
                            <dict-tag
                                :options="dict.type.sd_id_type"
                                :value="scope.row.id_type"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column label="证件号" align="center" prop="id_card" width="200" />
                </el-table-column>
                <el-table-column label="访视阶段" align="center" v-if="dynamicHeaders.length > 0">
                    <!-- 动态表头 -->
                    <el-table-column
                        v-for="(item, i) in dynamicHeaders"
                        :key="i"
                        :label="item"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-link type="primary" @click="goForm(scope.row,scope.row[item])">{{ scope.row[item] && scope.row[item].percentage ? scope.row[item].percentage + "%" : 0 }}</el-link>
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </div>
    </div>
</template>

<script>
import {getSdResearchQueueData, getTopicList, getGrouplist, delFromMongo} from '@/api/disease/caseManage'
import { listSubject } from '@/api/disease/subject';
import {mapGetters} from 'vuex'
import {getFormTemplate} from '@/api/form/form'
import { getToken } from "@/utils/auth";
import { getResearchQueueListData } from "@/api/queue/study";
export default {
    name: 'Studied',
    dicts: ["sd_dict_sex", "sd_id_type","pf_task_end_condition",
    "sys_user_sex",
    "followup_type",
    "followup_status",
    "followup_form_status"],
    data() {
        return {
            rgId: '',
            experimentalGroupList: [], //实验组列表
            queryParams: {
                rsId: '',
                rgId: '',
                pageNum: 1,
                pageSize: 10,
                selectFields: []
            },
            showSearch: true,
            list: [],
            total: 0, //总条数
            isShowPopper: false,
            subjectAndGroupTreeList: [], //课题实验组数据
            selectGroupText: '', //选择的课题实验组名称
            formCode: '', //自定义表单code
            lockStatus:'',// 是否锁定
            selectedUserList: [], //选中的用户数据
            userListAllSelections: [], //选中的总人数
            searchForm: {
                fieldList: [
                    {
                        fieldName: '',
                        condition: '',
                        fieldValue:''
                    }
                ]
            },
            searchFieldList: [],
            dictList: {}, //数据字典
            customColumn: [],
            operativeSymbolList: [
                {label: '大于', value: '>'},
                {label: '小于', value: '<'},
                {label: '大于等于', value: '>='},
                {label: '小于等于', value: '<='},
                {label: '等于', value: '='},
                {label: '不等于', value: '!='},
                {label: '包含', value: 'contains'},
                {label: '不包含', value: 'notContains'},
                {label: '为空', value: 'empty'},
                {label: '不为空', value: 'notEmpty'},
            ],
            isFirstLoading: true, //是否是第一次加载
            createStatisticsFileParams: {
                formCodeJson: '',
            }, //统计分析所需的字段参数
            studyList:[], // 研究列表
            total:0,
            loading:false,
            dynamicHeaders:[] //动态表头

        }
    },
    computed: {
		...mapGetters(['currentSelectedDisease']),
        dictGender() {
            this.dict.type.sd_dict_sex.forEach(item => {
                item.raw.listClass = ''
            })
            return this.dict.type.sd_dict_sex
        }
	},
    watch: {
        formCode(newVal, oldVal) {
            if(newVal) {
                this.getFormTemplateJson();
            }
        }
	},
    mounted() {
        let rgId = this.$route.query.rgId;
        let rsId = this.$route.query.rsId;
        this.rgId = rgId;
        this.lockStatus = this.$route.query.lockStatus 
        this.queryParams.rgId = Number(rgId) || '';
        this.queryParams.rsId = Number(rsId) || '';
        this.getSubjectAndGroupTree();
        if(rgId && rsId) {
            this.$router.replace({query: {}})
        }
        
        setTimeout(_ => {
            this.isFirstLoading = false;
        },2000)
    },
    activated() {
        console.log(2222)
        // if(this.isFirstLoading) return false;
        // let rgId = this.$route.query.rgId;
        // let rsId = this.$route.query.rsId;
        // if(rgId && rsId) {
        //     this.$router.replace({
        //         query: {}
        //     })
        //     this.rgId = rgId;
        //     this.queryParams.rgId = Number(rgId) || '';
        //     this.queryParams.rsId = Number(rsId) || '';
        // }
        // this.getSubjectAndGroupTree();
    },
    methods: {
        //获取课题实验组树
        getSubjectAndGroupTree(loadingList = true) {
            listSubject({diseaseSyscode: this.currentSelectedDisease.diseaseSyscode}).then(response => {
                let list = [];
                response.rows.forEach(item => {
                    if(item.groupList && item.groupList.length > 0) {
                        list.push(item);
                    }
                })
                if(this.rgId) {
                    list.forEach(subjectItem => {
                        subjectItem.groupList.forEach(groupItem => {
                            if(this.rgId == groupItem.rgId) {
                                this.selectGroupText = `${subjectItem.subjectName}/${groupItem.groupName}`;
                                this.formCode = groupItem.formCode;
                                if(loadingList) {
                                    this.getList();
                                }
                            }
                        })
                    })
                } else if(list.length > 0){
                    this.rgId = list[0].groupList[0].rgId;
                    this.queryParams.rgId = this.rgId;
                    this.queryParams.rsId = list[0].rsId;
                    this.selectGroupText = `${list[0].subjectName}/${list[0].groupList[0].groupName}`;
                    this.formCode = list[0].groupList[0].formCode;
                    this.lockStatus = list[0].lockStatus
                    if(loadingList) {
                        this.getList();
                    }
                }
                this.subjectAndGroupTreeList = list;
            })
        },
        //选择实验组
        selectGroup(row, rgId, rgName, formCode = '') {
            if(rgId == this.rgId) return false;
            this.rgId = rgId
            this.queryParams.rsId = row.rsId;
            this.queryParams.rgId = rgId;
            this.selectGroupText = `${row.subjectName}/${rgName}`;
            this.formCode = formCode;
            this.lockStatus = row.lockStatus
            this.userListAllSelections = [];
            this.queryParams.pageNum = 1;
            this.getList();
            this.isShowPopper = false;
        },
        /** 查询研究队列列表 */
        async getList() {
            this.loading = true;
            try {
                const response = await getResearchQueueListData(this.queryParams)
                this.studyList = response.rows;
                this.total = response.total;
                this.dynamicHeaders = response.headers || [];  
            }catch(err) {
            }finally{
                this.loading = false; 
            }   
        },
        //获取当前选择的实验组关联的crf表单数据
        getFormTemplateJson() {
            getFormTemplate({formKey: this.formCode})
            .then(res => {
                let formJson = JSON.parse(res.data.templateJson);
                let fildList = [];
                let fildList1 = []; 
                let _this = this;
                function recursiveFn(arr) {
                    for(let i = 0; i < arr.length; i++) {
                        if(arr[i].type != 'sub-form') {
                            if(arr[i].type == 'radio' || arr[i].type == 'checkbox' || arr[i].type == 'select') {
                                let item = {
                                    fieldName: arr[i].options.name,
                                    fieldLabel: arr[i].options.label,
                                    type: arr[i].type,
                                    disabled: false
                                }
                                if(arr[i].options.dict) {
                                    item.dict = arr[i].options.dict
                                    _this.getDictData(arr[i].options.dict, arr[i].options.name+'_dictCode')
                                }else {
                                    item.optionItems = arr[i].options.optionItems;
                                    _this.$set(_this.dictList, arr[i].options.name+'_dictCode',  arr[i].options.optionItems)
                                }
                                fildList.push(item);
                            }else if(arr[i].widgetList) {
                                recursiveFn(arr[i].widgetList)
                            }else if(arr[i].cols && arr[i].cols) {
                                recursiveFn(arr[i].cols)
                            }else{
                                let item = {
                                    fieldName: arr[i].options.name,
                                    fieldLabel: arr[i].options.label,
                                    type: arr[i].type
                                }
                                fildList.push(item);
                            }
                        }else {
                            let item = {
                                 fieldName: arr[i].options.name,
                                 type: arr[i].type,
                                 field: []
                            }
                            arr[i].widgetList.forEach(fieldItem => {
                                let subField = {
                                    fieldName: fieldItem.options.name,
                                    fieldLabel:fieldItem.options.label,
                                    type: fieldItem.type
                                }
                                if(fieldItem.type == 'radio' || fieldItem.type == 'checkbox' || fieldItem.type == 'select') {
                                    if(fieldItem.options.dict) {
                                        subField.dict = fieldItem.options.dict
                                    }else {
                                        subField.optionItems = fieldItem.options.optionItems;
                                    }
                                }
                                item.field.push(subField)
                            })
                            fildList1.push(item)
                        }
                    }
                }
                recursiveFn(formJson.widgetList); 
                this.searchFieldList = fildList.filter(item => item.type !='stage_definition_condition' && item.type !='stage_definition_group');
                let params = [...fildList,...fildList1];
                this.createStatisticsFileParams.formCodeJson = JSON.stringify(params);
            })
        },
         //获取数据字典
        getDictData(dictCode, key) {
            if(!this.dictList[dictCode]) {
                this.getDicts(dictCode).then(res => {
                    res.data.forEach(item => {
                        item.label = item.dictLabel;
                        item.value = item.dictValue;
                        item.raw = { listClass: null }
                    });
                    this.$set(this.dictList, key, res.data);
                });
            }
        },
        //添加一行查询条件
        addSearchLine() {
            this.searchForm.fieldList.push(
                {
                    fieldName: '',
                    fieldValue:'',
                    condition: ''
                }
            )
        },
        //删除一行查询条件
        delSearchLine(index) {
            let itemField = this.searchForm.fieldList[index];
            if(itemField.fieldName) {
                this.searchFieldList.map((item) => {
                    if(item.fieldName == itemField.fieldName){
                        item.disabled = false;
                    }
                })
            }
            this.searchForm.fieldList.splice(index, 1)
        },
        //选择字段
        changeFieldName(val, rowIndex) {
            this.searchFieldList.forEach(item => {
                item.disabled = false;
                if(item.fieldName == val) {
                    this.$set(this.searchForm.fieldList[rowIndex], 'type', item.type);
                    this.$set(this.searchForm.fieldList[rowIndex], 'fieldLabel', item.fieldLabel);
                }
                this.searchForm.fieldList.forEach(fItem => {
                    if(item.fieldName == fItem.fieldName) {
                        item.disabled = true;
                    }
                })
            })
            this.searchForm.fieldList[rowIndex].fieldValue = '';
        },
        /** 搜索按钮操作 */
		search() {
            let columns = [];
            let selectFields = [];
            this.searchForm.fieldList.forEach(item => {
                let fieldName = item.fieldName.split('_and_');
                let obj = {
                    fieldName:item.fieldName , 
                    fieldLabel: item.fieldLabel,
                }
                if(this.dictList[item.fieldName+'_dictCode'] && this.dictList[item.fieldName+'_dictCode'].length > 0) {
                    obj.dict = this.dictList[item.fieldName+'_dictCode']
                }
                if(fieldName.length > 1) {   
                    if(
                        fieldName[1] != 'pat_name' && 
                        fieldName[1] != 'gender' && 
                        fieldName[1] != 'age' && 
                        fieldName[1] != 'phone' && 
                        fieldName[1] != 'birthday' && 
                        fieldName[1] != 'id_type' && 
                        fieldName[1] != 'id_card' && 
                        fieldName[1] != 'address'){
                            columns.push(obj)
                        }
                } else {
                    columns.push(obj)
                }
                selectFields.push({
                    condition: item.condition,
                    fieldName: item.fieldName,
                    fieldValue: item.fieldValue
                })   
            })
            this.customColumn = columns;
            this.queryParams.pageNum = 1;
            this.queryParams.selectFields = selectFields;
            this.userListAllSelections = [];
            this.getList();
		},
		/** 重置按钮操作 */
		reset() {
			this.searchForm.fieldList = [
                {
                    fieldName: '',
                    fieldValue:'',
                    condition: ''
                }
            ]
            this.searchFieldList.map(item => item.disabled = false)
            this.queryParams.selectFields = [];
            this.userListAllSelections = [];
            this.queryParams.pageNum = 1;
            this.getList();
		},        
        //导出 type: 1选中数据 2全部数据
        exportList(type) {
            if(!this.formCode) {
                this.$message.error('当前队列没有关联表单信息!');
                return false;
            }
            let empis = [];
            if(type == 1) {
                this.userListAllSelections.forEach(item => {
                    if(item.EMPI) {
                        empis.push(item.EMPI);
                    }
                })
            }
            this.download("disease/group/export", {empis: empis.join(','), rgId: this.rgId}, `研究队列导出数据_${new Date().getTime()}.xlsx`);
        },
        //跳转患者360
        view(row) {
            if(!row.EMPI) return this.$modal.msgWarning('患者empi不存在！')
            this.$jumpPatient360({ path: "/cdrView", query: { empi: row.EMPI } }, row);
        },
        // 去纳排
        toNanPai() {
         if(!this.rgId || !this.queryParams.rsId) return;
            this.$router.push({path:"/addtopic",query:{
                    rsId:this.queryParams.rsId,
                    rgId: this.rgId || "", // 队列id
                    formCode:this.formCode || "",
                    isLookfilter:3 // 查看筛选
                }
            });
        },
        // 创建队列
        toTopic() {
            this.$router.push("/queue")
        },
        //病例注册
        toFormPage(type, row) {
            if(!this.formCode) {
                this.$message.error('当前队列没有关联表单信息!');
                return false;
            }
            let queryData = {
                formKey: this.formCode, 
                rgId: this.rgId,
                type: type,
                rsId: this.queryParams.rsId
            }
            this.$router.push({path: '/queue/form', query: queryData})
        },
        // 填充form
        goForm(row,item) {
            this.$router.push({
                path:"/queue/form",
                query:{rgId:this.queryParams.rgId,empi:row.EMPI,formKey:item.formCode,id:item.visitId,name:row.pat_name,age:row.age,gender:row.gender,type:"edit"}
            })
        }
    }
}
</script>

<style lang="scss">
.studyCohortDetail-page {
    height: 100%;
    // overflow: hidden;
    background: #F2F2F2;
    .p-head-box {
        background: #fff;
        padding: 15px 20px 0;
        // margin-bottom: 10px;
        line-height: 24px;
        .fl-head {
            // float: left;
            width: 500px;
        }
        .fr-head {
            // margin-left: 520px
            .set-sel-ipt-width {
                width: 400px;
            }
        }
        .p-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding-right: 20px;
        }
        .p-info {
            font-size: 15px;
            color: #333;
        }
        .sel-box {
            display: flex;
            overflow: hidden;
            .label {
                font-size: 14px;
                color: #555;
                float: left;
                line-height: 32px;
            }
            .sel-div {
                // margin: 0 10px 0 72px;
                margin: 0 10px 0 0px;
                width: 418px;
                height: 32px;
                border:1px solid #DCDFE6;
                border-radius: 4px;
                padding:0 10px;
                font-size: 14px;
                color: #666;
                line-height: 30px;
            }
            .arr-icon {
                float: right;
                margin-top:10px;
            }
            .subject-text {
                margin-right: 30px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }
        }
        .selectType .el-input {
            width: 110px;
        }
        .set-width {
            width: 220px;
        }
    }
    .selectUserNum {
        font-size: 16px;
        color: #666;
        line-height: 26px;
        float: left;
        .user-nums {
            font-size: 18px;
            color: #1890ff;
            padding: 0 2px;
        }
    }
    .p-content {
        // height: calc(100vh - 260px);
        padding: 15px 20px;
        box-sizing: border-box;
        background-color: #fff;
    }
}
.pop-subject {
     padding: 0px 0px 10px !important;
    .subject-item {
        color: #333;
        font-weight: bold;
        text-align: left;
        .fr-icon {
            color: #999;
            float: right;
            margin-top: 4px;
        }
    }
    .group-box {
        text-align: left;
        .group-item {
            // padding-right: 5%;
            color: #666;
            display: inline-block;
            cursor: pointer;
            width: 50%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .active {
            color: #179e84;
            font-weight: bold;
        }
    }
    .set-tab-header-h {
        padding: 0;
        height: 34px;
    }
}
</style>