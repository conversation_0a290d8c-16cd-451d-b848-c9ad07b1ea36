<template>
  <div class="upload-file-wrapper">
    <!-- 上传文件组件 -->
    <uploader 
      class="uploader-app" 
      ref="uploader" 
      :options="options" 
      :autoStart="false" 
      @files-added="handleFilesAdded"
      @file-removed="handleFileRemoved" 
      @file-success="handleFileSuccess" 
      @file-error="handleFileError"
      :file-status-text="fileStatusTextObj"
    >
      <uploader-unsupport></uploader-unsupport>
      
      <!-- 选择按钮 -->
      <uploader-btn class="select-file-btn" :attrs="attrs" ref="uploadBtn">选择文件</uploader-btn>
      <uploader-btn class="select-file-btn" :attrs="attrs" :directory="true" ref="uploadDirBtn">选择目录</uploader-btn>
      
      <!-- 拖拽上传区域 -->
      <uploader-drop>
        <div class="drop-box">
          <div class="upload-icon">
            <i class="el-icon-upload"></i>
          </div>
          <div class="drop-text">
            <p class="main-text">将文件或文件夹拖拽到此处</p>
            <p class="sub-text">
              或者
              <span class="upload-btn" @click="handleClickUploadBtn">选择文件</span>
              /
              <span class="upload-btn" @click="handleClickUploadDirBtn">选择文件夹</span>
            </p>
            <p class="tips">支持 .zip 格式文件</p>
          </div>
        </div>
      </uploader-drop>
      
      <!-- 文件列表 -->
      <uploader-list>
        <template v-slot:default="props">
          <div class="file-panel" v-if="props.fileList.length || folderStructure.length">
            
            <!-- 文件夹结构展示 -->
            <div v-if="folderStructure.length" class="folder-structure">
              <div class="structure-header">
                <span class="title">文件夹结构</span>
                <span class="count">（{{ totalFilesCount }} 个文件）</span>
                <el-button 
                  type="text" 
                  size="mini" 
                  @click="toggleFolderView"
                  class="toggle-btn"
                >
                  {{ showFolderView ? '收起' : '展开' }}
                </el-button>
              </div>
              
              <div v-show="showFolderView" class="folder-tree">
                <div 
                  v-for="folder in folderStructure" 
                  :key="folder.path" 
                  class="folder-item"
                >
                  <div class="folder-header" @click="toggleFolder(folder.path)">
                    <i :class="folder.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                    <span class="folder-name">{{ folder.name }}</span>
                    <span class="file-count">（{{ folder.files.length }} 个文件）</span>
                    <span class="folder-progress">
                      {{ getFolderProgress(folder) }}%
                    </span>
                  </div>
                  
                  <div v-show="folder.expanded" class="folder-files">
                    <div 
                      v-for="file in folder.files" 
                      :key="file.id" 
                      class="folder-file-item"
                    >
                      <i class="el-icon-document"></i>
                      <span class="file-name">{{ file.name }}</span>
                      <span class="file-size">{{ formatFileSize(file.size) }}</span>
                      <span class="file-status" :class="getFileStatusClass(file)">
                        {{ getFileStatusText(file) }}
                      </span>
                      <div class="file-progress">
                        <el-progress 
                          :percentage="getFileProgress(file)" 
                          :status="getProgressStatus(file)"
                          :show-text="false"
                          :stroke-width="4"
                        ></el-progress>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 传统文件列表 -->
            <div class="file-list-section">
              <div class="file-title">
                <span class="title-span">
                  上传列表<span class="count">（{{ props.fileList.length }}）</span>
                </span>
                <div class="list-actions">
                  <el-button type="text" size="mini" @click="pauseAll">全部暂停</el-button>
                  <el-button type="text" size="mini" @click="resumeAll">全部继续</el-button>
                  <el-button type="text" size="mini" @click="clearCompleted">清除已完成</el-button>
                </div>
              </div>
              
              <!-- 总体进度 -->
              <div v-if="props.fileList.length" class="overall-progress">
                <div class="progress-info">
                  <span>总进度：{{ overallProgress }}%</span>
                  <span>{{ completedCount }}/{{ props.fileList.length }} 个文件</span>
                  <span v-if="uploadSpeed">{{ uploadSpeed }}</span>
                </div>
                <el-progress 
                  :percentage="overallProgress" 
                  :show-text="false"
                  :stroke-width="6"
                ></el-progress>
              </div>
              
              <!-- 文件列表 -->
              <ul class="file-list">
                <li class="file-item" v-for="file in props.fileList" :key="file.id">
                  <uploader-file :file="file" :list="true">
                    <template v-slot:default="fileProps">
                      <div class="file-item-content">
                        <!-- 文件图标 -->
                        <div class="file-icon">
                          <i class="el-icon-document"></i>
                        </div>
                        
                        <!-- 文件信息 -->
                        <div class="file-info">
                          <div class="file-name" :title="fileProps.file.name">
                            {{ fileProps.file.name }}
                          </div>
                          <div class="file-details">
                            <span class="file-size">{{ fileProps.formatedSize }}</span>
                            <span class="file-path" v-if="fileProps.file.relativePath">
                              {{ fileProps.file.relativePath }}
                            </span>
                          </div>
                        </div>
                        
                        <!-- 进度信息 -->
                        <div class="progress-info">
                          <div class="progress-text">
                            <span v-if="md5Status[file.id]" class="md5-status">
                              {{ md5Status[file.id] }}
                            </span>
                            <span v-else-if="fileProps.status === 'uploading'" class="upload-status">
                              {{ fileProps.progressStyle.progress }} - {{ fileProps.formatedAverageSpeed }}
                              <span v-if="fileProps.formatedTimeRemaining">
                                - 剩余{{ fileProps.formatedTimeRemaining }}
                              </span>
                            </span>
                            <span v-else class="status-text">
                              {{ fileStatusTextObj[fileProps.status] }}
                            </span>
                          </div>
                          
                          <!-- 进度条 -->
                          <div class="progress-bar">
                            <el-progress 
                              :percentage="parseFloat(fileProps.progressStyle.progress) || 0"
                              :status="getProgressStatus(fileProps)"
                              :show-text="false"
                              :stroke-width="4"
                            ></el-progress>
                          </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="file-actions">
                          <el-button 
                            v-if="fileProps.status === 'error'" 
                            type="text" 
                            size="mini"
                            @click="onRetry(fileProps.file)"
                          >
                            重试
                          </el-button>
                          <el-button 
                            v-if="fileProps.status === 'uploading'" 
                            type="text" 
                            size="mini"
                            @click="onPause(fileProps.file)"
                          >
                            暂停
                          </el-button>
                          <el-button 
                            v-if="fileProps.status === 'paused'" 
                            type="text" 
                            size="mini"
                            @click="onResume(fileProps.file)"
                          >
                            继续
                          </el-button>
                          <el-button 
                            v-if="!md5Status[file.id] && fileProps.status !== 'success'" 
                            type="text" 
                            size="mini"
                            @click="onFileRemove(fileProps.file)"
                          >
                            删除
                          </el-button>
                        </div>
                      </div>
                    </template>
                  </uploader-file>
                </li>
              </ul>
            </div>
          </div>
        </template>
      </uploader-list>
    </uploader>
  </div>
</template>

<script>
import SparkMD5 from 'spark-md5'
import { getToken } from "@/utils/auth";
import { mergeChunks } from "@/api/file/file";
import { createInfo, updateFileInfoStatus } from "@/api/oct/index";

const CHUNK_SIZE = 20 * 1024 * 1024 // 每个分片的大小

export default {
  name: 'UploadLargeFilesEnhanced',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 上传组件配置项
      options: {
        target: process.env.VUE_APP_BASE_API + "/file/chunk",
        chunkSize: CHUNK_SIZE,
        fileParameterName: 'file',
        maxChunkRetries: 3,
        testChunks: true,
        headers: { Authorization: "Bearer " + getToken() },
        query: this.formData,
        checkChunkUploadedByResponse: (chunk, response) => {
          let result = JSON.parse(response)
          if (result.code && result.code != 200) {
            chunk.uploader.pause()
          }
          return (result.chunkNumbers || []).indexOf(chunk.offset + 1) >= 0
        },
        parseTimeRemaining: function (timeRemaining, parsedTimeRemaining) {
          return parsedTimeRemaining
            .replace(/\syears?/, "年")
            .replace(/\days?/, "天")
            .replace(/\shours?/, "小时")
            .replace(/\sminutes?/, "分钟")
            .replace(/\sseconds?/, "秒");
        }
      },
      attrs: {
        accept: '.zip'
      },
      fileStatusTextObj: {
        success: "上传成功",
        error: "上传失败",
        uploading: "正在上传",
        paused: "已暂停",
        waiting: "等待中",
      },
      filesList: [],
      md5Status: {},
      folderStructure: [], // 文件夹结构
      showFolderView: true, // 是否显示文件夹视图
      uploadStats: {
        totalSize: 0,
        uploadedSize: 0,
        speed: 0
      }
    }
  },
  computed: {
    uploaderInstance() {
      return this.$refs.uploader.uploader
    },
    totalFilesCount() {
      return this.folderStructure.reduce((total, folder) => total + folder.files.length, 0)
    },
    overallProgress() {
      if (!this.filesList.length) return 0
      const totalProgress = this.filesList.reduce((sum, file) => {
        return sum + (parseFloat(file.progress) || 0)
      }, 0)
      return Math.round(totalProgress / this.filesList.length)
    },
    completedCount() {
      return this.filesList.filter(file => file.isComplete).length
    },
    uploadSpeed() {
      if (this.uploadStats.speed > 0) {
        return this.formatSpeed(this.uploadStats.speed)
      }
      return ''
    }
  },
  methods: {
    handleClickUploadBtn() {
      this.$refs.uploadBtn.$el.click()
    },
    handleClickUploadDirBtn() {
      this.$refs.uploadDirBtn.$el.click()
    },

    handleFilesAdded(files) {
      this.filesList = [...this.filesList, ...files]
      this.$emit('fileList', this.filesList)

      // 分析文件夹结构
      this.analyzeFolderStructure(files)

      files.forEach((file) => {
        this.computeMD5(file)
      })
    },

    handleFileRemoved(file) {
      this.filesList = this.filesList.filter((item) => item.id != file.id)
      this.$emit('fileList', this.filesList)

      // 更新文件夹结构
      this.updateFolderStructure()
    },

    handleFileSuccess(rootFile, file, response) {
      let result = response ? JSON.parse(response) : ''

      // 更新文件状态
      this.updateFileInStructure(file, 'success')

      if (result.code == 200) return

      const formData = new FormData();
      formData.append("identifier", file.uniqueIdentifier);
      formData.append("filename", file.name);
      formData.append("relativePath", file.relativePath);
      formData.append("totalSize", file.size);

      mergeChunks(formData).then((res) => {
        this.filesList = this.filesList.filter((item) => item.id != file.id)
        this.$emit('fileList', this.filesList)

        if (Notification.permission === 'granted') {
          new Notification('文件上传完成', { body: file.name })
        }

        let params = {
          identifier: file.uniqueIdentifier,
          status: 1,
          fileUrl: res.data.fileUrl
        }
        updateFileInfoStatus(params)

        // 更新文件夹结构
        this.updateFolderStructure()
      })
    },

    handleFileError(rootFile, file, response) {
      console.log(rootFile, file, response, 'error')
      this.updateFileInStructure(file, 'error')
    },

    // 分析文件夹结构
    analyzeFolderStructure(files) {
      const folderMap = new Map()

      files.forEach(file => {
        if (file.relativePath) {
          const pathParts = file.relativePath.split('/')
          const folderPath = pathParts.slice(0, -1).join('/')

          if (folderPath) {
            if (!folderMap.has(folderPath)) {
              folderMap.set(folderPath, {
                path: folderPath,
                name: pathParts[pathParts.length - 2] || folderPath,
                files: [],
                expanded: true
              })
            }
            folderMap.get(folderPath).files.push(file)
          }
        }
      })

      this.folderStructure = Array.from(folderMap.values())
    },

    // 更新文件夹结构
    updateFolderStructure() {
      this.folderStructure.forEach(folder => {
        folder.files = folder.files.filter(file =>
          this.filesList.some(f => f.id === file.id)
        )
      })
      this.folderStructure = this.folderStructure.filter(folder => folder.files.length > 0)
    },

    // 更新文件夹中的文件状态
    updateFileInStructure(file, status) {
      this.folderStructure.forEach(folder => {
        const fileIndex = folder.files.findIndex(f => f.id === file.id)
        if (fileIndex !== -1) {
          folder.files[fileIndex].status = status
        }
      })
    },

    // 切换文件夹展开/收起
    toggleFolder(folderPath) {
      const folder = this.folderStructure.find(f => f.path === folderPath)
      if (folder) {
        folder.expanded = !folder.expanded
      }
    },

    // 切换文件夹视图
    toggleFolderView() {
      this.showFolderView = !this.showFolderView
    },

    // 获取文件夹进度
    getFolderProgress(folder) {
      if (!folder.files.length) return 0
      const totalProgress = folder.files.reduce((sum, file) => {
        return sum + (parseFloat(file.progress) || 0)
      }, 0)
      return Math.round(totalProgress / folder.files.length)
    },

    // 获取文件进度
    getFileProgress(file) {
      return parseFloat(file.progress) || 0
    },

    // 获取进度条状态
    getProgressStatus(file) {
      if (file.status === 'success') return 'success'
      if (file.status === 'error') return 'exception'
      return null
    },

    // 获取文件状态样式类
    getFileStatusClass(file) {
      return {
        'status-success': file.status === 'success',
        'status-error': file.status === 'error',
        'status-uploading': file.status === 'uploading',
        'status-paused': file.status === 'paused'
      }
    },

    // 获取文件状态文本
    getFileStatusText(file) {
      return this.fileStatusTextObj[file.status] || '等待中'
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 格式化速度
    formatSpeed(bytesPerSecond) {
      return this.formatFileSize(bytesPerSecond) + '/s'
    },

    // 计算MD5
    async computeMD5(file) {
      return new Promise((resolve, reject) => {
        this.$set(this.md5Status, file.id, '分片处理中 0%')
        let fileReader = new FileReader()
        let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice
        let currentChunk = 0
        let chunks = Math.ceil(file.size / CHUNK_SIZE)
        let spark = new SparkMD5.ArrayBuffer()

        file.pause()
        loadNext()

        fileReader.onload = (e) => {
          spark.append(e.target.result)
          if (currentChunk < chunks) {
            currentChunk++
            loadNext()
            this.$set(this.md5Status, file.id, `分片处理中 ${((currentChunk / chunks) * 100).toFixed(0)}%`)
          } else {
            let md5 = spark.end()
            this.$set(this.md5Status, file.id, '')
            file.uniqueIdentifier = md5

            let params = {
              identifier: md5,
              manufacturerInfoId: this.formData.manufacturerInfoId,
              deviceTypeId: this.formData.deviceTypeId,
              fileSize: file.size,
              fileName: file.name
            }

            createInfo(params).then(res => {
              if (res.data.status == 1) {
                this.$message.warning('影像已存在，请勿重复上传')
                file.cancel()
              } else {
                file.resume()
                this.md5Status[file.id] = ''
                resolve()
              }
            })
          }
        }

        fileReader.onerror = () => {
          this.$message.error(`文件${file.name}读取出错，请检查该文件`)
          file.cancel()
          resolve()
        }

        function loadNext() {
          let start = currentChunk * CHUNK_SIZE
          let end = start + CHUNK_SIZE >= file.size ? file.size : start + CHUNK_SIZE
          fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
        }
      })
    },

    // 开始上传
    async uploadStart() {
      if (this.uploaderInstance.isUploading()) return
      for (let i = 0; i < this.filesList.length; i++) {
        let file = this.filesList[i]
        if (!file.completed) {
          await this.computeMD5(file)
        }
      }
    },

    // 文件操作方法
    onFileRemove(file) {
      file.cancel()
    },

    onRetry(file) {
      file.retry()
    },

    onPause(file) {
      file.pause()
    },

    onResume(file) {
      file.resume()
    },

    // 批量操作
    pauseAll() {
      this.filesList.forEach(file => {
        if (file.isUploading && file.isUploading()) {
          file.pause()
        }
      })
    },

    resumeAll() {
      this.filesList.forEach(file => {
        if (file.paused) {
          file.resume()
        }
      })
    },

    clearCompleted() {
      const completedFiles = this.filesList.filter(file => file.isComplete)
      completedFiles.forEach(file => {
        file.cancel()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-file-wrapper {
  .uploader-drop {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      background: #f0f8ff;
    }
  }

  .drop-box {
    text-align: center;
    padding: 40px 20px;

    .upload-icon {
      margin-bottom: 16px;

      .el-icon-upload {
        font-size: 48px;
        color: #c0c4cc;
      }
    }

    .drop-text {
      .main-text {
        font-size: 16px;
        color: #333;
        margin: 0 0 8px 0;
      }

      .sub-text {
        font-size: 14px;
        color: #666;
        margin: 0 0 8px 0;
      }

      .tips {
        font-size: 12px;
        color: #999;
        margin: 0;
      }

      .upload-btn {
        color: #1890ff;
        cursor: pointer;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }

  .file-panel {
    margin-top: 20px;

    .folder-structure {
      margin-bottom: 20px;
      border: 1px solid #e8e8e8;
      border-radius: 6px;

      .structure-header {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background: #f5f5f5;
        border-bottom: 1px solid #e8e8e8;

        .title {
          font-weight: 500;
          color: #333;
        }

        .count {
          margin-left: 8px;
          color: #666;
          font-size: 12px;
        }

        .toggle-btn {
          margin-left: auto;
        }
      }

      .folder-tree {
        .folder-item {
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .folder-header {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            cursor: pointer;
            transition: background 0.2s;

            &:hover {
              background: #f9f9f9;
            }

            i {
              margin-right: 8px;
              color: #1890ff;
            }

            .folder-name {
              font-weight: 500;
              color: #333;
            }

            .file-count {
              margin-left: 8px;
              color: #666;
              font-size: 12px;
            }

            .folder-progress {
              margin-left: auto;
              color: #1890ff;
              font-weight: 500;
            }
          }

          .folder-files {
            background: #fafafa;

            .folder-file-item {
              display: flex;
              align-items: center;
              padding: 8px 16px 8px 40px;
              border-bottom: 1px solid #f0f0f0;

              &:last-child {
                border-bottom: none;
              }

              i {
                margin-right: 8px;
                color: #666;
              }

              .file-name {
                flex: 1;
                color: #333;
                font-size: 13px;
              }

              .file-size {
                margin-left: 12px;
                color: #999;
                font-size: 12px;
                min-width: 60px;
              }

              .file-status {
                margin-left: 12px;
                font-size: 12px;
                min-width: 60px;

                &.status-success {
                  color: #52c41a;
                }

                &.status-error {
                  color: #ff4d4f;
                }

                &.status-uploading {
                  color: #1890ff;
                }

                &.status-paused {
                  color: #faad14;
                }
              }

              .file-progress {
                margin-left: 12px;
                width: 100px;
              }
            }
          }
        }
      }
    }

    .file-list-section {
      .file-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .title-span {
          font-weight: 500;
          color: #333;

          .count {
            color: #666;
            font-weight: normal;
          }
        }

        .list-actions {
          .el-button {
            margin-left: 8px;
          }
        }
      }

      .overall-progress {
        margin-bottom: 16px;
        padding: 12px;
        background: #f9f9f9;
        border-radius: 6px;

        .progress-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          font-size: 13px;
          color: #666;
        }
      }

      .file-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .file-item {
          border: 1px solid #e8e8e8;
          border-radius: 6px;
          margin-bottom: 8px;
          overflow: hidden;
          transition: all 0.2s;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
          }

          .file-item-content {
            display: flex;
            align-items: center;
            padding: 12px 16px;

            .file-icon {
              margin-right: 12px;

              i {
                font-size: 24px;
                color: #1890ff;
              }
            }

            .file-info {
              flex: 1;
              min-width: 0;

              .file-name {
                font-weight: 500;
                color: #333;
                margin-bottom: 4px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .file-details {
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #999;

                .file-size {
                  margin-right: 12px;
                }

                .file-path {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }

            .progress-info {
              flex: 0 0 200px;
              margin: 0 16px;

              .progress-text {
                font-size: 12px;
                color: #666;
                margin-bottom: 4px;
                text-align: center;

                .md5-status {
                  color: #1890ff;
                }

                .upload-status {
                  color: #52c41a;
                }

                .status-text {
                  color: #999;
                }
              }

              .progress-bar {
                width: 100%;
              }
            }

            .file-actions {
              flex: 0 0 auto;

              .el-button {
                margin-left: 4px;
              }
            }
          }
        }
      }
    }
  }
}

/* 隐藏上传按钮 */
.select-file-btn {
  display: none;
}

/* 深度选择器样式 */
::v-deep {
  .uploader-file-status {
    width: 32%;
  }

  .el-progress-bar__outer {
    background-color: #f0f0f0;
  }

  .el-progress-bar__inner {
    transition: width 0.3s ease;
  }
}
</style>
