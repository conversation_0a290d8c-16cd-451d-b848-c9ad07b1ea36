<!-- Pearson卡方检验 -->
<template>
  <div class="descriptiveStatistics-page">
    <analyseRecordComponents
      ref="analyseRecordComponents"
      :parentType="resultType"
    ></analyseRecordComponents>
    <!-- 左侧变量列表组件 -->
    <selectVariableFieldsComponent
      ref="selectVariableFieldsComponent"
      disabledFieldType=""
      :parentType="resultType"
      @clearField="clearField"
    ></selectVariableFieldsComponent>
    <!-- 右侧 -->
    <div class="fr-box">
      <div class="content">
        <div class="tips">
          <b>Pearson卡方检验:</b>
          用于分析定类变量X与定类变量Y之间的差异性。
          <span @click="showfunctionalDescriptionAlert"
            >详情 <i class="el-icon-arrow-right"></i>
          </span>
        </div>
        <div class="explain">
          放入<span class="col2"> [定类] </span>变量X (变量数=1)
        </div>
        <div class="fr-field-box set-height">
          <div class="field-list">
            <draggable
              class="fr-drawing fieldType2"
              :disabled="selectedField.length == 1"
              :list="selectedField"
              :animation="200"
              :group="{ name: 'selectedField', pull: false, put: true }"
            >
              <div
                class="field-item"
                v-for="(item, index) in selectedField"
                :key="index"
              >
                {{ item.columnName }}[{{
                  item.columnType == 1 ? "定量" : "定类"
                }}]
                <i
                  class="close-btn el-icon-close"
                  @click="delField(item, index, 1)"
                ></i>
              </div>
            </draggable>
          </div>
          <div v-if="selectedField.length == 0" class="drag-tip">
            拖拽变量到此区域
          </div>
        </div>

        <div class="explain">
          放入<span class="col2"> [定类] </span>变量Y (变量数=1)
        </div>
        <div class="fr-field-box set-height">
          <div class="field-list">
            <draggable
              class="fr-drawing fieldType2"
              :disabled="selectedField1.length == 1"
              :list="selectedField1"
              :animation="200"
              :group="{ name: 'selectedField1', pull: false, put: true }"
            >
              <div
                class="field-item"
                v-for="(item, index) in selectedField1"
                :key="index"
              >
                {{ item.columnName }}[{{
                  item.columnType == 1 ? "定量" : "定类"
                }}]
                <i
                  class="close-btn el-icon-close"
                  @click="delField(item, index, 2)"
                ></i>
              </div>
            </draggable>
          </div>
          <div v-if="selectedField1.length == 0" class="drag-tip">
            拖拽变量到此区域
          </div>
        </div>

        <div class="explain" v-if="false">
          放入<span class="col1"> [定量] </span>权重项 (非必选)
        </div>
        <div class="fr-field-box" v-if="false">
          <div class="field-list">
            <draggable
              class="fr-drawing fieldType1"
              :list="selectedField2"
              :animation="200"
              :group="{ name: 'selectedField2', pull: false, put: true }"
            >
              <div
                class="field-item"
                v-for="(item, index) in selectedField2"
                :key="index"
              >
                {{ item.columnName }}[{{
                  item.columnType == 1 ? "定量" : "定类"
                }}]
                <i
                  class="close-btn el-icon-close"
                  @click="delField(item, index, 2)"
                ></i>
              </div>
            </draggable>
          </div>
          <div v-if="selectedField2.length == 0" class="drag-tip">
            拖拽变量到此区域
          </div>
        </div>
        <div class="btn-group">
          <el-button size="small" @click="reset">重置</el-button>
          <el-button type="primary" size="small" @click="submit"
            >开始分析</el-button
          >
        </div>
      </div>
    </div>
    <functionalDescriptionAlert
      ref="functionalDescriptionAlert"
    ></functionalDescriptionAlert>
  </div>
</template>
<script>
import functionalDescriptionAlert from "../functionalDescriptionAlert";
import draggable from "vuedraggable";
import selectVariableFieldsComponent from "../selectVariableFieldsComponent";
import analyseRecordComponents from "../analyseRecordComponents";
import { checkExcel, pearsonDataCheck } from "@/api/disease/statisticalAnalysis";
export default {
  name: "Pearson",
  data() {
    return {
      selectedField: [], //选中的定类字段
      selectedField1: [], //选中的定量字段
      selectedField2: [], //选中的定量字段
      num: 1,
      resultType: "13",
    };
  },
  components: {
    functionalDescriptionAlert,
    draggable,
    selectVariableFieldsComponent,
    analyseRecordComponents,
  },
  activated() {
    let fileId = this.$route.query.fileId;
    if (fileId) {
      this.$refs.selectVariableFieldsComponent.showMyData(fileId);
    }
  },
  methods: {
    //移除选中的字段
    delField(item, index, type) {
      if (type == 1) {
        this.selectedField.splice(index, 1);
      } else {
        this.selectedField1.splice(index, 1);
      }
      this.$refs.selectVariableFieldsComponent.addFieldItem(item);
    },
    //type == del : 我的数据弹窗中进行删除操作时 , 如删除的是当前选择的文件 则清空当前已选的字段
    // else : 选择的文件发生变化后 将已选择的字段清空, 并且重亲获取分析历史记录
    clearField(fileObj, type) {
      if (type == "del") {
        if (
          this.selectedField.length > 0 &&
          this.selectedField[0].sdStatisticsFilesId == fileObj.fileId
        ) {
          this.selectedField = [];
          this.selectedField1 = [];
        }
        this.$refs.analyseRecordComponents.clearData(
          fileObj.fileId,
          fileObj.fileName
        );
      } else {
        this.selectedField = [];
        this.selectedField1 = [];
        this.$refs.analyseRecordComponents.initData(
          fileObj.fileId,
          fileObj.fileName
        );
      }
    },

    //显示功能描述弹窗
    showfunctionalDescriptionAlert() {
      let obj = {
        title: "Pearson卡方检验", //标题
        description:
          "Pearson卡方检验用于分析定类变量X与定类变量Y之间的差异性。若在2x2列联表中，所有的单元格理论数T≥5并且总样本量n≥40，建议使用Pearson卡方进行检验。若在R×C列联表(R>2或C>2)中，全部单元格理论数T>=1 且 1 &lt;=T&lt;5单元格的比例小于20% ，建议使用Pearson卡方。", //功能描述
        imgPath: require("@/assets/images/disease/kafangzidongjianyan.png"), //示例图片
        exampleDescription:
          "根据两种药物对于疾病的疗效情况，来分析两种药物的疗效是否有显著差异。", //示例描述
        inputDescription:
          "一个定类变量X（如药物，包括甲药物、乙药物）与定类变量Y（如痊愈、未痊愈）。", //输入描述
        outputDescription:
          "分组定类变量X与定类变量Y之间是否存在差异性及差异程度的大小。", //输出描述
      };
      this.$refs.functionalDescriptionAlert.init(obj);
    },
    //重置
    reset() {
      if (this.selectedField.length > 0) {
        this.selectedField.forEach((item) => {
          this.$refs.selectVariableFieldsComponent.addFieldItem(item);
        });
        this.selectedField = [];
      }
      if (this.selectedField1.length > 0) {
        this.selectedField1.forEach((item) => {
          this.$refs.selectVariableFieldsComponent.addFieldItem(item);
        });
        this.selectedField1 = [];
      }
    },
    submit() {
      if (this.selectedField.length == 0) {
        this.$message.warning("请先拖入变量X后在进行分析操作!");
        return false;
      } else if (this.selectedField1.length == 0) {
        this.$message.warning("请先拖入变量Y后在进行分析操作!");
        return false;
      }
      let list1 = this.selectedField.map((item) => {
        return {
          columnName: item.columnName,
          columnType: item.columnType,
          columnIndex: item.columnIndex,
          columnGroup: "21",
        };
      });
      let list2 = this.selectedField1.map((item) => {
        return {
          columnName: item.columnName,
          columnType: item.columnType,
          columnIndex: item.columnIndex,
          columnGroup: "22",
        };
      });
      let list3 = this.selectedField2.map((item) => {
        return {
          columnName: item.columnName,
          columnType: item.columnType,
          columnIndex: item.columnIndex,
          columnGroup: "3",
        };
      });
      let dataModelVoListArr = [...list1, ...list2, ...list3];
      let params = {
        statisticsColumnList: dataModelVoListArr,
        sdStatisticsFilesId: this.selectedField[0]?.sdStatisticsFilesId,
        resultType: this.resultType,
      };
      this.$modal.loading();
      checkExcel(params)
        .then((res) => {
          this.$modal.closeLoading();
          if (!res.checkResult) {
            let query = {
              componentName: "pearsonResult",
              dataModelVoListArr: JSON.stringify(dataModelVoListArr),
              fileId: this.selectedField[0].sdStatisticsFilesId,
            }
            sessionStorage.setItem("analysisResultParams", JSON.stringify(query))
            // 卡方条件校验
            let statisticalInfo = [];
            if (dataModelVoListArr) {
              statisticalInfo = dataModelVoListArr.map((item) => {
                return {
                  column_name: item.column_name || item.columnName,
                  data_type: item.data_type || item.columnGroup,
                };
              });
            }
            let pearsonParams = {
              sdStatisticsFilesId: this.selectedField[0].sdStatisticsFilesId,
              resultName: `Pearson卡方检验_${statisticalInfo.map((item) => item.column_name).join("_")}`,
              resultType: '13',
              statisticalInfo,
            };
            pearsonDataCheck(pearsonParams).then(res => {
              this.$router.push({
                path: "/descriptiveStatistics/analysisResult",
              });
            }).catch(err => {
              this.$confirm(`${err}，请确认是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.$router.push({
                  path: "/descriptiveStatistics/analysisResult",
                });
              }).catch(() => {
                          
              });
            })
          } else {
            this.$message.warning(`${res.checkResult}`);
          }
        })
        .catch((_) => {
          this.$modal.closeLoading();
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.descriptiveStatistics-page {
  background: #f8f8f8;
  padding: 10px;
  display: flex;
  height: 100%;

  .fr-box {
    flex: 1;
    padding: 0 20px;
    background: #fff;
    margin-left: 10px;
    overflow: auto;

    .content {
      padding: 14px 0 0;

      .tips {
        font-size: 14px;
        color: #a8abb2;

        span {
          color: #409eff;
          cursor: pointer;
        }

        b {
          color: #333;
          font-size: 16px;
        }
      }

      .explain {
        margin: 50px 0 0;
        font-size: 14px;
        color: #666;

        .col1 {
          color: #20d783;
        }

        .col2 {
          color: #409eff;
        }

        .e-icon {
          color: #999;
          font-size: 16px;
          vertical-align: middle;
          cursor: pointer;
        }
      }

      .fr-field-box {
        min-height: 20vh;
        border: 1px solid #eee;
        margin: 30px 0 0;
        overflow: auto;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .field-list {
          overflow: hidden;
          padding: 10px;
          position: relative;
          z-index: 99;
          width: 100%;
          .fr-drawing {
            min-height: 20vh;
          }

          .field-item {
            float: left;
            margin: 0 10px 10px 0;
            border: 1px solid #d9ecff;
            padding: 7px 10px;
            font-size: 14px;
            line-height: 20px;
            border-radius: 3px;
            color: #666;
            background: #ecf5ff;
            color: #409eff;
            cursor: move;

            .close-btn {
              cursor: pointer;
              padding: 0 0 0 5px;
            }
          }
        }

        .drag-tip {
          font-size: 20px;
          color: #999;
          text-align: center;
          line-height: 30px;
          position: absolute;
        }
      }

      .set-height {
        min-height: 80px;
        height: 80px;
        
        .field-list {
          height: 100%;
          .fr-drawing {
            min-height: 100%;
          }
        }
      }

      .btn-group {
        margin: 30px 0 0;
        text-align: center;
        padding: 20px;
      }
    }
  }
}
</style>
