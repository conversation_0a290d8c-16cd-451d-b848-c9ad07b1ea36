/**
 * 根据p值返回带星号标记的字符串
 * @param {number} val - p值
 * @returns {string} 带星号标记的p值字符串
 */
export function getPval(val) {
  // 检查输入是否为有效数字
  if (typeof val !== 'number' || isNaN(val)) {
    return '';
  }

  // 格式化数字，保留4位小数
  // const formattedVal = val.toFixed(4);
  const formattedVal = val;
  
  // 根据p值大小返回不同的星号标记
  if (val < 0.01) {
    return `${formattedVal}***`;  // p < 0.01，极显著
  } 
  if (val < 0.05) {
    return `${formattedVal}**`;   // 0.01 ≤ p < 0.05，显著
  }
  if (val < 0.1) {
    return `${formattedVal}*`;    // 0.05 ≤ p < 0.1，边际显著
  }
  
  return formattedVal;            // p ≥ 0.1，不显著
}