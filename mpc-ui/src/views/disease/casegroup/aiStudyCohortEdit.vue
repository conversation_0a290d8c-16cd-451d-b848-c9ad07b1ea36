<template>
  <div class="form-box" v-loading="loading">
    <div class="header">
      <div class="user-box">
        <div style="display: flex; align-items: center;" v-if="empi">
          <span class="pat-name user-item">{{ userInfo.pat_name }}</span>
          <img class="user-icon" src="@/assets//images/360.svg" @click="onGoPage" />
          <span class="user-item">
            <span class="label">性别：</span>
            <span class="value">{{ getSex(userInfo.gender)}}</span>
          </span>
          <span class="user-item">
            <span class="label">年龄：</span>
            <span class="value">{{ userInfo.age }}</span>
          </span>
          <span class="user-item" v-if="admitNo">
            <span class="label">住院号：</span>
            <span class="value">{{ admitNo }}</span>
          </span>
          <span class="user-item" v-if="baseLine">
            <span class="label">基线时间：</span>
            <span class="value">{{ baseLine }}</span>
          </span>
          <span class="user-item" v-if="baseLineToNowDays">
            <span class="label">今日距基线 </span>
            <span class="value">{{ baseLineToNowDays }}天</span>
          </span>
          <!-- 自身标签 -->
          <el-tooltip v-if="userInfo.selfQueue" content="当前归属队列" placement="top">
            <el-tag size="small">{{ handelTagLabel(userInfo.selfQueue) }}</el-tag>
          </el-tooltip>
          <!-- 其它标签 -->
          <span v-for="(t, i) in tagsList" :key="i">
            <el-tooltip content="所属其他队列" placement="top">
              <el-tag type="success" size="small" style="margin-left: 8px;">{{ handelTagLabel(t) }}</el-tag>
            </el-tooltip>
          </span>

        </div>
      </div>
      <div class="btn-box">
        <el-button type="primary" size="small" @click="onSave(1)">保 存</el-button>
        <el-button v-if="type == 'edit'" type="warning" size="small" @click="onAuditCompleted(1)">审核完成</el-button>
        <el-button v-if="type == 'edit'" type="warning" plain size="small"
          @click="onAuditCompleted(2)">审核后下一位</el-button>
      </div>
    </div>
    <div class="app-container">
      <div class="form-handel" v-if="leftWidth <= 100" @click="leftWidth = 220">
        <i class="el-icon-s-unfold"></i>
      </div>
      <div class="left-box" :style="{ 'min-width': leftWidth + 'px' }" v-if="leftWidth > 100">
        <i class="el-icon-s-fold" @click="leftWidth = 100"></i>
        <!-- 表单list -->
        <div class="form-title">表单目录</div>
        <div class="form-list" v-if="formJson && formJson.widgetList">
          <div class="form-item" :class="activeName == item.options.name ? 'active' : ''"
            v-for="item in formJson.widgetList" :key="item.id" @click="onFormItem(item)">
            <span class="to-examine-icon" :class="hasToExamineInfo(item) ? 'ing' : ''"></span>
            <span>{{ item.options.label }}</span>
          </div>
        </div>
      </div>
      <div class="drag-icon" @mousedown="initLeftResize" v-if="leftWidth > 100">
        <div class="drag-item" v-for="item in 8" :key="item">
          <div class="drag-line"></div>
          <div class="drag-line"></div>
        </div>
      </div>
      <div class="center-box" onselectstart="return false">
        <div class="v-form-box">
          <v-form-render :key="userCurrentIndex" ref="vFormRef" :form-json="vFormData" :global-dsv="globalDsv"></v-form-render>
        </div>
        <div class="btn-box">
          <el-button v-if="type == 'edit'" type="primary" size="small" @click="onSave(2)">审核提交</el-button>
          <el-button v-if="type == 'edit'" size="small" @click="onSave(3)">审核后继续</el-button>
        </div>
      </div>
      <div class="drag-icon" @mousedown="initRightResize">
        <div class="drag-item" v-for="item in 8" :key="item">
          <div class="drag-line"></div>
          <div class="drag-line"></div>
        </div>
      </div>
      <div class="right-box" :style="{ width: rightWidth + 'px' }">
        <!-- <ReferenceInfo ref="referenceInfoRef" /> -->
        <AiStructured ref="aiStructuredRef" v-if="vFormData && vFormData.formConfig"/>
      </div>
    </div>

    <UpdateDialog ref="updateDialogRef" />

    <el-dialog title="提示" :visible.sync="dialogVisible" width="600px" :show-close="false" :close-on-click-modal="false"
      :before-close="onClose">
      <span style="font-size: 16px;color: #333">{{ tipText }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose">取 消</el-button>
        <el-button type="primary" @click="onBack">立即返回</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import $ from 'jquery'
import { getToken } from '@/utils/auth'
import { getFormTemplate } from '@/api/form/form'
import { getSdResearchQueueDataDetail, getSearchDataByTableNameAndEmpi, saveFormForMongo, addFormForMongo, saveAuditedData, selectFormBaseLineData, alterAuditForDataStatus } from '@/api/disease/caseManage'
import UpdateDialog from './updateReasonDialog.vue'
import ReferenceInfo from './referenceInfo.vue'
import AiStructured from './aiStructured.vue'
import { mapGetters } from "vuex";
import moment from "moment";
export default {
  dicts: ['sd_researchQueue_used', "sd_dict_sex"],
  // name: 'StudyCohortEdit',
  components: {
    UpdateDialog,
    ReferenceInfo,
    AiStructured
  },
  data() {
    return {
      loading: false,
      leftWidth: 220,
      rightWidth: 400,
      isResizingLeft: false,
      isResizingRight: false,
      maxWidth: 0,
      activeName: '',
      formJson: {},
      formData: {},
      oldFormData: {},
      dictList: {},
      // 表单修改过的
      changeData: {
        custom: {
          card: '',
          fields: [],
        }
      },
      vFormData: {},
      formKey: "",
      type: '',
      empi: '',
      rgId: '',
      rsId: '',
      encountId: '',
      baseLine: '',
      baseLineToNowDays: '',
      admitNo: '',
      userInfo: {},
      // vform插件需要
      globalDsv: {
        vueAppBaseApi: process.env.VUE_APP_BASE_API,
        userToken: getToken(),
        dictApi: '/disease/dict/data/dictCode/'
      },
      // 右侧tab
      tableNames: [],
      displayFieldList: [],

      userList: [],
      dialogVisible: false,
      tipText: '已经是最后一个患者了，3秒后返回列表！',
      timer: null,
      toExamineInfo: '',
      cardBaseLineList: [],
      cardInfo: {},
      buttonType: 1,
      currentCardDataValue: {},
      userCurrentIndex: -1,
      isBaseLineRequest: false,
      tagsList: []
    }
  },

  computed: {
    ...mapGetters(["currentSelectedDisease"]),
    hasToExamineInfo() {
      return (item) => {
        let obj = this.cardBaseLineList.find(f => f.cardKey == item.id)
        if ((obj && obj.reviewer && obj.reviewTime) || item.options.toExamineInfo) {
          return true
        } else {
          return false
        }
      }
    },
    handelTagLabel() {
      return (val) => {
        return this.dict.type.sd_researchQueue_used.find(item => item.value == val)?.label || ''
      }
    },
    getSex() {
      return (val) => {
        return this.dict.type.sd_dict_sex.find(
          (item) => item.value == val
        )?.label;
      }
    }
  },
  watch: {
    cardBaseLineList(val) {
      this.updateAuditInfo(val)
    }
  },
  mounted() {
    this.formKey = this.$route.query.formKey || "";
    this.type = this.$route.query.type || 'add';
    this.rgId = this.$route.query.rgId || '';
    this.rsId = this.$route.query.rsId || '';

    if (this.type == 'edit') {
      this.empi = sessionStorage.getItem('studyCohortEditEmpi') || ''
      this.userList = sessionStorage.getItem('studyCohortEditData') ? JSON.parse(sessionStorage.getItem('studyCohortEditData')) : []
      // let currentObj = this.userList.find(item => item.empi == this.empi)
      this.encountId = this.$route.query.encountId || '';
      // this.baseLine = currentObj.baseLine || '';
    }

    this.formKey && this.getTemplate()

    this.$nextTick(() => {
      let bodyWidth = $('body').width()
      let menuWidth = $('.sidebar-container').width()
      let leftWidth = $('.sidebar-container').width()
      this.maxWidth = this.rightWidth = (bodyWidth - menuWidth - leftWidth - 50) / 2
    })
  },
  methods: {
    // 页面拖拽布局相关
    initLeftResize(event) {
      this.isResizingLeft = true;
      document.addEventListener('mousemove', this.resizeLeft);
      document.addEventListener('mouseup', this.stopResize);
    },
    initRightResize(event) {
      this.isResizingRight = true;
      this.startX = event.clientX;
      document.addEventListener('mousemove', this.resizeRight);
      document.addEventListener('mouseup', this.stopResize);
    },
    resizeLeft(event) {
      if (this.isResizingLeft) {
        let menuWidth = $('.sidebar-container').width() + 17
        const newWidth = event.clientX - menuWidth;
        if (newWidth >= 0 && newWidth <= 220) {
          this.leftWidth = newWidth;
        }
      }
    },
    resizeRight(event) {
      if (this.isResizingRight) {
        let bodyWidth = $('body').width()
        const newWidth = bodyWidth - event.clientX - 19
        if (newWidth > 0 && newWidth <= this.maxWidth + (this.maxWidth / 2)) {
          this.rightWidth = newWidth;
        }
      }
    },
    stopResize() {
      this.isResizingLeft = false;
      this.isResizingRight = false;
      document.removeEventListener('mousemove', this.resizeLeft);
      document.removeEventListener('mousemove', this.resizeRight);
      document.removeEventListener('mouseup', this.stopResize);
    },
    // 获取表单json数据
    getTemplate() {
      this.loading = true
      getFormTemplate({ formKey: this.formKey })
        .then(res => {
          this.formJson = JSON.parse(res.data.templateJson);
          if (this.formJson.widgetList) {
            this.onFormItem(this.formJson.widgetList[0])
          }
          if (this.empi) {
            this.getAuditData()
            this.getFormData()
          } else {
            this.loading = false
          }

        })
    },
    // 获取表单字段数据
    getFormData() {
      let params = {
        rgId: this.rgId,
        empi: this.empi,
        encountId: this.encountId
      }
      getSdResearchQueueDataDetail(params)
        .then(res => {
          let formData = res.data;
          this.formData = formData
          this.oldFormData = JSON.parse(JSON.stringify(res.data));
          this.userInfo = {
            address: formData.address,
            age: formData.age,
            del_flag: formData.del_flag,
            birthday: formData.birthday,
            create_time: formData.create_time,
            disease_syscode: formData.disease_syscode,
            gender: formData.gender,
            id_card: formData.id_card,
            id_type: formData.id_type,
            nationality: formData.nationality,
            native_province: formData.native_province,
            pat_name: formData.pat_name,
            phone: formData.phone,
            selfQueue: formData.selfQueue,
          }
          this.tagsList = formData.otherQueue ? formData.otherQueue.split(',') : []
          this.formatDate()
          this.$refs.vFormRef.setFormData(this.formData);
        }).finally((d) => {
          this.loading = false
        })
    },
    // 获取审核信息
    getAuditData() {
      let cardKey = this.cardInfo.id
      let params = {
        formKey: this.formKey,
        empi: this.empi,
        diseaseSyscode: this.currentSelectedDisease.diseaseSyscode,
        encountId: this.encountId || null,
        cardBaseLineList: [
          // {
          //     cardKey: cardKey,  /** 卡片唯一标识 */
          //     tableName: this.tableNames[0] || null  /** 卡片内对应的栅格第一行第一个绑定的表名 */
          // }
        ]
      }
      selectFormBaseLineData(params).then(res => {
        if (res && res.data) {
          this.admitNo = res.data.admitNo
          this.baseLine = res.data.baseLine
          this.baseLineToNowDays = res.data.baseLineToNowDays
          this.cardBaseLineList = res.data.cardBaseLineList
          if (this.buttonType == 2) {
            this.formJson.widgetList.forEach(item => {
              let obj = this.cardBaseLineList.find(f => f.cardKey == item.id)
              if (obj && item.id == cardKey) {
                if (obj.reviewer && obj.reviewTime) {
                  item.options.toExamineInfo = `${obj.reviewer} ${obj.reviewTime}`
                }
              }
            })

            this.vFormData = {
              formConfig: this.formJson.formConfig,
              widgetList: this.formJson.widgetList.filter(f => f.id == cardKey)
            }
          }
        }
      }).finally(() => {
        if (!this.isBaseLineRequest) {
          this.isBaseLineRequest = true
          this.$refs.referenceInfoRef?.getData({ cardKey: this.cardInfo.id, empi: this.empi, formKey: this.formKey, encountId: this.encountId || '', baseLine: this.baseLine || '', baseLineToNowDays: this.baseLineToNowDays || '' })
        }
      })
    },
    // 左侧
    onFormItem(row) {
      if (this.$refs.referenceInfoRef?.loading) return this.$message.warning('右侧数据请求中，请稍后！')
      if (this.activeName == row.options.name) return
      this.cardInfo = row
      this.$refs.vFormRef.getFormData().then(data => {
        // console.log(this.removeUndefined(data), '上一表单的数据');
        this.getChangeData(this.removeUndefined(data))
        this.formData = { ...this.formData, ...this.removeUndefined(data) }
        this.tableNames = []
        this.displayFieldList = []
        this.activeName = row.options.name
        this.updateAuditInfo(this.cardBaseLineList)
        this.recursiveFn(row.widgetList);
        if (this.isBaseLineRequest) {
          this.$refs.referenceInfoRef?.getData({ cardKey: row.id, empi: this.empi, formKey: this.formKey, encountId: this.encountId || '', baseLine: this.baseLine || '', baseLineToNowDays: this.baseLineToNowDays || '' })
        }
        // AI智能提取打标记
        this.$refs.aiStructuredRef?.updateFormField()
      })
    },
    // 更新审核信息
    updateAuditInfo(list) {
      this.formJson.widgetList.forEach(item => {
        let obj = list.find(f => f.cardKey == item.id)
        if (obj && item.id == this.cardInfo.id) {
          if (obj.reviewer && obj.reviewTime) {
            item.options.toExamineInfo = `${obj.reviewer} ${obj.reviewTime}`
          }
        }
      })

      this.vFormData = {
        formConfig: this.formJson.formConfig,
        widgetList: this.formJson.widgetList.filter(f => f.id == this.cardInfo.id)
      }
      this.$refs.vFormRef.setFormJson(this.vFormData);
      this.formatDate()
      this.$refs.vFormRef.setFormData(this.formData);
    },
    // 时间格式化
    formatDate() {
      for (let k in this.formData) {
        let fieldJson = this.$refs.vFormRef.getWidgetRef(k)
        if (fieldJson && fieldJson.field && fieldJson.field.type == 'date') {
          let valueFormat = 'YYYY-MM-DD'
          if (fieldJson.field.options.valueFormat == 'yyyy-MM-dd HH:mm:ss') {
            valueFormat = 'YYYY-MM-DD HH:mm:ss'
          }
          this.formData[k] = this.formData[k] ? moment(this.formData[k]).format(valueFormat) : null
          this.oldFormData[k] = this.formData[k]
        }
      }
    },
    // 递归循环当前card下面所有字段组件, 获取全部表名(过滤后的)
    recursiveFn(arr) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].options.fieldName && arr[i].options.fieldName.length > 1) {
          if (this.tableNames.indexOf(arr[i].options.fieldName[0]) == -1) {
            this.tableNames.push(arr[i].options.fieldName[0])
          }
          // 获取字段
          if (this.displayFieldList.indexOf(arr[i].options.fieldName[1]) == -1) {
            this.displayFieldList.push(arr[i].options.fieldName[1])
          }
        } else if (arr[i].widgetList) {
          this.recursiveFn(arr[i].widgetList)
        } else if (arr[i].cols && arr[i].cols) {
          this.recursiveFn(arr[i].cols)
        }
      }
    },
    // 去除对象中值为undefined
    removeUndefined(obj) {
      let data = JSON.parse(JSON.stringify(obj))
      let isObject = (tem) => {
        return Object.prototype.toString.call(tem) == '[object Object]'
      }
      for (let k in data) {
        if (isObject(data[k])) {
          if (Object.keys(data[k]).length == 0) {
            delete data[k]
          }
        } else if (Array.isArray(data[k])) {
          let fieldJson = this.$refs.vFormRef.getWidgetRef(k)
          let options = fieldJson?.field || { type: 'field' }
          if (options.type != "file-upload") {
            if (data[k].length == 0) {
              delete data[k]
            }
          }
        } else {
          if (data[k] == undefined) {
            delete data[k]
          }
        }
      }
      return data
    },
    // 获取修改过的数据对象集合
    getChangeData(formData) {
      // console.log(formData,'formData')
      this.currentCardDataValue = formData
      // 先删除当前表单修改过的数据再重新获取，主要解决重置后数据任然存在的问题
      for (let c in this.changeData) {
        if (c == this.activeName) {
          delete this.changeData[c]
          break
        }
      }

      let oldFormData = this.oldFormData;
      //表单所有容器
      let allContainer = this.$refs.vFormRef.getContainerWidgets();
      //获取表单中所有字段的json
      let allFormFields = this.$refs.vFormRef.getFieldWidgets();
      let changeFieldObj = {}; //记录修改的数据对象集合
      for (let k in formData) {
        //如果是子表单, 多行子表单为数组结构单独处理
        if (Array.isArray(formData[k]) && k.indexOf('subform') != -1) {
          let fields = [];
          //循环子表单数组
          formData[k].forEach((item, index) => {
            //如果字表单的数据索引大于老的数据, 就是新增的
            if (oldFormData[k] && oldFormData[k].length > 0) {
              if (index > oldFormData[k].length - 1) {
                for (let itemK in item) {
                  let fieldJson = allFormFields.filter(item => item.name == itemK)[0];
                  if (!fieldJson || !fieldJson.field) break
                  let fieldOption = fieldJson.field.options;
                  let obj = {
                    dsEnabled: fieldOption.dsEnabled,
                  };
                  // if (fieldOption.dict && fieldOption.dsEnabled) {
                  //   this.getDictData(fieldOption.dict);
                  //   obj.dict = fieldOption.dict
                  // } else 
                  if (fieldOption.optionItems && fieldOption.optionItems.length > 0) {
                    obj.optionItems = fieldOption.optionItems
                  }
                  let fieldName = itemK.split('_and_');
                  if (item[itemK]) {
                    fields.push({
                      updateTableName: fieldName.length > 1 ? fieldName[0] : '', //表名
                      updateFieldName: fieldName.length > 1 ? fieldName[1] : fieldName[0], //字段名
                      updateFieldLabel: fieldOption.label, //字段label
                      updateBeforeValue: '', //原始值
                      updateAfterValue: item[itemK], //新值
                      updateCause: '', //修改原因
                      ...obj
                    })
                  }
                }
              } else {
                // 否则就去比较当前这条数据里面的每个字段的值是否修改过
                for (let itemK in item) {
                  let fieldJson = allFormFields.filter(item => item.name == itemK)[0];
                  if (!fieldJson || !fieldJson.field) break
                  let fieldOption = fieldJson.field.options;
                  let obj = {
                    dsEnabled: fieldOption.dsEnabled,
                  };
                  // if (fieldOption.dict && fieldOption.dsEnabled) {
                  //   this.getDictData(fieldOption.dict);
                  //   obj.dict = fieldOption.dict
                  // } else 
                  // if (fieldOption.optionItems && fieldOption.optionItems.length > 0) {
                  //   obj.optionItems = fieldOption.optionItems
                  // }
                  let fieldName = itemK.split('_and_');
                  if (item[itemK] != oldFormData[k][index][itemK]) {
                    fields.push({
                      updateTableName: fieldName.length > 1 ? fieldName[0] : '', //表名
                      updateFieldName: fieldName.length > 1 ? fieldName[1] : fieldName[0], //字段名
                      updateFieldLabel: fieldOption.label, //字段label
                      updateBeforeValue: oldFormData[k][index][itemK], //原始值
                      updateAfterValue: item[itemK], //新值
                      updateCause: '', //修改原因
                      ...obj
                    })
                  }
                }
              }
            } else {
              for (let itemK in item) {
                let fieldJson = allFormFields.filter(item => item.name == itemK)[0];
                if (!fieldJson || !fieldJson.field) break
                let fieldOption = fieldJson.field.options;
                let obj = {
                  dsEnabled: fieldOption.dsEnabled,
                };
                // if (fieldOption.dict && fieldOption.dsEnabled) {
                //   this.getDictData(fieldOption.dict);
                //   obj.dict = fieldOption.dict
                // } else 
                if (fieldOption.optionItems && fieldOption.optionItems.length > 0) {
                  obj.optionItems = fieldOption.optionItems
                }
                let fieldName = itemK.split('_and_');
                if (item[itemK]) {
                  fields.push({
                    updateTableName: fieldName.length > 1 ? fieldName[0] : '', //表名
                    updateFieldName: fieldName.length > 1 ? fieldName[1] : fieldName[0], //字段名
                    updateFieldLabel: fieldOption.label, //字段label
                    updateBeforeValue: '', //原始值
                    updateAfterValue: item[itemK], //新值
                    updateCause: '', //修改原因
                    ...obj
                  })
                }
              }
            }
          })
          //如果修改的字段数大于0
          if (fields.length > 0) {
            changeFieldObj[k] = fields;
          }
        } else {
          let fieldName = k.split('_and_');
          let newFieldValue = formData[k];
          let oldFieldValue = oldFormData[k];
          let fieldJson = this.$refs.vFormRef.getWidgetRef(k);
          if (!fieldJson || !fieldJson.field) break
          let fieldOption = fieldJson.field.options;
          if (Array.isArray(newFieldValue) && Array.isArray(oldFieldValue) && newFieldValue.join(',') === oldFieldValue.join(',')) {
            break;
          }
          if ((oldFieldValue != undefined && oldFieldValue != newFieldValue) || (oldFieldValue == undefined && newFieldValue && newFieldValue.length != 0)) {
            let obj = {
              dsEnabled: fieldOption.dsEnabled || '',
            };
            // if (fieldOption.dict && fieldOption.dsEnabled) {
            //   this.getDictData(fieldOption.dict);
            //   obj.dict = fieldOption.dict
            // } else 
            if (fieldOption.optionItems && fieldOption.optionItems.length > 0) {
              obj.optionItems = fieldOption.optionItems
            }
            changeFieldObj[k] = {
              updateTableName: fieldName.length > 1 ? fieldName[0] : '', //表名
              updateFieldName: fieldName.length > 1 ? fieldName[1] : fieldName[0], //字段名
              updateFieldLabel: fieldOption.label, //字段label
              updateBeforeValue: oldFieldValue, //原始值
              updateAfterValue: newFieldValue, //新值
              updateCause: '', //修改原因
              ...obj
            }
          }
        }
      }
      /**
       * 对于修改的字段进行分组, 以Card进行分组, 不在card容器的字段统一放在custom.fields
       */
      let changeData = {
        // custom: {
        //   card: '',
        //   fields: [],
        // }
      }
      for (let k in changeFieldObj) {
        allContainer.forEach(item => {
          if (['card', 'baseLine', 'visits'].includes(item.type)) {
            if (item.container.widgetList && item.container.widgetList.length > 0) {
              recursiveFn(item.container.widgetList)
            }
            function recursiveFn(arr) {
              for (let i = 0; i < arr.length; i++) {
                if (arr[i].options.name == k) {
                  //如果是数组说明是子表单活多行子表单
                  if (Array.isArray(changeFieldObj[k])) {
                    if (changeData[item.container.options.name]) {
                      changeData[item.container.options.name].card = item.container.options.label;
                      changeData[item.container.options.name].fields = changeFieldObj[k];
                    } else {
                      changeData[item.container.options.name] = {
                        card: item.container.options.label,
                        fields: changeFieldObj[k]
                      }
                    }
                    //否则就是单字段 嵌套在card容器里面
                  } else {
                    if (changeData[item.container.options.name]) {
                      changeData[item.container.options.name].card = item.container.options.label;
                      changeData[item.container.options.name].fields.push(changeFieldObj[k])
                    } else {
                      if (changeFieldObj[k]) {
                        changeData[item.container.options.name] = {
                          card: item.container.options.label,
                          fields: [changeFieldObj[k]]
                        }
                      }
                    }
                  }
                  //已经取过值的将其删除, 剩余的字段就是不在card容器里面的
                  delete changeFieldObj[k]
                  break
                } else if (arr[i].widgetList) {
                  recursiveFn(arr[i].widgetList)
                } else if (arr[i].cols) {
                  recursiveFn(arr[i].cols)
                }
              }
            }
          }
        })
      }
      //将剩余未分组的字段统一放在custom.fields里面
      for (let k in changeFieldObj) {
        if (Array.isArray(changeFieldObj[k])) {
          this.changeData.custom.fields = [...this.changeData.custom.fields, ...changeFieldObj[k]]
        } else {
          this.changeData.custom.fields.push(changeFieldObj[k])
        }
      }
      this.changeData = { ...this.changeData, ...changeData }
      // console.log(this.changeData,'this.changeData');
    },
    //获取数据字典
    getDictData(dictCode) {
      if (!this.dictList[dictCode]) {
        this.getDicts(dictCode).then(res => {
          res.data.forEach(item => {
            item.label = item.dictLabel;
            item.value = item.dictValue;
            item.raw = { listClass: null };
          });
          this.$set(this.dictList, dictCode, res.data);
        });
      }
    },
    // 更新表单数据
    updateFormData(data) {
      this.$refs.vFormRef.setFormData(Object.assign({}, this.formData, data))
    },
    onPageClose() {
      const obj = { path: "/casegroup/studied", query: { rsId: this.rsId, rgId: this.rgId } };
      this.$tab.closeOpenPage(obj);
    },
    onReviewAndSubmit(temp) {
      let params = {
        empi: this.empi,
        rgId: this.rgId,
        encountId: this.encountId,
        auditEndStatus: '1'
      }
      alterAuditForDataStatus(params).then(res => {
        let params2 = {
          empi: temp.empi,
          encountId: this.encountId,
          queueUpdateLogs: temp.queueUpdateLogs,
          rgId: temp.rgId,
        }
        // 无变更传所有
        if (!params2.queueUpdateLogs.fields.length) {
          params2.queueUpdateLogs.fields = this.getCardValue()
        }
        // console.log(params, 'params');
        saveAuditedData(params2).then(res => {
          this.$message.success('审核提交成功')
          this.getAuditData()
          if (this.buttonType == 3) {
            this.onReviewAfter()
          }

          this.onSave(1, true)

        })
      })
    },
    // 获取当前卡片的值
    getCardValue() {
      let fields = []
      for (let k in this.currentCardDataValue) {
        let fieldName = k.split('_and_');
        let fieldJson = this.$refs.vFormRef.getWidgetRef(k)
        let fieldOption = fieldJson?.field?.options;

        fields.push({
          card: this.cardInfo.id,
          cardLabel: this.cardInfo.options.label,
          updateTableName: fieldName.length > 1 ? fieldName[0] : '', //表名
          updateFieldName: fieldName.length > 1 ? fieldName[1] : fieldName[0], //字段名
          updateFieldLabel: fieldOption ? fieldOption.label : '',
          updateBeforeValue: Array.isArray(this.currentCardDataValue[k]) ? null : this.currentCardDataValue[k],
          updateAfterValue: Array.isArray(this.currentCardDataValue[k]) ? null : this.currentCardDataValue[k],
          updateCause: "",
          dsEnabled: ""
        })
      }

      return fields
    },
    // 审核后继续
    onReviewAfter() {
      let currentIndex = this.formJson.widgetList.findIndex(item => this.activeName == item.options.name)
      if (this.formJson.widgetList[currentIndex + 1]) {
        this.onFormItem(this.formJson.widgetList[currentIndex + 1])
      }
    },
    // 保存
    onSave(type, noDialog=false) {
      this.buttonType = type
      this.$refs.vFormRef.getFormData().then(data => {
        this.formData = { ...this.formData, ...this.removeUndefined(data) }
        if (this.type == 'add') {
          this.onSubmit()
        } else {
          if (!noDialog) {
            this.getChangeData(this.removeUndefined(data))
            for (let key in this.changeData) {
              if (this.changeData[key].fields && this.changeData[key].fields.length > 0) {
                this.$refs.updateDialogRef.show(this.changeData)
                return false;
              }
            }
          }
          console.log('没有改变');
          this.onSubmit({}, noDialog)
        }

      })
    },
    // 提交
    onSubmit(changeForm = {}, noDialog) {
      let formData = this.formData
      let params = {
        rgId: this.rgId,
        empi: this.empi
      }
      let userOldInfo = null;
      if (this.type == 'add') {
        let userInfo = {
          address: '',
          age: '',
          del_flag: '',
          birthday: '',
          create_time: '',
          disease_syscode: '',
          gender: '',
          id_card: '',
          id_type: '',
          nationality: '',
          native_province: '',
          pat_name: '',
          phone: ''
        }
        for (let k in formData) {
          if (typeof formData[k] == 'array') {
            formData[k].forEach(row => {
              for (let row_k in row) {
                let fieldNames = row_k.split('_and_');
                if (fieldNames.length > 1) {
                  if (fieldNames[0] == 'sd_patient_info' && userInfo[fieldNames[1]] != undefined && !userInfo[fieldNames[1]]) {
                    userInfo[fieldNames[1]] = row[row_k]
                  }
                }
              }
            })
          } else {
            let fieldNames = k.split('_and_');
            if (fieldNames.length > 1) {
              if (fieldNames[0] == 'sd_patient_info' && userInfo[fieldNames[1]] != undefined && !userInfo[fieldNames[1]]) {
                userInfo[fieldNames[1]] = formData[k]
              }
            }
          }
        }
        userOldInfo = userInfo;
        params.idCardNo = userOldInfo.id_card;
        params.phoneNumber = userOldInfo.phone;
      } else {
        let basicInfo = [
          'sd_patient_info_and_pat_name',
          'sd_patient_info_and_gender',
          'sd_patient_info_and_age',
          'sd_patient_info_and_phone',
          'sd_patient_info_and_birthday',
          'sd_patient_info_and_id_type',
          'sd_patient_info_and_id_card',
          'sd_patient_info_and_address'
        ]
        basicInfo.forEach(item => {
          if (formData[item] == undefined) {
            formData[item] = this.oldFormData[item];
          }
        })
        userOldInfo = this.userInfo;
        //将修改的字段组装成需要的格式
        let queueUpdateLogs = {
          empi: this.empi,
          encountId: this.encountId,
          formId: this.formKey,
          updateExplain: changeForm.updateExplain,
          fields: []
        }
        let changeFieldObj = changeForm.changeFieldObj;
        for (let key in changeFieldObj) {
          if (changeFieldObj[key].fields && changeFieldObj[key].fields.length > 0) {
            changeFieldObj[key].fields.forEach(item => {
              if (Array.isArray(item.updateAfterValue)) {
                item.updateAfterValue = item.updateAfterValue.join(',')
              }
              if (Array.isArray(item.updateBeforeValue)) {
                item.updateBeforeValue = item.updateBeforeValue.join(',')
              }
              let obj = {
                card: key,
                cardLabel: changeFieldObj[key].card || '',
                ...item
              }
              queueUpdateLogs.fields.push(obj);
            })
          }
        }
        params.queueUpdateLogs = queueUpdateLogs;
      }
      params.dataJson = JSON.stringify({ ...formData, ...userOldInfo });
      params.encountId = this.encountId || null
      this.$refs.updateDialogRef.dialogVisible = false
      if (this.buttonType == 1) {
        if (this.type == 'add') {
          addFormForMongo(params)
            .then(res => {
              this.loading = false;
              if (!noDialog) {
                this.$message.success('添加成功!');
                this.onPageClose()
              }
              this.oldFormData = this.formData
            })
        } else {
          saveFormForMongo(params)
            .then(res => {
              this.loading = false;
              if (!noDialog) {
                this.$message.success('保存成功!');
                this.onPageClose()
              }
              this.oldFormData = this.formData
            })
        }
      } else {
        this.onReviewAndSubmit(params)
      }
    },
    // 审核完成
    onAuditCompleted(type) {
      let params = {
        empi: this.empi,
        rgId: this.rgId,
        encountId: this.encountId,
        auditEndStatus: '2'
      }
      alterAuditForDataStatus(params).then(res => {
        if (type == 2) {
          this.onNext()
        } else {
          this.onPageClose()
        }
      })
    },
    // 提交后下一位
    onNext() {
      this.userCurrentIndex = this.userList.findIndex(item => item.empi == this.empi)
      let nextObj = this.userList[this.userCurrentIndex + 1]
      if (!nextObj) {
        this.dialogVisible = true
        let time = 3
        this.timer = setInterval(() => {
          time--
          if (time <= 0) {
            this.onBack()
          }
          this.tipText = `已经是最后一个患者了，${time}秒后返回列表！`
        }, 1000);
        return
      }
      sessionStorage.setItem('studyCohortEditEmpi', nextObj.empi)
      this.empi = nextObj.empi
      this.encountId = nextObj.encountId
      // this.baseLine = nextObj.baseLine
      this.activeName = ''
      this.formData = {}
      this.formJson = {}
      this.getTemplate()
    },
    onClose() {
      this.dialogVisible = false
      this.tipText = `已经是最后一个患者了，3秒后返回列表！`
      clearInterval(this.timer)
    },
    onBack() {
      this.onClose()
      setTimeout(() => {
        this.onPageClose()
      }, 50)
    },
    // 跳转患者360
    onGoPage(row) {
      this.$jumpPatient360({ path: "/cdrView", query: { empi: this.empi } }, this.$route.query)
    },
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f7f7f7;

  ::v-deep .el-card {
    // border: 0;
  }

  ::v-deep .el-card__header {
    // display: none;
    // background-color: #F2F8FF;
    // border: 0;

    .clear-fix {
      display: flex;
      align-items: center;
      // padding-left: 24px;
      position: relative;
      // font-size: 15px;
    }

    .float-right {
      position: absolute;
      right: 0;
      cursor: pointer;
    }

    .el-icon-arrow-down {
      font-size: 16px !important;
    }
  }
}

.header {
  height: 60px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .user-box {
    .user-icon {
      display: inline-block;
      width: 26px;
      cursor: pointer;
      margin-right: 12px;
    }

    .user-item {
      font-size: 14px;
      margin-right: 12px;

      .label {
        color: #333;
      }

      .value {
        color: #666;
        font-weight: bold;
      }
    }

    .pat-name {
      font-size: 18px;
      font-weight: bold;
    }
  }
}

.app-container {
  flex: 1;
  width: 100%;
  display: flex;
  padding: 12px !important;
  position: relative;
  overflow: hidden;

  .form-handel {
    position: absolute;
    padding: 4px;
    top: 10px;
    left: 0;
    color: #333;
    z-index: 999;
    background: #fff;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);

    .el-icon-s-unfold {
      font-size: 22px;
      cursor: pointer;

      &:hover {
        color: #1890ff;
      }
    }
  }

  .left-box {
    height: 100%;
    padding: 12px 0;
    background: #fff;
    // margin-right: 10px;
    position: relative;
    overflow: hidden;

    .el-icon-s-fold {
      position: absolute;
      color: #999;
      right: 12px;
      font-size: 20px;
      cursor: pointer;

      &:hover {
        color: #1890ff;
      }
    }

    .form-title {
      font-size: 15px;
      line-height: 20px;
      padding: 0 12px;
      color: #333;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .form-list {
      height: calc(100% - 32px);
      overflow-y: auto;

      .form-item {
        color: #333;
        font-size: 15px;
        cursor: pointer;
        padding: 6px 10px;
        margin-bottom: 4px;
        display: flex;
        align-items: center;

        .to-examine-icon {
          display: inline-block;
          width: 6px;
          height: 6px;
          background: #ccc;
          border-radius: 50%;
          margin-right: 6px;

          &.ing {
            background: #00ff7f;
          }
        }

        &.active {
          color: #1890ff;
          background: #F2F8FF;
        }

        &:hover {
          // color: #1890ff;
          background: #F2F8FF;
        }
      }
    }
  }

  .center-box {
    flex: 1;
    height: 100%;
    background: #fff;
    padding: 12px;
    // margin-right: 10px;
    position: relative;
    // overflow: auto;


    .v-form-box {
      height: calc(100% - 44px);
      overflow-y: auto;
      margin-bottom: 12px;
    }

    .btn-box {
      width: 100%;
      display: flex;
      justify-content: center;
    }

    ::v-deep .card-container {
      overflow: auto !important;
    }
  }

  .drag-icon {
    min-width: 12px;
    height: 50px;
    padding: 12px 2px;
    border: 1px solid #e6e6e6;
    border-radius: 10px;
    cursor: ew-resize;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    background: #fff;
    z-index: 100;

    // background-image: url('~@/assets/images/disease/drag-icon1.png') !important;
    // background-position: -6px 10px !important;
    // background-repeat: no-repeat !important;
    .drag-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 1px;

      .drag-line {
        width: 2px;
        height: 2px;
        background: #ccc;
        border-radius: 50%;
      }
    }

    &:hover {
      background: #F2F8FF;
      box-shadow: 0px 0px 10px rgba(0, 0, 0, .1);

      .drag-line {
        background: #1890ff;
      }
    }
  }

  .right-box {
    height: 100%;
    padding: 12px;
    background: #fff;
    position: relative;
    overflow: hidden;
  }
}
</style>
