<template>
    <div class="app-container studyCohortDetail-page">
        <div class="p-head-box" v-if="showSearch">
            <div class="fr-head">
                <el-form :model="searchForm" ref="form" label-width="72px" :inline="true" size="small">
                    <el-form-item label="切换队列">
                        <div class="sel-box">
                            <el-popover popper-class="pop-subject" placement="bottom" width="420" trigger="click"
                                @show="getSubjectAndGroupTree(false)" v-model="isShowPopper">
                                <el-table :data="subjectAndGroupTreeList" height="350px">
                                    <el-table-column width="150" property="subjectName" label="科研课题" align="center">
                                        <template slot-scope="scope">
                                            <div class="subject-item">
                                                <i class="el-icon-caret-right fr-icon"></i>
                                                {{scope.row.subjectName}}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="研究队列" align="center">
                                        <template slot-scope="scope">
                                            <div class="group-box"
                                                v-if="scope.row.groupList && scope.row.groupList.length > 0">
                                                <span class="group-item" :class="rgId == item.rgId ? 'active' : ''"
                                                    @click="selectGroup(scope.row, item.rgId, item.groupName, item.formCode)"
                                                    v-for="(item, index) in scope.row.groupList">
                                                    <el-tooltip class="item" effect="dark"
                                                        :content="`${item.groupName}(${item.researchQueueDataCount || 0})`"
                                                        placement="top-start">
                                                        <span>
                                                            {{item.groupName.length > 5 ?
                                                            item.groupName.substr(0,5)+'...' : item.groupName}}
                                                            ({{item.researchQueueDataCount || 0}})
                                                        </span>
                                                    </el-tooltip>
                                                </span>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div class="sel-div" slot="reference">
                                    <i class="arr-icon el-icon-arrow-down"></i>
                                    <div class="subject-text">{{selectGroupText}}</div>
                                </div>
                            </el-popover>
                        </div>
                    </el-form-item>
                    <el-form-item label="审核状态">
                        <el-select v-model="searchForm.auditEndStatus">
                            <el-option v-for="item in auditEndStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <div v-if="formCode" v-for="(row, rowIndex) in searchForm.fieldList" :key="rowIndex">
                        <el-form-item label="字段">
                            <el-select v-model="row.fieldName" @change="changeFieldName($event, rowIndex)">
                                <el-option :label="item.fieldLabel" :value="item.fieldName" :disabled="item.disabled"
                                    v-for="(item, index) in searchFieldList" :key="index"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="运算符">
                            <el-select v-model="row.condition" placeholder="" clearable filterable>
                                <el-option v-for="item in operativeSymbolList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>

                        </el-form-item>

                        <el-form-item label="参数" v-if="formCode">
                            <el-select class="set-width" v-if="row.fieldName && dictList[row.fieldName+'_dictCode']"
                                v-model="row.fieldValue"
                                :disabled="row.condition == 'empty' || row.condition == 'notEmpty'">
                                <el-option :label="item.label" :value="item.value"
                                    v-for="(item, index) in dictList[row.fieldName+'_dictCode']"
                                    :key="index"></el-option>
                            </el-select>
                            <el-date-picker unlink-panels  class="set-width" v-else-if="row.type == 'date'" v-model="row.fieldValue"
                                value-format="yyyy-MM-dd" type="date"
                                :disabled="row.condition == 'empty' || row.condition == 'notEmpty'" placeholder="选择日期">
                            </el-date-picker>
                            <el-input class="set-width" v-else
                                :disabled="row.condition == 'empty' || row.condition == 'notEmpty'"
                                v-model="row.fieldValue" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-button type="danger" icon="el-icon-minus" circle size="mini"
                                v-if="searchForm.fieldList.length > 1" @click="delSearchLine(rowIndex)"></el-button>
                            <el-button type="primary" icon="el-icon-plus" circle size="mini"
                                v-if="rowIndex == searchForm.fieldList.length - 1" @click="addSearchLine"></el-button>
                        </el-form-item>
                    </div>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="small" @click="search">查询</el-button>
                        <el-button type="primary" plain icon="el-icon-search" size="small" @click="reset">重置</el-button>
                        <el-button type="success" icon="el-icon-plus" size="small"
                            @click="toFormPage('add')">病例注册</el-button>
                        <el-button type="success" icon="el-icon-download" size="small"
                            @click="exportTemplate()">导出模版</el-button>
                        <el-button type="success" icon="el-icon-upload" size="small"
                            @click="handleImport('队列数据导入')">导入数据</el-button>
                        <el-button type="warning" icon="el-icon-download" size="small"
                            @click="exportDialog = true" v-hasPermi="['disease:researchQueueData:desensitizationExport', 'disease:researchQueueData:nonDesensitizationExport']">导出申请</el-button>
                        <el-button type="primary" size="small" icon="el-icon-data-analysis" plain :disabled="list.length == 0"
                            @click="toStatis">统计分析</el-button>
                    </el-form-item>
                </el-form>
                <!-- <el-button type="primary" plain icon="el-icon-plus" size="small" @click="">韦恩图工具</el-button> -->
            </div>
        </div>
        <el-row :gutter="10" class="mb8">
            <div class="selectUserNum" v-if="userListAllSelections.length > 0">
                已选择<span class="user-nums">{{userListAllSelections.length}}</span>条数据
            </div>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" v-if="false"></right-toolbar>
        </el-row>

        <el-table v-if="searchFieldList && searchFieldList.length" ref="userTable" :data="list" border stripe @selection-change="selectUser" @sort-change="onSortChange">
            <el-table-column type="selection"></el-table-column>
            <template v-if="!isNewUser">
                <el-table-column label="患者姓名">
                    <template slot-scope="scope">
                        <div v-if="scope.row.isNewUser">{{scope.row.sd_patient_info_and_pat_name}}</div>
                        <el-link v-else type="primary" @click="view(scope.row)">{{ scope.row.sd_patient_info_and_pat_name
                            }}</el-link>
                    </template>
                </el-table-column>
                <el-table-column label="性别">
                    <template slot-scope="scope">
                        <dict-tag :options="dict.type.sd_dict_sex" :value="scope.row.sd_patient_info_and_gender" />
                    </template>
                </el-table-column>
                <el-table-column prop="sd_patient_info_and_age" label="年龄" sortable="custom" :min-width="80"></el-table-column>
                <el-table-column prop="sd_patient_info_and_phone" label="电话"></el-table-column>
                <el-table-column show-overflow-tooltip prop="sd_patient_info_and_birthday" label="出生日期" sortable="custom" :min-width="100"></el-table-column>
                <el-table-column show-overflow-tooltip label="证件类型">
                    <template slot-scope="scope">
                        <dict-tag :options="dict.type.sd_id_type" :value="scope.row.sd_patient_info_and_id_type" />
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip prop="sd_patient_info_and_id_card" label="证件号"></el-table-column>
                <el-table-column show-overflow-tooltip prop="sd_patient_info_and_address" label="地址"></el-table-column>
            </template>
            <template v-else>
                <el-table-column :label="item.fieldLabel" v-for="(item,index) in this.searchFieldList.slice(0,8)" :key="index">
                    <template slot-scope="scope">
                        <el-link v-if="item.fieldLabel.includes('姓名')" type="primary" @click="view(scope.row)">{{scope.row[item.fieldName]}}</el-link>
                        <span v-else-if="item.optionItems && item.optionItems.length">{{ getLabel(item.optionItems, scope.row[item.fieldName]) }}</span>
                        <span v-else>{{scope.row[item.fieldName]}}</span>
                    </template>
                </el-table-column>
            </template>
            <el-table-column show-overflow-tooltip prop="baseLine" label="基线日期" sortable="custom" :min-width="100"></el-table-column>
            <el-table-column label="审核状态">
                <template slot-scope="scope">
                    <dict-tag :options="auditEndStatusOptions" :value="scope.row.auditEndStatus" />
                </template>
            </el-table-column>
            <el-table-column :label="col.fieldLabel" :prop="col.fieldName" v-for="(col, colIndex) in customColumn"
                :key="colIndex" :sortable="col.fieldLabel == '受试者编号' ? 'custom' : false">
                <template slot-scope="scope">
                    <div v-if="col.dict && col.dict.length > 0">
                        <dict-tag :options="col.dict" :value="scope.row[col.fieldName]" />
                    </div>
                    <div v-else>{{scope.row[col.fieldName]}}</div>
                </template>
            </el-table-column>

            <el-table-column label="操作" align="center" width="180" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-edit" size="small"
                        @click="toFormPage('edit', scope.row)">编辑信息</el-button>
                    <el-dropdown trigger="hover" placement="bottom">
                        <el-button type="text" size="small" style="margin-left: 4px;" icon="el-icon-arrow-down">更多</el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item>
                                <div @click="delUserItem(scope.row)">
                                    <i class="el-icon-delete"></i>
                                    <span>删除</span>
                                </div>
                            </el-dropdown-item>
                            <el-dropdown-item>
                                <div @click="toFormPage('edit', scope.row, 'ai')">
                                    <i class="el-icon-s-operation"></i>
                                    <span>AI智能提取</span>
                                </div>
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        <selectStatisticalType ref="selectStatisticalType"></selectStatisticalType>
        <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
            <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
                :action="upload.url" :disabled="upload.isUploading"
                :on-progress="handleFileUploadProgress"
                :on-success="handleFileSuccess"
                :auto-upload="false"
                :data={rgId:this.rgId}
                 drag>
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip text-center" slot="tip">
                    <span>仅允许导入xls、xlsx格式文件。</span>
                    <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                        @click="exportTemplate()">下载模板</el-link>
                </div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitFileForm">确 定</el-button>
                <el-button @click="upload.open = false">取 消</el-button>
            </div>
        </el-dialog>

        <el-dialog title="导出申请" :visible.sync="exportDialog" width="600px" append-to-body destroy-on-close>
            <div style="margin-top: 20px;">
                <span style="font-weight: 500;margin-right: 10px;">导出类型:</span>
                <el-radio-group v-model="desensitizationAuditObject">
                    <el-radio label="data_export" v-hasPermi="['disease:researchQueueData:desensitizationExport']">脱敏导出</el-radio>
                    <el-radio label="data_non_desensitization_export" v-hasPermi="['disease:researchQueueData:nonDesensitizationExport']">非脱敏导出</el-radio>
                </el-radio-group>
            </div>
            <div style="margin-top: 20px;">
                <span style="font-weight: 500;margin-right: 10px;">导出格式:</span>
                <el-radio-group v-model="exportType">
                    <el-radio label="1">csv</el-radio>
                    <el-radio label="2">excel</el-radio>
                </el-radio-group>
            </div>
            <div style="font-weight: 500;margin-top: 20px;margin-bottom: 10px;">附件上传:</div>
            <Upload ref="uploadRef"/>
            <div slot="footer">
                <el-button @click="exportDialog = false">取消</el-button>
                <el-button type="primary" @click="exportList">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {getSdResearchQueueData, getTopicList, getGrouplist, delFromMongo} from '@/api/disease/caseManage'
import { exportApplication } from '@/api/disease/casegroup'
import { listSubject } from '@/api/disease/subject';
import {mapGetters} from 'vuex'
import {getFormTemplate} from '@/api/form/form'
import selectStatisticalType from '../components/selectStatisticalType'
import { getToken } from "@/utils/auth";
import Upload from "../dataAssertManage/upload.vue";
export default {
    name: 'Studied',
    dicts: ["sd_dict_sex", "sd_id_type"],
    data() {
        return {
            rgId: '',
            subjectList: [], //课题列表
            experimentalGroupList: [], //实验组列表
            queryParams: {
                rsId: '',
                rgId: '',
                pageNum: 1,
                pageSize: 10,
                selectFields: []
            },
            showSearch: true,
            list: [],
            total: 0, //总条数
            isShowPopper: false,
            subjectAndGroupTreeList: [], //课题实验组数据
            selectGroupText: '', //选择的课题实验组名称
            formCode: '', //自定义表单code
            selectedUserList: [], //选中的用户数据
            userListAllSelections: [], //选中的总人数
            searchForm: {
                fieldList: [
                    {
                        fieldName: '',
                        condition: '',
                        fieldValue:''
                    }
                ]
            },
            searchFieldList: [],
            dictList: {}, //数据字典
            customColumn: [],
            operativeSymbolList: [
                {label: '大于', value: '>'},
                {label: '小于', value: '<'},
                {label: '大于等于', value: '>='},
                {label: '小于等于', value: '<='},
                {label: '等于', value: '='},
                {label: '不等于', value: '!='},
                {label: '包含', value: 'contains'},
                {label: '不包含', value: 'notContains'},
                {label: '为空', value: 'empty'},
                {label: '不为空', value: 'notEmpty'},
            ],
            isFirstLoading: true, //是否是第一次加载
            createStatisticsFileParams: {
                formCodeJson: '',
            }, //统计分析所需的字段参数
            // 导入参数
            upload: {
                // 是否显示弹出层（用户导入）
                open: false,
                // 弹出层标题（用户导入）
                title: "",
                // 是否禁用上传
                isUploading: false,
                // 是否更新已经存在的用户数据
                updateSupport: 0,
                // 设置上传的请求头部
                // 上传的地址
                url: process.env.VUE_APP_BASE_API + "/disease/group/import"
            },
            // 导出申请
            exportDialog: false,
            exportType: '1',
            desensitizationAuditObject: 'data_export',

            auditEndStatusOptions: [
                {label: '待审核', value: '0', raw: {listClass: 'primary'}},
                {label: '审核中', value: '1',  raw: {listClass: 'warning'}},
                {label: '审核完成', value: '2',  raw: {listClass: 'success'}},
            ],

            // 队列锁定状态
            lockStatus: 0,
            // 排序
            sortList: [],
            isNewUser: false, // 是否外部患者
        }
    },
    components: {
        selectStatisticalType,
        Upload
    },
    computed: {
		...mapGetters(['currentSelectedDisease']),
        getLabel() {
            return (options, val) => {
                let label = '';
                options.forEach(item => {
                    if(item.value == val) {
                        label = item.label;
                    }
                })
                return label || val;
            }
        }
	},
    watch: {
		// currentSelectedDisease: {
		// 	handler(newVal, oldVal) {
		// 		this.queryParams.rsId = '';
        //         this.queryParams.rgId = '';
        //         this.total = 0;
        //         this.list = [];
        //         this.selectGroupText = '';
        //         this.formCode = '';
        //         this.queryParams.pageNum = 1;
		// 		this.getSubjectAndGroupTree();
		// 	},
		// 	deep: true
        // },
        formCode(newVal, oldVal) {
            if(newVal) {
                this.getFormTemplateJson();
            }
        }
	},
    mounted() {
        this.upload.headers = { Authorization: "Bearer " + getToken(),diseaseSyscode: this.currentSelectedDisease.diseaseSyscode }
        console.log("打印this.currentSelectedDisease",this.currentSelectedDisease)
        let rgId = this.$route.query.rgId;
        let rsId = this.$route.query.rsId;
        this.rgId = rgId;
        this.queryParams.rgId = Number(rgId) || '';
        this.queryParams.rsId = Number(rsId) || '';
        this.getSubjectAndGroupTree();
        if(rgId && rsId) {
            this.$router.replace({query: {}})
        }

        setTimeout(_ => {
            this.isFirstLoading = false;
        },2000)

        // 导出添加权限后的默认值
        // 脱敏导出
        let desensitizationExport = 'disease:researchQueueData:desensitizationExport'
        // 非脱敏导出
        let nonDesensitizationExport = 'disease:researchQueueData:nonDesensitizationExport'
        if (!this.$auth.hasPermi(desensitizationExport) && this.$auth.hasPermi(nonDesensitizationExport)) {
            this.desensitizationAuditObject = 'data_non_desensitization_export'
        }
    },
    activated() {
        if(this.isFirstLoading) return false;
        let rgId = this.$route.query.rgId;
        let rsId = this.$route.query.rsId;
        if(rgId && rsId) {
            this.$router.replace({
                query: {}
            })
            this.rgId = rgId;
            this.queryParams.rgId = Number(rgId) || '';
            this.queryParams.rsId = Number(rsId) || '';
        }
        //每次进入都要重新获取队列 , 因为用户去编辑队列后 在返回到此页面需要重新加载数据才是最新的
        this.getSubjectAndGroupTree();
    },
    methods: {
        //获取课题实验组树
        getSubjectAndGroupTree(loadingList = true) {
            listSubject({diseaseSyscode: this.currentSelectedDisease.diseaseSyscode}).then(response => {
                let list = [];
                response.rows.forEach(item => {
                    if(item.groupList && item.groupList.length > 0) {
                        list.push(item);
                    }
                })
                if(this.rgId) {
                    list.forEach(subjectItem => {
                        subjectItem.groupList.forEach(groupItem => {
                            if(this.rgId == groupItem.rgId) {
                                this.lockStatus = groupItem.lockStatus
                                this.selectGroupText = `${subjectItem.subjectName}/${groupItem.groupName}`;
                                this.formCode = groupItem.formCode;
                                if(loadingList) {
                                    this.getList();
                                }
                            }
                        })
                    })
                } else if(list.length > 0){
                    this.rgId = list[0].groupList[0].rgId;
                    this.lockStatus = list[0].groupList[0].lockStatus
                    this.queryParams.rgId = this.rgId;
                    this.selectGroupText = `${list[0].subjectName}/${list[0].groupList[0].groupName}`;
                    this.formCode = list[0].groupList[0].formCode;
                    if(loadingList) {
                        this.getList();
                    }
                }
                this.subjectAndGroupTreeList = list;
            })
        },
        //选择实验组
        selectGroup(row, rgId, rgName, formCode = '') {
            if(rgId == this.rgId) return false;
            this.lockStatus = row.lockStatus
            this.rgId = rgId
            this.queryParams.rsId = row.rsId;
            this.queryParams.rgId = rgId;
            this.selectGroupText = `${row.subjectName}/${rgName}`;
            this.formCode = formCode;
            this.userListAllSelections = [];
            this.queryParams.pageNum = 1;
            this.getList();
            this.isShowPopper = false;
        },
        //获取研究队列列表
        getList() {
            this.$modal.loading();
            // let params = this.queryParams;
            // params.fieldList = JSON.stringify(this.searchForm.fieldList);
            this.queryParams.sortList = this.sortList
            getSdResearchQueueData(this.queryParams)
            .then(res => {
                this.list = res.rows || [];
                this.isNewUser = this.list.some(item => {
                    return item.isNewUser
                })
                this.total = res.total;
                this.$modal.closeLoading();
                this.$nextTick(() => {
                    this.setSelectRow();
                });
                // 添加受试者编号列
                let fieldName = 'input91621'
                if (this.list.some(item => item[fieldName] != undefined) && !this.customColumn.some(item => item.fieldName == fieldName)) {
                    this.customColumn = [...[ { fieldName, fieldLabel: '受试者编号' } ],...this.customColumn]
                }
                // 缓存当前页数据
                let studyCohortEditData = this.list.map(item => {
                    return {
                        empi: item.EMPI,
                        encountId: item.encountId,
                        baseLine: item.baseLine,
                    }
                })
                sessionStorage.setItem('studyCohortEditData', JSON.stringify(studyCohortEditData))

            }).catch(_ => {
                this.$modal.closeLoading();
            })
        },
        // 排序
        onSortChange({column, prop, order}) {
            console.log("打印column",column, prop, order)
            if (order) {
                this.sortList = [
                    {
                        sortField: prop,
                        sortValue: order === 'descending' ? 0 : 1
                    }
                ]
            } else {
                this.sortList = []
            }
            this.getList()
        },
        //切换课题
        changeRsId(e) {
			this.queryParams.rgId = '';
			this.experimentalGroupList = [];
			this.getGrouplist();
        },
        //获取当前选择的实验组关联的crf表单数据
        getFormTemplateJson() {
            getFormTemplate({formKey: this.formCode})
            .then(res => {
                if (!res.data) return
                let formJson = JSON.parse(res.data.templateJson);
                let fildList = [];
                let fildList1 = [];
                let _this = this;
                function recursiveFn(arr) {
                    for(let i = 0; i < arr.length; i++) {
                        if(arr[i].type != 'sub-form') {
                            if(arr[i].type == 'radio' || arr[i].type == 'checkbox' || arr[i].type == 'select') {
                                let item = {
                                    fieldName: arr[i].options.name,
                                    fieldLabel: arr[i].options.label,
                                    type: arr[i].type,
                                    disabled: false
                                }
                                if(arr[i].options.dict) {
                                    item.dict = arr[i].options.dict
                                    _this.getDictData(arr[i].options.dict, arr[i].options.name+'_dictCode')
                                }else {
                                    item.optionItems = arr[i].options.optionItems;
                                    _this.$set(_this.dictList, arr[i].options.name+'_dictCode',  arr[i].options.optionItems)
                                }
                                fildList.push(item);
                            }else if(arr[i].widgetList) {
                                recursiveFn(arr[i].widgetList)
                            }else if(arr[i].cols && arr[i].cols) {
                                recursiveFn(arr[i].cols)
                            }else{
                                let item = {
                                    fieldName: arr[i].options.name,
                                    fieldLabel: arr[i].options.label,
                                    type: arr[i].type
                                }
                                fildList.push(item);
                            }
                        }else {
                            let item = {
                                 fieldName: arr[i].options.name,
                                 type: arr[i].type,
                                 field: []
                            }
                            arr[i].widgetList.forEach(fieldItem => {
                                let subField = {
                                    fieldName: fieldItem.options.name,
                                    fieldLabel:fieldItem.options.label,
                                    type: fieldItem.type
                                }
                                if(fieldItem.type == 'radio' || fieldItem.type == 'checkbox' || fieldItem.type == 'select') {
                                    if(fieldItem.options.dict) {
                                        subField.dict = fieldItem.options.dict
                                    }else {
                                        subField.optionItems = fieldItem.options.optionItems;
                                    }
                                }
                                item.field.push(subField)
                            })
                            fildList1.push(item)
                        }
                    }
                }
                recursiveFn(formJson.widgetList);
                this.searchFieldList = fildList;
                let params = [...fildList,...fildList1];
                this.createStatisticsFileParams.formCodeJson = JSON.stringify(params);
            })
        },
         //获取数据字典
        getDictData(dictCode, key) {
            if(!this.dictList[dictCode]) {
                this.getDicts(dictCode).then(res => {
                    res.data.forEach(item => {
                        item.label = item.dictLabel;
                        item.value = item.dictValue;
                        item.raw = { listClass: null }
                    });
                    this.$set(this.dictList, key, res.data);
                });
            }
        },
        //添加一行查询条件
        addSearchLine() {
            this.searchForm.fieldList.push(
                {
                    fieldName: '',
                    fieldValue:'',
                    condition: ''
                }
            )
        },
        //删除一行查询条件
        delSearchLine(index) {
            let itemField = this.searchForm.fieldList[index];
            if(itemField.fieldName) {
                this.searchFieldList.map((item) => {
                    if(item.fieldName == itemField.fieldName){
                        item.disabled = false;
                    }
                })
            }
            this.searchForm.fieldList.splice(index, 1)
        },
        //选择字段
        changeFieldName(val, rowIndex) {
            this.searchFieldList.forEach(item => {
                item.disabled = false;
                if(item.fieldName == val) {
                    this.$set(this.searchForm.fieldList[rowIndex], 'type', item.type);
                    this.$set(this.searchForm.fieldList[rowIndex], 'fieldLabel', item.fieldLabel);
                }
                this.searchForm.fieldList.forEach(fItem => {
                    if(item.fieldName == fItem.fieldName) {
                        item.disabled = true;
                    }
                })
            })
            this.searchForm.fieldList[rowIndex].fieldValue = '';
        },
        /** 搜索按钮操作 */
		search() {
            let columns = [];
            let selectFields = [];
            this.searchForm.fieldList.forEach(item => {
                let fieldName = item.fieldName.split('_and_');
                let obj = {
                    fieldName:item.fieldName ,
                    fieldLabel: item.fieldLabel,
                }
                if(this.dictList[item.fieldName+'_dictCode'] && this.dictList[item.fieldName+'_dictCode'].length > 0) {
                    obj.dict = this.dictList[item.fieldName+'_dictCode']
                }
                if(fieldName.length > 1) {
                    if(
                        fieldName[1] != 'pat_name' &&
                        fieldName[1] != 'gender' &&
                        fieldName[1] != 'age' &&
                        fieldName[1] != 'phone' &&
                        fieldName[1] != 'birthday' &&
                        fieldName[1] != 'id_type' &&
                        fieldName[1] != 'id_card' &&
                        fieldName[1] != 'address'){
                            columns.push(obj)
                        }
                } else {
                    obj.fieldLabel && columns.push(obj)
                }
                selectFields.push({
                    condition: item.condition,
                    fieldName: item.fieldName,
                    fieldValue: item.fieldValue
                })
            })
            this.customColumn = columns;
            this.queryParams.pageNum = 1;
            this.queryParams.selectFields = selectFields;
            this.queryParams.auditEndStatus = this.searchForm.auditEndStatus;
            this.userListAllSelections = [];
            this.getList();
		},
		/** 重置按钮操作 */
		reset() {
			this.searchForm.fieldList = [
                {
                    fieldName: '',
                    fieldValue:'',
                    condition: ''
                }
            ]
            this.searchForm.auditEndStatus = undefined;
            this.queryParams.auditEndStatus = undefined;
            this.searchFieldList.map(item => item.disabled = false)
            this.queryParams.selectFields = [];
            this.userListAllSelections = [];
            this.queryParams.pageNum = 1;
            this.getList();
		},
        //跳转自定义表单
        toFormPage(type, row, pathType) {
            // if (type == 'add') {
                if (this.lockStatus == 1) {
                    return this.$message.error("当前队列已锁定，禁止操作！");
                }
            // }
            if(!this.formCode) {
                this.$message.error('当前队列没有关联表单信息!');
                return false;
            }
            let queryData = {
                formKey: this.formCode,
                rgId: this.rgId,
                type: type,
                rsId: this.queryParams.rsId,
                selectGroupText: this.selectGroupText
            }
            if(type == 'edit') {
                // queryData.empi = row.EMPI;
                queryData.encountId = row.encountId;
                // queryData.baseLine = row.baseLine;
                sessionStorage.setItem('studyCohortEditEmpi', row.EMPI)
            }
            if (pathType == 'ai') {
                this.$router.push({path: '/casegroup/aiStudyCohortEdit', query: queryData})
            } else {
                this.$router.push({path: '/casegroup/studyCohortEdit', query: queryData})
            }
        },
        //删除用户
        delUserItem(row) {
            if (this.lockStatus == 1) {
                return this.$message.error("当前队列已锁定，禁止操作！");
            }
            this.$confirm('是否确认删除该患者？',"提示", {
                confirmButtonText: "是",
                cancelButtonText: "否",
                type: "warning"
            }).then(res => {
                delFromMongo({empi: row.EMPI, rgId: this.rgId, encountId: row.encountId})
                .then(res => {
                    this.$message.success('删除成功');
                    this.userListAllSelections.forEach((item, index) => {
                        if(row.EMPI == item.EMPI) {
                            this.userListAllSelections.splice(index , 1);
                        }
                    })
                    this.getList();
                })
          }).catch(_ => {});
        },
        //选择用户
        selectUser(val) {
            this.selectedUserList = val;
            this.$nextTick(() => {
                this.changePageCoreRecordData();
            });
        },
        //设置表格数据选中
        setSelectRow() {
            if (!this.userListAllSelections || this.userListAllSelections.length <= 0) {
                this.$refs.userTable.clearSelection();
                return;
            }
            // 标识当前行的唯一键的名称
            let idKey = "EMPI";
            let selectAllIds = [];
            this.userListAllSelections.forEach((row) => {
                selectAllIds.push(row[idKey]);
            });
            this.$refs.userTable.clearSelection();
            for (var i = 0; i < this.list.length; i++) {
                if (selectAllIds.indexOf(this.list[i][idKey]) >= 0) {
                    this.$refs.userTable.toggleRowSelection(this.list[i], true);
                }
            }
        },
        // 记忆选择核心方法
        changePageCoreRecordData() {
            // 标识当前行的唯一键的名称
            let idKey = "EMPI";
            // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算
            if (this.userListAllSelections.length <= 0) {
                this.selectedUserList.forEach((row) => {
                    this.userListAllSelections.push(row);
                });
                return;
            }
            // 总选择里面的key集合
            let selectAllIds = [];
            this.userListAllSelections.forEach((row) => {
                selectAllIds.push(row[idKey]);
            });
            let selectIds = [];
            // 获取当前页选中的id
            this.selectedUserList.forEach((row) => {
                selectIds.push(row[idKey]);
                // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里
                if (selectAllIds.indexOf(row[idKey]) < 0) {
                    this.userListAllSelections.push(row);
                }
            });
            let noSelectIds = [];
            // 得到当前页没有选中的id
            this.list.forEach((row) => {
                if (selectIds.indexOf(row[idKey]) < 0) {
                    noSelectIds.push(row[idKey]);
                }
            });
            noSelectIds.forEach((id) => {
                if (selectAllIds.indexOf(id) >= 0) {
                    for (let i = 0; i < this.userListAllSelections.length; i++) {
                        if (this.userListAllSelections[i][idKey] === id) {
                            // 如果总选择中有未被选中的，那么就删除这条
                            this.userListAllSelections.splice(i, 1);
                            break;
                        }
                    }
                }
            });
        },
        //导出 type: 1选中数据 2全部数据
        exportList() {
            if(!this.formCode) {
                this.$message.error('当前队列没有关联表单信息!');
                return false;
            }
            let empis = [];
            this.userListAllSelections.forEach(item => {
                if(item.EMPI) {
                    empis.push(item.EMPI);
                }
            })
            let params = new FormData()
            params.append('empis', empis.length ? empis.join(',') : '')
            params.append('rgId', this.rgId)
            params.append('exportType', this.exportType)
            params.append('desensitizationAuditObject', this.desensitizationAuditObject)
            // 添加附件
            if (this.$refs.uploadRef?.fileList.length) {
                let sysTaskFileInfoList = this.$refs.uploadRef?.fileList.map(item => {
                return {
                    fileUrl: item.url,
                    remark: JSON.stringify({size: item.size, name: item.name})
                }
                })
                params.append("sysTaskFileInfoList", JSON.stringify(sysTaskFileInfoList))
            }
            this.$prompt('自定义文件名称', '系统提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(({ value }) => {
                params.append("customExportFileName", value)
                exportApplication(params).then(res => {
                    this.$message.success('导出申请成功');
                    this.exportDialog = false
                })
            }).finally(() => {
            });
            
        },
        //跳转患者360
        view(row) {
            this.$jumpPatient360({ path: "/cdrView", query: { empi: row.EMPI } }, row)
        },
        //跳转统计分析
        toStatis() {
            if(!this.formCode) {
                this.$message.error('当前队列没有关联表单信息!');
                return false;
            }else if(!this.createStatisticsFileParams.formCodeJson) {
                this.$message('表单数据正在处理中, 请稍后再试!');
                return false;
            }
            this.createStatisticsFileParams.rgId = this.rgId;
            this.$refs.selectStatisticalType.init(this.createStatisticsFileParams, 2);
        },
        exportTemplate() {
            if (!this.formCode) {
                this.$message.error('当前队列没有关联表单信息!');
                return false;
            }
            this.download("disease/group/export/template", { rgId: this.rgId }, `研究队列导出模版_${new Date().getTime()}.xlsx`);
        },
        // 文件上传中处理
        handleFileUploadProgress(event, file, fileList) {
            this.upload.isUploading = true;
        },
        // 文件上传成功处理
        handleFileSuccess(response, file, fileList) {
            this.upload.open = false;
            this.upload.isUploading = false;
            this.$refs.upload.clearFiles();
            this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
            this.getList();
        },
        /** 导入按钮操作 */
        handleImport(title) {
            if (this.lockStatus == 1) {
                return this.$message.error("当前队列已锁定，禁止操作！");
            }
            this.upload.title = title;
            this.upload.open = true;
        },
        submitFileForm() {
            this.$refs.upload.submit();
        },
    }
}
</script>

<style lang="scss">
.studyCohortDetail-page {
    height: 100%;
    background: #fff !important;
    background-color: #fff !important;
    .p-head-box {
        padding: 15px 20px 0;
        // margin-bottom: 10px;
        line-height: 24px;
        .fl-head {
            // float: left;
            width: 500px;
        }
        .fr-head {
            // margin-left: 520px
            .set-sel-ipt-width {
                width: 400px;
            }
        }
        .p-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding-right: 20px;
        }
        .p-info {
            font-size: 15px;
            color: #333;
        }
        .sel-box {
            overflow: hidden;
            .label {
                font-size: 14px;
                color: #555;
                float: left;
                line-height: 32px;
            }
            .sel-div {
                // margin: 0 10px 0 72px;
                margin: 0 10px 0 0px;
                width: 418px;
                height: 32px;
                border:1px solid #DCDFE6;
                border-radius: 4px;
                padding:0 10px;
                font-size: 14px;
                color: #666;
                line-height: 30px;
            }
            .arr-icon {
                float: right;
                margin-top:10px;
            }
            .subject-text {
                margin-right: 30px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }
        }
        .selectType .el-input {
            width: 110px;
        }
        .set-width {
            width: 220px;
        }
    }
    .selectUserNum {
        font-size: 16px;
        color: #666;
        line-height: 26px;
        float: left;
        .user-nums {
            font-size: 18px;
            color: #1890ff;
            padding: 0 2px;
        }
    }
}
.pop-subject {
     padding: 0px 0px 10px !important;
    .subject-item {
        color: #333;
        font-weight: bold;
        text-align: left;
        .fr-icon {
            color: #999;
            float: right;
            margin-top: 4px;
        }
    }
    .group-box {
        text-align: left;
        .group-item {
            // padding-right: 5%;
            color: #666;
            display: inline-block;
            cursor: pointer;
            width: 50%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .active {
            color: #179e84;
            font-weight: bold;
        }
    }
    .set-tab-header-h {
        padding: 0;
        height: 34px;
    }
}
</style>
