<template>
  <div class="app-container projectDetail-page">
    <div class="p-head-box">
      <div class="fl-head">
        <!-- <div slot="reference">click 激活</div> -->
        <div class="sel-box">
          <span class="label">切换队列</span>
          <el-popover
            popper-class="pop-subject"
            placement="bottom"
            width="420"
            trigger="click"
            @show="getSubjectAndGroupTree()"
            v-model="isShowPopper"
          >
            <el-table
              class="sub-table"
              :data="subjectAndGroupTreeList"
              height="350px"
              header-cell-class-name="set-tab-header-h"
            >
              <el-table-column
                width="150"
                property="subjectName"
                label="科研课题"
                align="center"
              >
                <template slot-scope="scope">
                  <div class="subject-item">
                    <i class="el-icon-caret-right fr-icon"></i>
                    {{ scope.row.subjectName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="初筛队列" align="center">
                <template slot-scope="scope">
                  <div
                    class="group-box"
                    v-if="scope.row.groupList && scope.row.groupList.length > 0"
                  >
                    <span
                      class="group-item"
                      :class="cgId == item.rgId ? 'active' : ''"
                      @click="selectGroup(scope.row, item.rgId, item.groupName)"
                      v-for="(item, index) in scope.row.groupList"
                    >
                      <el-tooltip
                        class="item"
                        effect="dark"
                        :content="`${item.groupName}(${
                          item.initialQueueDataCount || 0
                        })`"
                        placement="top-start"
                      >
                        <span>
                          {{
                            item.groupName.length > 5
                              ? item.groupName.substr(0, 5) + "..."
                              : item.groupName
                          }}
                          ({{ item.initialQueueDataCount || 0 }})
                        </span>
                      </el-tooltip>
                    </span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div class="sel-div" slot="reference">
              <i class="arr-icon el-icon-arrow-down"></i>
              <div class="subject-text">{{ selectGroupText }}</div>
            </div>
          </el-popover>
        </div>
      </div>
      <div class="fr-head">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="small"
          @click="toSubjectPage"
          >快速创建队列</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="small"
          @click="showModel = true"
          >韦恩图工具</el-button
        >
        <span class="p-name">{{ projectData.projectName }}</span>
        <span class="p-info">
          <!-- 此初筛队列共{{ total1 }}名患者，{{variableNum}}个研究变量 -->
          病例数{{ total1 }}, 变量数{{ variableNum }}
        </span>
      </div>
    </div>
    <div class="p-main-box" v-loading="mainBoxLoading" element-loading-text="数据加载中..." element-loading-background="rgba(225, 225, 225, 0.6)">
      <div class="fl-box" :class="!isUnfold ? 'fl-hidden' : ''">
        <div class="fl-box-content">
          <div class="label">变量管理</div>
          <!-- <div v-if="varGroupList.length > 0">
            {{varGroupList[1]}}
            {{varGroupList[1].conditionBaseline.fieldName}}</br>
            {{typeof varGroupList[1].conditionBaseline}}</br>
          </div> -->

          <div class="group-item" :class="!item.isShow ? 'set-group-height' : ''" v-for="(item, index) in varGroupList" :key="index">
            <div class="c-item">
              <div class="field-box">
                <div class="operate">
                  <el-button
                    v-if="index > 0"
                    type="text"
                    size="small"
                    @click="delGroup(index)"
                    >删除组</el-button>
                  <el-button
                    v-if="item.isShow"
                    type="text"
                    size="small"
                    @click="item.isShow = false"
                    >收起<i class="el-icon-caret-top el-icon--right"></i
                  ></el-button>
                  <el-button
                    v-else
                    type="text"
                    size="small"
                    @click="item.isShow = true"
                    >展开<i class="el-icon-caret-bottom el-icon--right"></i
                  ></el-button>
                  <!-- <el-button v-else type="text" size="small" @click="resetGroup">重置组</el-button> -->
                </div>
                <div class="f-name" v-if="index == 0">{{ item.name }}</div>
                <!-- <div class="f-name" v-else-if="index == 1">基线时间</div> -->
                <div class="f-name" v-else>{{ item.name }}{{ index}}</div>
              </div>
            </div>

            <div class="c-item" v-if="false">
              <!-- <div class="label">基线时间</div> -->
              <div
                v-if="
                  item.conditionBaseline && item.conditionBaseline.fieldName
                "
              >
                <div class="field-box">
                  <div class="operate">
                    <el-button
                      type="text"
                      size="small"
                      @click="addVariate(3, index)"
                      >编辑</el-button
                    >
                    <el-button
                      type="text"
                      size="small"
                      @click="delBaseLineField(index)"
                      >删除</el-button
                    >
                  </div>
                  <div class="baseLine-field">
                    {{ item.conditionBaseline.fieldComment }}
                  </div>
                </div>
                <div class="filter-box">
                  <div class="box">
                    <el-select
                      class="set-w"
                      v-model="item.conditionBaseline.condition"
                      size="small"
                      filterable
                    >
                      <el-option
                        v-for="conditionItem in operativeSymbolList"
                        :key="conditionItem.value"
                        :label="conditionItem.label"
                        :value="conditionItem.value"
                      ></el-option>
                    </el-select>
                    <el-tooltip
                      v-if="showTip(item.conditionBaseline.condition)"
                      class="icon-warning"
                      effect="dark"
                      :content="showTipValue(item.conditionBaseline.condition)"
                      placement="top"
                    >
                      <i class="tip-icon el-icon-warning"></i>
                    </el-tooltip>
                    <el-tooltip
                      v-else-if="
                        item.conditionBaseline.condition == '&lt;' ||
                        item.conditionBaseline.condition == '&lt;='
                      "
                      class="icon-warning"
                      effect="dark"
                      content="小于选择的日期, 大于选择的日期前一年"
                      placement="top"
                    >
                      <i class="tip-icon el-icon-warning"></i>
                    </el-tooltip>
                    <el-date-picker unlink-panels 
                      v-show="item.conditionBaseline.condition !== 'in'"
                      class="set-w1"
                      size="small"
                      v-model="item.conditionBaseline.fieldValue"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="开始日期"
                    ></el-date-picker>
                    <el-date-picker unlink-panels 
                      :style="
                        item.conditionBaseline.condition != 'in'
                          ? 'display:none !important'
                          : ''
                      "
                      class="set-w1"
                      size="small"
                      v-model="item.conditionBaseline.fieldValue2"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      value-format="yyyy-MM-dd"
                      end-placeholder="结束日期"
                      @change="
                        changeConditionBaseline($event, item.conditionBaseline)
                      "
                    ></el-date-picker>
                    <el-select
                      class="set-w whichTimes"
                      v-model="item.conditionBaseline.whichTimes"
                      size="small"
                      filterable
                    >
                      <el-option label="首次" value="3"></el-option>
                      <el-option label="末次" value="4"></el-option>
                    </el-select>
                  </div>
                  <div class="box">
                    <el-select
                      class="set-w"
                      v-model="item.conditionBaseline.traceAnchor"
                      size="small"
                      filterable
                    >
                      <el-option label="前后" value="1"></el-option>
                      <el-option label="前" value="2"></el-option>
                      <el-option label="后" value="3"></el-option>
                    </el-select>
                    <el-input
                      class="set-w"
                      v-model="item.conditionBaseline.anchorNum"
                      size="small"
                      clearable
                    ></el-input>
                    <el-select
                      class="set-w2"
                      v-model="item.conditionBaseline.anchorUnit"
                      size="small"
                      filterable
                    >
                      <el-option label="日" value="1"></el-option>
                      <el-option label="月" value="2"></el-option>
                      <el-option label="年" value="3"></el-option>
                    </el-select>
                    <el-select
                      class="set-w2"
                      v-model="item.conditionBaseline.anchorRange"
                      size="small"
                      filterable
                    >
                      <el-option label="内" value="1"></el-option>
                      <!-- <el-option label="外" value="2"></el-option> -->
                    </el-select>
                  </div>
                </div>
                <el-checkbox
                  v-model="item.conditionBaseline.onlyInHospital"
                  label="只查住院数据"
                  true-label="1"
                  false-label="0"
                ></el-checkbox>
              </div>
              <el-button
                v-if="
                  !item.conditionBaseline || !item.conditionBaseline.fieldName
                "
                type="text"
                size="small"
                icon="el-icon-plus"
                @click="addVariate(3, index)"
                >添加基线时间</el-button
              >
            </div>
            <div class="c-item baseline-describe-text" v-if="index > 0">
              <div class="describe-text">
                <div class="text-label">基线时间:</div>
                <div class="text-content">
                  <span class="text" v-if="item.baselineDescribe">{{item.baselineDescribe}}</span>
                  <el-button class="edit-btn" type="text" size="small" @click="setBaseline(index, item)" icon="el-icon-edit">设定基线时间</el-button>
                </div>
              </div>
              <div>
                 <el-checkbox
                  v-model="item.conditionBaseline.onlyInHospital"
                  label="只查住院数据"
                  true-label="1"
                  false-label="0"
                ></el-checkbox>
              </div>
            </div>
            <div class="c-item">
              <!-- <div class="label">研究变量</div> -->
              <!-- 这里使用filter过滤是 为了将第一个empi进行隐藏起来 -->
              <el-table
                :data="
                  item.variateList.filter((test) => {
                    return test.fieldName != 'EMPI__00' && test.fieldComment != '就诊号';
                  })"
                border
                stripe
                :row-class-name="rowClassName">
                <el-table-column prop="fieldComment" label="变量名称">
                  <template slot-scope="scope">
                    <div>
                      <span>{{ scope.row.fieldComment }}</span>
                      <!-- <el-cascader class="sel-cascader" size="mini"
                        v-model="scope.row.fieldValue"
                        v-if=" scope.row.tableName == 'sd_chemcheck_doc' && (scope.row.fieldName.split('__')[0] == 'item_name' || scope.row.fieldName.split('__')[0] == 'item_code') "
                        :placeholder="scope.row.fieldName.split('__')[0] == 'item_name' ? '选择检验项目名'  : '选择检验项目代码'"
                        filterable
                        :options="chemCheckList"
                        :props="scope.row.fieldName.split('__')[0] == 'item_name' ? props : propsCode"
                        :before-filter="cascaderFilter"
                        :filter-method="filterNode"
                        @focus="cascaderFocus(scope.row)"
                        :key="scope.row.randomKey"
                      ></el-cascader> -->
                      <el-select class="sel-cascader" v-model="scope.row.fieldValue" v-if="scope.row.tableName == 'sd_chemcheck_doc' && scope.row.fieldName.split('__')[0] == 'item_name'" clearable filterable size="mini">
                          <el-option :label="oItem.itemName" :value="oItem.itemName" v-for="(oItem, oIndex) in chemCheckList" :key="oIndex"></el-option>
                      </el-select>
                      <el-select class="sel-cascader" v-model="scope.row.fieldValue" v-else-if="scope.row.tableName == 'sd_chemcheck_doc' && scope.row.fieldName.split('__')[0] == 'item_code'" clearable filterable size="mini">
                          <el-option :label="oItem.itemCode" :value="oItem.itemCode" v-for="(oItem, oIndex) in chemCheckList" :key="oIndex"></el-option>
                      </el-select>
                      <el-cascader class="sel-cascader" size="mini"
                        v-model="scope.row.fieldValue"
                        v-if="scope.row.tableName == 'sd_chemcheck_doc' && (scope.row.fieldName.split('__')[0] == 'subitem_name' || scope.row.fieldName.split('__')[0] == 'subitem_code')"
                        :placeholder="scope.row.fieldName.split('__')[0] == 'subitem_name' ? '选择检验子项目名' : '选择检验子项目代码'"
                        filterable
                        :options="chemCheckList"
                        :props="scope.row.fieldName.split('__')[0] == 'subitem_name' ? props : propsCode"
                        :before-filter="cascaderFilter"
                        :filter-method="filterNode"
                        @focus="cascaderFocus(scope.row)"
                        :key="scope.row.randomKey"
                      ></el-cascader>
                      <!-- <el-cascader class="sel-cascader" size="mini" v-model="scope.row.fieldValue"
                        v-else-if="scope.row.tableName == 'sd_techcheck_doc' && (scope.row.fieldName.split('__')[0] == 'item_name' || scope.row.fieldName.split('__')[0] == 'check_item')"
                        :placeholder="scope.row.fieldName.split('__')[0] == 'item_name' ? '选择检查项目名' : '选择检查项目代码'"
                        filterable
                        :options="techCheckList"
                        :props="scope.row.fieldName.split('__')[0] == 'item_name' ? props : propsCode"
                        :before-filter="cascaderFilter"
                        :filter-method="filterNode"
                        @focus="cascaderFocus(scope.row)"
                        :key="scope.row.randomKey"
                      ></el-cascader> -->
                      <el-select class="sel-cascader" v-model="scope.row.fieldValue" v-else-if="scope.row.tableName == 'sd_techcheck_doc' && scope.row.fieldName.split('__')[0] == 'item_name'" clearable filterable size="mini">
                          <el-option :label="oItem.itemName" :value="oItem.itemName" v-for="(oItem, oIndex) in techCheckList" :key="oIndex"></el-option>
                      </el-select>
                      <el-select class="sel-cascader" v-model="scope.row.fieldValue" v-else-if="scope.row.tableName == 'sd_techcheck_doc' && scope.row.fieldName.split('__')[0] == 'check_item'" clearable filterable size="mini">
                          <el-option :label="oItem.itemCode" :value="oItem.itemCode" v-for="(oItem, oIndex) in techCheckList" :key="oIndex"></el-option>
                      </el-select>
                      <el-cascader class="sel-cascader" size="mini" v-model="scope.row.fieldValue"
                        v-else-if="  scope.row.tableName == 'sd_techcheck_doc' && (scope.row.fieldName.split('__')[0] == 'subitem_name' || scope.row.fieldName.split('__')[0] == 'check_subitem')"
                        :placeholder="scope.row.fieldName.split('__')[0] == 'subitem_name' ? '选择检查子项目名' : '选择检查子项目代码'"
                        filterable
                        :options="techCheckList"
                        :props="scope.row.fieldName.split('__')[0] == 'subitem_name' ? props : propsCode"
                        :before-filter="cascaderFilter"
                        :filter-method="filterNode"
                        @focus="cascaderFocus(scope.row)"
                        :key="scope.row.randomKey"
                      ></el-cascader>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="取值" width="100">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.fieldValue"
                      placeholder="请输入自定义变量的值"
                      size="small"
                      v-if="
                        scope.row.fieldType && scope.row.fieldType == 'custom'
                      "
                    ></el-input>
                    <el-select
                      v-model="scope.row.whichTimes"
                      size="small"
                      v-else-if="scope.row.tableName != 'sd_patient_info'"
                      :disabled="
                        (typeof varGroupList[1].conditionBaseline != 'object' ||
                          !varGroupList[1].conditionBaseline.fieldName) &&
                        scope.row.computeFlag != 1
                      "
                      class="o-select"
                    >
                      <el-option
                        label="最近一次"
                        value="1"
                        :disabled="
                          typeof varGroupList[1].conditionBaseline !=
                            'object' ||
                          !varGroupList[1].conditionBaseline.fieldName
                        "
                      ></el-option>
                      <el-option
                        label="全部"
                        value="2"
                        :disabled="
                          typeof varGroupList[1].conditionBaseline !=
                            'object' ||
                          !varGroupList[1].conditionBaseline.fieldName
                        "
                      ></el-option>
                      <el-option
                        label="首次"
                        value="3"
                        :disabled="
                          typeof varGroupList[1].conditionBaseline !=
                            'object' ||
                          !varGroupList[1].conditionBaseline.fieldName
                        "
                      ></el-option>
                      <el-option
                        label="末次"
                        value="4"
                        :disabled="
                          typeof varGroupList[1].conditionBaseline !=
                            'object' ||
                          !varGroupList[1].conditionBaseline.fieldName
                        "
                      ></el-option>
                      <el-option
                        v-if="scope.row.computeFlag == 1"
                        label="最大值"
                        value="5"
                      ></el-option>
                      <el-option
                        v-if="scope.row.computeFlag == 1"
                        label="最小值"
                        value="6"
                      ></el-option>
                      <el-option
                        v-if="scope.row.computeFlag == 1"
                        label="平均值"
                        value="7"
                      ></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="fillRate"
                  label="填充率"
                  width="60"
                ></el-table-column>
                <el-table-column label="操作" width="47">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      size="small"
                      v-if="
                        (index == 0 &&
                          scope.row.fieldName != 'EMPI__00' &&
                          scope.row.fieldName != 'encount_id__00' &&
                          scope.row.fieldName != 'pat_name__01' &&
                          scope.row.fieldName != 'gender__02' &&
                          scope.row.fieldName != 'birthday__03' &&
                          scope.row.fieldName != 'pat_name__02' &&
                          scope.row.fieldName != 'gender__03' &&
                          scope.row.fieldName != 'birthday__04'
                          ) || index != 0
                      "
                      @click="delVariate(scope, index)"
                      >移除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
              <div class="add-btn-group">
                <el-button
                  type="text"
                  plain
                  icon="el-icon-plus"
                  size="small"
                  @click="addVariate(1, index)"
                  >添加变量</el-button
                >
                <!-- <el-button
                  type="text"
                  plain
                  icon="el-icon-plus"
                  size="small"
                  @click="addVariate(2, index)"
                >添加自定义变量</el-button>-->
              </div>
            </div>
          </div>
        </div>

        <div class="f-btn-group">
          <div class="fl">
            <el-button type="primary" plain size="small" @click="addVarGroup"
              >添加变量组</el-button
            >
          </div>
          <div class="fr">
            <!-- <el-button type="default" size="small" @click="reset">重置</el-button> -->
            <el-button
              :disabled="loading || !cgId"
              type="primary"
              size="small"
              @click="useVariate('btn')"
              >刷新应用</el-button>
          </div>
        </div>
      </div>
      <div class="fr-box" :class="!isUnfold ? 'set-mar-l' : ''">
        <div class="fr-head-box">
          <div class="fr-btn-group">
            <!-- <div class="table-type">
                          <el-radio-group v-model="formType" :disabled="loading" size="small" @change="changeTableType">
                              <el-radio-button label="1">表格1</el-radio-button>
                              <el-radio-button label="2">表格2</el-radio-button>
                              <el-radio-button label="3">表格3</el-radio-button>
                          </el-radio-group>
            </div>-->
            <el-button
              type="primary"
              size="small"
              plain
              @click="isUnfold = !isUnfold"
              >变量管理 / {{ isUnfold ? "隐藏" : "显示" }}</el-button
            >
            <!-- <el-button type="primary" size="small" plain @click="showDownloadListAlert">下载列表</el-button> -->
            <el-button
              type="primary"
              size="small"
              plain
              :disabled="dataList1.length == 0"
              @click="toStatis"
              >统计分析</el-button
            >
            <!-- <el-button
              type="primary"
              size="small"
              plain
              @click="saveResearchQueue"
              :disabled="userListAllSelections.length == 0"
              >保存为研究队列</el-button> -->
            <el-dropdown @command="selectSaveType" class="dropdown-box">
              <el-button type="primary"  size="small" plain>
                保存为研究队列<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1" :disabled="userListAllSelections.length == 0">保存已勾选的数据</el-dropdown-item>
                <el-dropdown-item command="2">保存全部数据</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-dropdown style="margin-right: 10px;" @command="onExport" :disabled="dataList1.length == 0" v-hasPermi="['disease:initialQueueData:desensitizationExport', 'disease:initialQueueData:nonDesensitizationExport']">
              <el-button type="primary"  size="small" plain :disabled="dataList1.length == 0">
                导出申请<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="data_export" v-hasPermi="['disease:initialQueueData:desensitizationExport']">脱敏导出</el-dropdown-item>
                <el-dropdown-item command="data_non_desensitization_export" v-hasPermi="['disease:initialQueueData:nonDesensitizationExport']">非脱敏导出</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <!-- <el-button type="primary" size="small" plain @click="">导入</el-button>
            <el-button type="primary" size="small" plain @click="">添加患者</el-button>-->
            <!-- <el-button type="primary" size="small" plain @click="editCondition">修改筛选条件</el-button> -->
            <el-button
              type="danger"
              size="small"
              @click="delUser"
              :disabled="userListAllSelections.length == 0"
              >删除患者</el-button
            >
            <div
              class="exportRecord-btn"
              ref="exportRecord"
              @click="showDownloadListAlert"
              v-if="false"
            >
              <!-- 导出<br />记录 -->
              <el-tooltip
                class="item"
                effect="dark"
                content="导出记录"
                placement="left"
              >
                <img src="@/assets/images/disease/export-icon.png" alt="" />
              </el-tooltip>
            </div>
            <div class="exportRecord-btn history-btn" @click="getHistoryList">
              <el-tooltip
                class="item"
                effect="dark"
                content="队列记录"
                placement="left"
              >
                <img src="@/assets/images/disease/record-icon.png" alt="" />
              </el-tooltip>
              <!-- 队列<br />记录 -->
            </div>
          </div>
          <div class="selectUserNum" v-if="userListAllSelections.length > 0">
            已选择<span class="user-nums">{{
              userListAllSelections.length
            }}</span
            >条数据
          </div>
        </div>
        <div v-show="formType == 1">
          <el-table
            ref="userTable"
            :data="dataList1"
            border
            stripe
            size="small"
            @selection-change="selectUser"
          >
            <!-- <el-table-column label="登记号" prop=""></el-table-column> -->
            <el-table-column type="selection" key="selection"></el-table-column>
            <el-table-column
              align="center"
              v-for="(col, index) in tableColumn1"
              :key="index"
              :label="col.name == '患者基本信息' ? col.name : col.name + index"
              v-if="col.variateList && col.variateList.length > 0">
              <!-- <el-table-column
                v-if="projectData.resultSetMode == 1 && col.name == '患者基本信息'"
                width="55"
              >
              </el-table-column> -->
              <el-table-column
                v-for="(col1, index1) in col.variateList"
                :key="index1"
                :label=" col1.fieldValue ? `${Array.isArray(col1.fieldValue) ? (col1.fieldValue[1] || col1.fieldValue[0]) : col1.fieldValue}${formatTableLabel(col1.whichTimes)}` : `${col1.fieldComment}${formatTableLabel(col1.whichTimes)}`"
                :show-overflow-tooltip="true"
                :prop="col1.fieldName"
                v-if="col1.fieldName !== 'EMPI__00'"
              >
                <template slot-scope="scope">

                  <span v-if="col1.fieldType && col1.fieldType == 'custom'">{{
                    col1.fieldValue
                  }}</span>
                  <span v-else-if="col1.tableName != 'sd_patient_info'">
                    <!-- {{col.name == '患者基本信息' ? col.name : col.name + index}} -->
                    <!-- {{scope.row[col1.tableName].map(item => { return item[col1.fieldName]}).join('，')}} -->
                    <!-- {{
                      scope.row[
                        col.name == "患者基本信息" ? col.name : col.name + index
                      ].map((item) => {
                          return item[col1.fieldName];
                        }).filter((_) => _).join("，") || ""
                    }} -->
                    <el-link
                      class="text-overhide"
                      v-if="
                        formatTdValueLength(
                          scope.row,
                          col.name,
                          index,
                          col1.fieldName
                        ) > 1
                      "
                      type="primary"
                      @click="
                        moreRecoedDetail(
                          scope.row,
                          col1.fieldName,
                          col1.tableName,
                          index,
                          index1,
                          col1.fieldValue,
                          col.name
                        )
                      "
                    >
                      <!-- {{ formatTdValueLength( scope.row, col.name, index,col1.fieldName) }} -->
                      {{
                        formatTdValue(
                          scope.row,
                          col.name,
                          index,
                          col1.fieldName
                        )
                      }}
                    </el-link>
                    <div v-else class="text-overhide">
                      {{
                        formatTdValue(
                          scope.row,
                          col.name,
                          index,
                          col1.fieldName
                        )
                      }}
                    </div>
                  </span>
                  <span v-else-if="col1.fieldName.split('__')[0] == 'pat_name'">
                    <!-- <span v-if="col1.dictCode">
                      <dict-tag
                        :options="dictList[col1.dictCode]"
                        :value="scope.row[col1.fieldName]"
                      />
                    </span>
                    <span v-else>{{ scope.row[col1.fieldName] }}</span>-->
                    <el-link type="primary" @click="view(scope.row, 'empi')">{{
                      scope.row[col1.fieldName]
                    }}</el-link>
                  </span>
                  <span v-else-if="col1.fieldName.split('__')[0] == 'encount_id'">
                    <el-link type="primary" @click="view(scope.row, 'encount_id')">{{
                      scope.row[col1.fieldName]
                    }}</el-link>
                  </span>
                  <span v-else>{{ scope.row[col1.fieldName] }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="col.name == '患者基本信息'"  label="入组时间" prop="create_time"></el-table-column>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total1 > 0"
            :total="total1"
            :page.sync="pageNum1"
            :limit.sync="pageSize1"
            @pagination="useVariate"
          />
        </div>
        <div v-show="formType == 2">
          <el-table :data="dataList2" border stripe size="small">
            <el-table-column
              v-for="(col, index) in tableColumnPatientInfo"
              :prop="col.fieldName"
              :label="col.fieldComment"
              :key="index"
              align="center"
            ></el-table-column>
            <el-table-column
              v-for="(col, index) in tableColumnOtherInfo"
              :label="col.tableName"
              :key="col.tableName + index"
              align="center"
            >
              <el-table-column
                v-for="(col1, index1) in col.exportFieldList"
                :label="col1.fieldComment"
                :key="col.tableName + index1"
              >
                <template slot-scope="scope">
                  <div
                    v-if="
                      scope.row[col1.tableName][col.index] &&
                      scope.row[col1.tableName][col.index][col1.fieldName]
                    "
                  >
                    {{ scope.row[col1.tableName][col.index][col1.fieldName] }}
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total2 > 0"
            :total="total2"
            :page.sync="pageNum2"
            :limit.sync="pageSize2"
            @pagination="useVariate"
          />
        </div>
      </div>
    </div>
    <!-- 选择字段组件 -->
    <selectField
      ref="selectField"
      :selectFieldType="selectFieldType"
      @getList="getField"
      parentComName="projectDetail"
    ></selectField>
    <!-- 添加自定义变量弹窗 -->
    <el-dialog
      title="添加自定义变量"
      :visible.sync="addVariateAlertShow"
      width="450px"
      top="20vh !important"
      :close-on-click-modal="false"
      @close="addVariateAlertShow = false">
      <el-form
        :model="variateForm"
        ref="variateForm"
        label-width="80px"
        :inline="false"
        size="normal"
      >
        <el-form-item
          prop="fieldComment"
          label="变量名称"
          :rules="[
            { required: true, message: '请输入变量名称', triggle: 'blur' },
          ]"
        >
          <el-input
            v-model="variateForm.fieldComment"
            placeholder="请输入变量名"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="fieldValue"
          label="变量值"
          :rules="[
            { required: true, message: '请输入变量值', triggle: 'blur' },
          ]"
        >
          <el-input
            v-model="variateForm.fieldValue"
            placeholder="请输入变量值"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="addVariateAlertShow = false">取消</el-button>
        <el-button type="primary" @click="sureAdd">保存</el-button>
      </span>
    </el-dialog>
    <!-- 导出弹窗 -->
    <el-dialog
      class="export-box"
      title="导出申请"
      :visible.sync="exportAlertShow"
      width="1000px"
      destroy-on-close
      @close="onExportAlertClose">
      <div
        v-loading="fullscreenLoading"
        element-loading-text="数据正在处理中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <span class="label">导出方式:</span>
        <el-radio-group v-model="exportType" @change="onExportTypeChange">
          <el-radio label="1">按研究变量</el-radio>
          <el-radio label="2">按数据模型</el-radio>
          <el-radio label="3">按基线时间导出</el-radio>
        </el-radio-group>
        <div style="margin-top: 20px;">
          <span class="label">导出格式:</span>
          <el-radio-group v-model="exportFormat">
            <el-radio label="1" v-if="exportType != 2">csv</el-radio>
            <el-radio label="2">excel</el-radio>
          </el-radio-group>
        </div>
        <div class="img-box" v-show="exportType != 2">
          <div class="label">导出示例:</div>
          <img
            class="img"
            v-if="exportType == 1"
            src="@/assets/images/exportType1.jpg"
            alt
          />
          <img
            class="img"
            v-else-if="exportType == 2"
            src="@/assets/images/exportType2.jpg"
            alt
          />
          <img
            class="img"
            v-else-if="exportType == 3"
            src="@/assets/images/exportType3.jpg"
            alt
          />
        </div>
        <!-- 导出字段展示 -->
        <div class="img-box" v-show="exportType == 2">
          <div>
            <el-button
              type="primary"
              @click="showSelectExportField"
              size="small"
              >
              <span>选择导出字段</span>
            </el-button
            >
          </div>
          <div v-show="exportFields.length">
            <div class="label" style="margin: 10px 0">导出字段预览</div>
            <div class="export-fields-box">
              <div class="field-item">
                <div class="table-name" style="font-weight: bold;">模型名称</div>
                <div class="field-list">
                  <div class="field-tag" style="font-weight: bold;">字段名称</div>
                </div>
              </div>
              <div class="field-item" v-for="(item,index) in exportFields" :key="index">
                <div class="table-name" :title="item.tableComment">{{ item.tableComment }}</div>
                <div class="field-list">
                  <el-tag closable class="field-tag" v-for="(item1,index1) in item.exportFieldList" :key="index1" @close="onTagRemove(item.exportFieldList, index1, index, item.tableName, item1.fieldName)">
                  {{item1.fieldComment}}
                </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="download-btn" v-if="(exportType == 1 && downloadUrl1) || (exportType == 2 && downloadUrl2)">
          <span>表格下载: </span>
          <el-button v-if="exportType == 1" type="primary" size="small" plain @click="download(downloadUrl1, projectData.projectName)">点击下载</el-button>
          <el-button v-else-if="exportType == 2" type="primary" size="small" plain @click="download(downloadUrl2, projectData.projectName)">点击下载</el-button>
          <span class="tip">*上次导出的表格文件</span>
        </div> -->
      </div>
      <div class="label" style="margin-top: 20px;margin-bottom: 10px;">附件上传:</div>
      <Upload ref="uploadRef"/>
      <span slot="footer">
        <el-button @click="exportAlertShow = false">取消</el-button>
        <el-button
          type="primary"
          :disabled="fullscreenLoading || (exportType == 2 && !exportFields.length)"
          @click="exportData"
          ><span ref="exportBtn">确定</span></el-button
        >
      </span>
    </el-dialog>

    <!-- 下载列表 -->
    <el-dialog
      title="导出列表"
      :visible.sync="isShowDownloadListAlert"
      width="900px"
      @close="isShowDownloadListAlert = false">
      <div>
        <!-- <el-tabs v-model="downloadType" type="card" tab-position="top" @tab-click="">
          <el-tab-pane
            :label=""
            :name="item.key">
          </el-tab-pane>
        </el-tabs> -->
        <div class="search-box">
          <span>导出类型：</span>
          <el-select
            v-model="fileQueryParams.exportType"
            placeholder="导出类型"
            clearable
            filterable
            size="small"
          >
            <el-option label="按研究变量" value="1"></el-option>
            <el-option label="按基线时间导出" value="3"></el-option>
          </el-select>
          <el-button
            class="s-btn"
            type="primary"
            size="small"
            @click="getExportRecordList('search')"
            >查询</el-button
          >
          <el-button
            type="primary"
            plain
            size="small"
            @click="getExportRecordList"
            >刷新导出状态</el-button
          >
        </div>
        <el-table :data="downloadFileList" border stripe>
          <el-table-column
            prop="downloadName"
            label="文件名称"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column prop="exportType" label="导出方式">
            <template slot-scope="scope">
              <div>
                <span v-if="scope.row.exportType == 1">按研究变量</span>
                <span v-else-if="scope.row.exportType == 2">按数据模型</span>
                <span v-else-if="scope.row.exportType == 3">按基线时间</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="导出状态">
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.sd_download_status"
                :value="scope.row.downloadStatus"
              />
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="生成时间" width="160">
          </el-table-column>
          <el-table-column prop="downloadTimes" label="下载次数">
          </el-table-column>
          <el-table-column prop="updateTime" label="最后下载时间" width="160">
            <template slot-scope="scope">
              <div v-if="scope.row.downloadTimes > 0">
                {{ scope.row.updateTime }}
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                v-if="scope.row.downloadStatus == 2"
                @click="download(scope.row)"
                >下载</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="delFile(scope.row.peId, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="fileTotal > 0"
          :total="fileTotal"
          :page.sync="fileQueryParams.pageNum"
          :limit.sync="fileQueryParams.pageSize"
          @pagination="getExportRecordList"
        />
      </div>
      <span slot="footer">
        <el-button @click="isShowDownloadListAlert = false">关闭</el-button>
        <!-- <el-button type="primary" @click="">确定</el-button> -->
      </span>
    </el-dialog>
    <!-- 队列添加记录 -->
    <el-dialog
      title="初筛队列数据添加记录"
      :visible.sync="showHistory"
      width="800px"
      @close="showHistory = false">
      <div class="search-box">
        <el-button type="primary" plain size="small" @click="getHistoryList"
          >刷新添加状态</el-button
        >
      </div>
      <el-table :data="historyList" border stripe>
        <el-table-column prop="searchType" label="添加方式" align="center">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.row.searchType == 1">大数据查询</span>
              <span v-else-if="scope.row.searchType == 2">高级查询</span>
              <span v-else-if="scope.row.searchType == 3">患者编号查询</span>
              <span v-else-if="scope.row.searchType == 4">韦恩图查询</span>
              <span v-else-if="scope.row.searchType == 6">事件查询</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="addStatus" label="添加状态" align="center">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sd_add_status"
              :value="scope.row.addStatus"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="patientTotal"
          label="检索患者数量"
          align="center"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.patientTotal == 0">正在计算中</div>
            <div v-else>{{ scope.row.patientTotal }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="patientAddedTotal"
          :label="
            currentSubjectIsAutoGroup == 1 ? '随机入组数量' : '入组患者数量'
          "
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="添加时间"
          align="center"
          width="160"
        ></el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              v-if="scope.row.searchType != 4"
              size="mini"
              @click="editCondition(scope.row)"
              >筛选条件</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer">
        <el-button type="primary" @click="showHistory = false">关闭</el-button>
      </span>
    </el-dialog>
    <venn-model
      :show-data="showModel"
      :collect-data="subjectAndGroupTreeList"
      @getOpen="getOpenEvent"
      @refreshList="refreshList"
      @getSubjectAndGroupTree="getSubjectAndGroupTree"
    ></venn-model>
    <!-- 点击td上的数字显示历史记录弹窗 -->
    <el-dialog
      title="记录详情"
      :visible.sync="showRecordList"
      width="700px"
      @close="showRecordList = false"
      top="8vh">
      <el-table
        :data="recordList"
        border
        stripe
        height="70vh"
        v-loading="showHistoryLoading"
      >
        <el-table-column
          v-for="col in recordColumns"
          :prop="col.fieldName"
          :key="col.fieldName"
          :label="col.aliseName"
        >
          <template slot-scope="scope">
            <!-- <div v-if="col.dictCode">
              <dict-tag :options="dictList[col.dictCode]" :value="scope.row[col.fieldName]"/>
            </div> -->
            <div>{{ scope.row[col.fieldName] }}</div>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer">
        <el-button type="primary" @click="showRecordList = false"
          >关闭</el-button
        >
      </span>
    </el-dialog>
    <transition
      @before-enter="beforeEnter"
      @enter="enter"
      @after-enter="afterEnter">
      <img
        v-if="addShow"
        src="@/assets/images/disease/excel-icon1.png"
        class="img_js"
        alt=""
      />
    </transition>
    <!-- 选择统计类型弹窗 -->
    <selectStatisticalType
      ref="selectStatisticalType"
      @getTypeValue="getStatisticalTypeValue"
    ></selectStatisticalType>
    <!-- 数据模型导出选择字段弹窗 -->
    <selectExportField
      ref="selectExportField"
      @getList="getExportField"
    ></selectExportField>

    <!-- 设置基线弹窗 -->
    <el-dialog
      title="设置基线"
      :visible.sync="setBaselineAlert"
      width="860px"
      :close-on-click-modal="false"
      @close="setBaselineAlert = false"
      class="set-baseline-alert">
      <div class="container">
         <!-- //     tableName: "",
        //     tableComment: "",
        //     fieldName: "",
        //     fieldComment: "",
        //     condition: "in",
        //     fieldValue: "",
        //     fieldValue2: [],
        //     whichTimes: "3",
        //     traceAnchor: "1",
        //     anchorNum: "30",
        //     anchorUnit: "1",
        //     anchorRange: "1",
        //     onlyInHospital: "0",
        //     baselineType: "",//*事件类型 --固定时间;1--时间事件，2--干预事件*
        //     traceAnchorNext: "",
        //     anchorNumNext: "",
        //     anchorUnitNext: "",
        //     anchorRangeNext: "" -->
        <el-form :model="currentEditBaselineObj" ref="baselineForm" :rules="baselineFormRules" :inline="true">
          <el-form-item label="基线时间类型" prop="baselineType" label-width="130px">
            <template #label>
              <span>基线时间类型</span>
              <el-popover trigger="hover">
                <div style="font-size: 13px;">
                  <div>固定时间：明确的时间点或时间段；</div>
                  <div>干预事件：同时间事件，区别为事件具有干预性质区别，如冠脉手术；</div>
                </div>
                <i slot="reference" class="el-icon-warning-outline"></i>
              </el-popover>
            </template>
            <el-select v-model="currentEditBaselineObj.baselineType" placeholder="请选择事件类型" filterable @change="changeBaselineType">
              <el-option label="固定时间" value="0"></el-option>
              <el-option label="干预事件" value="2"></el-option>
            </el-select>
          </el-form-item>
          <!-- 固定时间 -->
          <div v-if="currentEditBaselineObj.baselineType === '0'">
            <el-form-item label="设定时间范围" size="normal" :required="true" label-width="130px">
              <template #label>
                <span>设定时间范围</span>
                <el-popover trigger="hover">
                  <div style="font-size: 13px;">
                    <div>等于：具体的时间，选择一个时间点；</div>
                    <div>时间段内：研究变量的时间在选定的时间范围内；</div>
                    <div>时间段外：研究变量的时间不在选定的时间范围内；</div>
                    <div>早于：在时间点之前；</div>
                    <div>早于等于（不晚于）：在时间点之前包含该时间点；</div>
                    <div>晚于：在时间点之后；</div>
                    <div>晚于等于（不早于）：在时间点之后包含该时间点。</div>
                  </div>
                  <i slot="reference" class="el-icon-warning-outline"></i>
                </el-popover>
              </template>
              <el-form-item label="" size="normal" label-width="0" prop="fieldComment">
                <el-input class="set-w3" placeholder="点击加号选择字段" :readonly="true" v-model="currentEditBaselineObj.fieldComment">
                  <el-button slot="append" icon="el-icon-plus" @click="addVariate(3, currentEditBaselineObj)"></el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="" size="normal" label-width="0" prop="condition">
                <el-select class="set-w" v-model="currentEditBaselineObj.condition" filterable>
                  <el-option v-for="conditionItem in operativeSymbolList" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" size="normal" label-width="0" prop="fieldValue" v-if="currentEditBaselineObj.condition != 'in'">
                <el-date-picker unlink-panels 
                  v-show="currentEditBaselineObj.condition !== 'in'"
                  class="set-w1"
                  v-model="currentEditBaselineObj.fieldValue"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="开始日期"
                  key="date"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="" size="normal" label-width="0" prop="fieldValue2" v-else>
                <el-date-picker unlink-panels 
                  :style="currentEditBaselineObj.condition != 'in' ? 'display:none !important' : '' "
                  class="set-w1"
                  v-model="currentEditBaselineObj.fieldValue2"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  value-format="yyyy-MM-dd"
                  end-placeholder="结束日期"
                  @change="changeConditionBaseline($event, currentEditBaselineObj)"
                  key="daterange"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="" size="normal" label-width="0" prop="whichTimes">
                <el-select class="set-w whichTimes" v-model="currentEditBaselineObj.whichTimes" filterable>
                  <el-option v-for="conditionItem in whichTimesOptions" v-if="conditionItem.value != 1" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                </el-select>
              </el-form-item>
            </el-form-item>
            <el-form-item label="设定浮动时间" size="normal" label-width="130px">
              <el-form-item label="" size="normal" label-width="0" >
                <el-select class="set-w" v-model="currentEditBaselineObj.traceAnchor" filterable>
                  <el-option v-for="conditionItem in traceAnchorOptions" v-if="conditionItem.value != 3 && conditionItem.value != 4" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" size="normal" label-width="0" >
                <el-input class="set-w" v-model="currentEditBaselineObj.anchorNum" clearable placeholder="请输入" type="number" min="0"></el-input>
              </el-form-item>
              <el-form-item label="" size="normal" label-width="0">
                <el-select class="set-w" v-model="currentEditBaselineObj.anchorUnit" filterable>
                  <el-option v-for="conditionItem in anchorUnitOptions" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                </el-select>
              </el-form-item>
              <span class="text-span">内</span>
            </el-form-item>
          </div>
          <!-- 时间事件 -->
          <div v-if="currentEditBaselineObj.baselineType == 1">
            <el-form-item label="设定时间事件" size="normal" :required="true">
              <el-form-item label="" size="normal" label-width="0" prop="whichTimes">
                <el-select class="set-w whichTimes" v-model="currentEditBaselineObj.whichTimes" filterable>
                  <el-option v-for="conditionItem in whichTimesOptions" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" size="normal" label-width="0" prop="eventName">
                <el-input class="set-w1" placeholder="点击加号选择字段" :readonly="true" v-model="currentEditBaselineObj.eventName">
                  <el-button slot="append" icon="el-icon-plus" @click="addVariate(4, currentEditBaselineObj)"></el-button>
                </el-input>
              </el-form-item>
            </el-form-item>
            <el-form-item label="设定时间范围" size="normal" :required="true">
              <div class="set-mar-b-10">
                <span class="text-span">事件</span>
                <el-form-item label="" size="normal" label-width="0" prop="traceAnchor">
                  <el-select class="set-w" v-model="currentEditBaselineObj.traceAnchor" filterable>
                    <el-option v-for="conditionItem in traceAnchorOptions" v-if="conditionItem.value != 5" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" size="normal" label-width="0" prop="anchorNum">
                  <el-input class="set-w" v-model="currentEditBaselineObj.anchorNum" clearable placeholder="请输入" type="number"></el-input>
                </el-form-item>
                <el-form-item label="" size="normal" label-width="0" prop="anchorUnit">
                  <el-select class="set-w" v-model="currentEditBaselineObj.anchorUnit" filterable>
                    <el-option v-for="conditionItem in anchorUnitOptions" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                  </el-select>
                </el-form-item>
                <span class="text-span">内</span>
              </div>
              <div>
                <span class="text-span">事件</span>
                <el-form-item label="" size="normal" label-width="0">
                  <el-select class="set-w" v-model="currentEditBaselineObj.traceAnchorNext" filterable>
                    <el-option v-for="conditionItem in traceAnchorOptions" v-if="conditionItem.value != 5"
                    :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"
                    :disabled="((currentEditBaselineObj.traceAnchor == 1 || currentEditBaselineObj.traceAnchor == 3) && (conditionItem.value != 2 && conditionItem.value != 4)) ||
                    ((currentEditBaselineObj.traceAnchor == 2 || currentEditBaselineObj.traceAnchor == 4) && (conditionItem.value != 1 && conditionItem.value != 3))"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" size="normal" label-width="0">
                  <el-input class="set-w" v-model="currentEditBaselineObj.anchorNumNext" clearable placeholder="请输入" type="number"></el-input>
                </el-form-item>
                <el-form-item label="" size="normal" label-width="0">
                  <el-select class="set-w" v-model="currentEditBaselineObj.anchorUnitNext" filterable>
                    <el-option v-for="conditionItem in anchorUnitOptions" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                  </el-select>
                </el-form-item>
                <span class="text-span">内</span>
              </div>
            </el-form-item>
          </div>
          <!-- 干预事件 -->
          <div v-if="currentEditBaselineObj.baselineType == 2">
            <el-form-item label="设定干预事件" size="normal" :required="true" label-width="130px">
              <el-form-item label="" size="normal" label-width="0" prop="whichTimes">
                <el-select class="set-w whichTimes" v-model="currentEditBaselineObj.whichTimes" filterable>
                  <el-option v-for="conditionItem in whichTimesOptions" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" size="normal" label-width="0" prop="eventName">
                <el-input class="set-w1" placeholder="点击加号选择字段" :readonly="true" v-model="currentEditBaselineObj.eventName">
                  <el-button slot="append" icon="el-icon-plus" @click="addVariate(5, currentEditBaselineObj)"></el-button>
                </el-input>
              </el-form-item>
              <template v-if="currentEditBaselineObj.eventType != '1'">
                <el-form-item label="" size="normal" label-width="0" prop="condition">
                  <el-select class="set-w" v-model="currentEditBaselineObj.condition" filterable>
                    <el-option v-for="conditionItem in conditionList" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                  </el-select>
                </el-form-item>
                <!-- 检验项目名  检验项目代码 -->
                <el-form-item label="" size="normal" label-width="0" prop="fieldValue" v-if="currentEditBaselineObj.tableName == 'sd_chemcheck_doc' && (currentEditBaselineObj.fieldName == 'item_name' || currentEditBaselineObj.fieldName == 'item_code')">
                  <el-select class="set-w3"  v-model="currentEditBaselineObj.fieldValue" v-if="currentEditBaselineObj.fieldName == 'item_name'" clearable filterable size="medium">
                    <el-option :label="oItem.itemName" :value="oItem.itemName" v-for="(oItem, oIndex) in chemCheckList" :key="oIndex"></el-option>
                  </el-select>
                  <el-select class="set-w3"  v-model="currentEditBaselineObj.fieldValue" v-else-if="currentEditBaselineObj.fieldName == 'item_code'" clearable filterable size="medium">
                    <el-option :label="oItem.itemCode" :value="oItem.itemCode" v-for="(oItem, oIndex) in chemCheckList" :key="oIndex"></el-option>
                  </el-select>
                </el-form-item>
                <!-- 检验子项目名 检验子项目代码 -->
                <el-form-item label="" size="normal" label-width="0" prop="fieldValue2" v-else-if="currentEditBaselineObj.tableName == 'sd_chemcheck_doc' && (currentEditBaselineObj.fieldName == 'subitem_name' || currentEditBaselineObj.fieldName == 'subitem_code')">
                  <el-cascader
                      class="set-w3"
                      size="medium"
                      v-model="currentEditBaselineObj.fieldValue2"
                      placeholder="请选择"
                      filterable
                      :options="chemCheckList"
                      :props="currentEditBaselineObj.fieldName == 'subitem_name' ? props : propsCode"
                      :before-filter="cascaderFilter"
                      :filter-method="filterNode"
                      @focus="cascaderFocus(currentEditBaselineObj)"
                  ></el-cascader>
                </el-form-item>
                <!-- 检查项目名  检查项目代码 -->
                <el-form-item label="" size="normal" label-width="0" prop="fieldValue" v-else-if="currentEditBaselineObj.tableName == 'sd_techcheck_doc' && (currentEditBaselineObj.fieldName == 'item_name' || currentEditBaselineObj.fieldName == 'check_item')">
                  <el-select class="set-w3"  v-model="currentEditBaselineObj.fieldValue" v-if="currentEditBaselineObj.fieldName == 'item_name'" clearable filterable size="medium">
                    <el-option :label="oItem.itemName" :value="oItem.itemName" v-for="(oItem, oIndex) in techCheckList" :key="oIndex"></el-option>
                  </el-select>
                  <el-select class="set-w3"  v-model="currentEditBaselineObj.fieldValue" v-else-if="currentEditBaselineObj.fieldName == 'check_item'" clearable filterable size="medium">
                    <el-option :label="oItem.itemCode" :value="oItem.itemCode" v-for="(oItem, oIndex) in techCheckList" :key="oIndex"></el-option>
                  </el-select>
                </el-form-item>
                <!-- 检查子项目名 检查子项目代码 -->
                <el-form-item label="" size="normal" label-width="0" prop="fieldValue2" v-else-if="currentEditBaselineObj.tableName == 'sd_techcheck_doc' && (currentEditBaselineObj.fieldName == 'subitem_name' || currentEditBaselineObj.fieldName == 'check_subitem')">
                  <el-cascader
                      class="set-w3"
                      size="medium"
                      v-model="currentEditBaselineObj.fieldValue2"
                      placeholder="请选择"
                      filterable
                      :options="techCheckList"
                      :props="currentEditBaselineObj.fieldName == 'subitem_name' ? props : propsCode"
                      :before-filter="cascaderFilter"
                      :filter-method="filterNode"
                      @focus="cascaderFocus(currentEditBaselineObj)"
                  ></el-cascader>
                </el-form-item>
                <el-form-item label="" size="normal" label-width="0" prop="fieldValue" v-else>
                   <el-input class="set-w3" v-model="currentEditBaselineObj.fieldValue" clearable placeholder="请输入"></el-input>
                </el-form-item>
              </template>
            </el-form-item>
            <el-form-item label="设定时间范围" size="normal" :required="true" label-width="130px">
              <template #label>
                <span>设定时间范围</span>
                <el-popover trigger="hover">
                  <div>
                    <img style="width: 600px;" src="@/assets/images/b4e46f293803.png"/>
                  </div>
                  <i slot="reference" class="el-icon-warning-outline"></i>
                </el-popover>
              </template>
              <div class="set-mar-b-10">
                <span class="text-span">事件</span>
                <el-form-item label="" size="normal" label-width="0" prop="traceAnchor">
                  <el-select class="set-w" v-model="currentEditBaselineObj.traceAnchor" filterable>
                    <el-option v-for="conditionItem in traceAnchorOptions" v-if="conditionItem.value != 5" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" size="normal" label-width="0" prop="anchorNum">
                  <el-input class="set-w" v-model="currentEditBaselineObj.anchorNum" clearable placeholder="请输入" type="number" min="0"></el-input>
                </el-form-item>
                <el-form-item label="" size="normal" label-width="0" prop="anchorUnit">
                  <el-select class="set-w" v-model="currentEditBaselineObj.anchorUnit" filterable>
                    <el-option v-for="conditionItem in anchorUnitOptions" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                  </el-select>
                </el-form-item>
                <span class="text-span">内</span>
              </div>
              <div>
                <span class="text-span">事件</span>
                <el-form-item label="" size="normal" label-width="0">
                  <el-select class="set-w" v-model="currentEditBaselineObj.traceAnchorNext" filterable>
                    <el-option v-for="conditionItem in traceAnchorOptions" v-if="conditionItem.value != 5"
                    :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"
                    :disabled="((currentEditBaselineObj.traceAnchor == 1 || currentEditBaselineObj.traceAnchor == 3) && (conditionItem.value != 2 && conditionItem.value != 4)) ||
                    ((currentEditBaselineObj.traceAnchor == 2 || currentEditBaselineObj.traceAnchor == 4) && (conditionItem.value != 1 && conditionItem.value != 3))"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" size="normal" label-width="0">
                   <el-input class="set-w" v-model="currentEditBaselineObj.anchorNumNext" clearable placeholder="请输入" type="number" min="0"></el-input>
                </el-form-item>
                <el-form-item label="" size="normal" label-width="0">
                  <el-select class="set-w" v-model="currentEditBaselineObj.anchorUnitNext" filterable>
                    <el-option v-for="conditionItem in anchorUnitOptions" :key="conditionItem.value" :label="conditionItem.label" :value="conditionItem.value"></el-option>
                  </el-select>
                </el-form-item>
                <span class="text-span">内</span>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer">
        <el-button @click="setBaselineAlert = false">取消</el-button>
        <el-button type="primary" @click="saveBaseline">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import selectField from "../components/selectField";
import selectExportField from "./selectExportField";
import { listSubject } from "@/api/disease/subject";
import {
  getCasegroup,
  quickCustomSearch,
  customSearch,
  patientIdCustomSearch,
  quickCustomExport,
  getCaseGroupInfo,
  listCasegroup,
  selectVariableDetails,
  createStatisticsFile,
} from "@/api/disease/casegroup";
import {
  customExport1,
  customExport2,
  customExport3,
  getExportRecord,
  delExportRecordFile,
  delUser,
  saveResearchQueue,
  saveResearchQueueAll,
  addCount
} from "@/api/disease/caseManage";
import { downloadFile } from "@/utils/request";
import {
  selectCheckItemList,
  selectParentList,
} from "@/api/system/checkortest";
import { mapGetters } from "vuex";
import { getTableFindElementList } from "@/api/system/mddelement";
import vennModel from "./venn.vue";
import { formatDate } from "@/utils/index";
import { getCurrentDate } from "@/api/disease/common";
import selectStatisticalType from "../components/selectStatisticalType";
import Upload from "../dataAssertManage/upload.vue";
export default {
  name: "Initial",
  dicts: ["sd_download_status", "sd_add_status"],

  data() {
    return {
      showModel: false, //韦恩图工具是否显示
      operativeSymbolList: [
        { label: "范围", value: "in" },
        { label: "等于", value: "=" },
        { label: "大于", value: "&gt;" },
        { label: "小于", value: "&lt;" },
        { label: "大于等于", value: "&gt;=" },
        { label: "小于等于", value: "&lt;=" },
      ],
      conditionList: [
        {label: '包含', value: '1'},
        {label: '不包含', value: '2'},
        {label: '等于', value: '3'},
        {label: '不等于', value: '4'}
      ],
      traceAnchorOptions: [
        { label: "前", value: "1" },
        { label: "后", value: "2" },
        { label: "之前", value: "3" },
        { label: "之后", value: "4" },
        { label: "前后", value: "5" },
      ],
      whichTimesOptions: [
        { label: "当次", value: "0" },
        { label: "首次", value: "2" },
        { label: "末次", value: "3" },
        { label: "任意次", value: "1" }
      ],
      anchorUnitOptions: [
        { label: "日", value: "0" },
        { label: "月", value: "1" },
        { label: "年", value: "2" },
      ],
      loading: false,
      formType: "1", //表格展示类型
      exportFormat: "1", // 导出格式
      addVariateAlertShow: false,
      variateForm: {
        fieldComment: "",
        fieldValue: "",
        fieldType: "custom",
      },
      selectFieldType: 1, //1选择结构化字段 2选择基线字段 3选择时间事件 4选择干预事件
      cgId: null,
      projectData: {
        projectName: "",
        searchType: "1",
      },
      varGroupList: [
        {
          isShow: true,
          name: "患者基本信息",
          // conditionBaseline: {
          //   tableName: "",
          //   tableComment: "",
          //   fieldName: "",
          //   fieldComment: "",
          //   condition: "in",
          //   fieldValue: "",
          //   fieldValue2: [],
          //   whichTimes: "3",
          //   traceAnchor: "1",
          //   anchorNum: "30",
          //   anchorUnit: "1",
          //   anchorRange: "1",
          //   onlyInHospital: '0'
          // },
          variateList: [
            //研究变量列表
            {
              fieldName: "EMPI__00",
              fieldComment: "本系统患者主索引",
              tableComment: "患者基本信息表",
              tableName: "sd_patient_info",
              fieldValue: "",
              whichTimes: "",
            },
            {
              fieldName: "encount_id__00",
              fieldComment: "就诊号",
              tableComment: "患者基本信息表",
              tableName: "sd_patient_info",
              fieldValue: "",
              whichTimes: "",
            },
            {
              fieldName: "pat_name__01",
              fieldComment: "患者姓名",
              tableComment: "患者基本信息表",
              tableName: "sd_patient_info",
              fieldValue: "",
              whichTimes: "",
            },
            {
              fieldName: "gender__02",
              fieldComment: "性别",
              tableComment: "患者基本信息表",
              tableName: "sd_patient_info",
              dictCode: "sd_dict_sex",
              fieldValue: "",
              whichTimes: "",
            },
            {
              fieldName: "birthday__03",
              fieldComment: "出生日期",
              fillingRate: "100%",
              tableComment: "患者基本信息表",
              tableName: "sd_patient_info",
              fieldValue: "",
              whichTimes: "",
            },
          ],
        }
        // {
        //   isShow: true,
        //   conditionBaseline: {
        //     tableName: "",
        //     tableComment: "",
        //     fieldName: "",
        //     fieldComment: "",
        //     condition: "in",
        //     fieldValue: "",
        //     fieldValue2: [],
        //     whichTimes: "3",
        //     traceAnchor: "1",
        //     anchorNum: "30",
        //     anchorUnit: "1",
        //     anchorRange: "1",
        //     onlyInHospital: "0",
        //     baselineType: "",//*事件类型 --固定时间;1--时间事件，2--干预事件*
        //     traceAnchorNext: "",
        //     anchorNumNext: "",
        //     anchorUnitNext: "",
        //     anchorRangeNext: "",
        //     eventName: '',
        //     eventType: ''
        //   },
        // },
      ],
      currentEditVarGroupIndex: null, //当前编辑变量组的下标

      tableColumn1: [], //表格表头
      dataList1: [], //表格1数据
      total1: 0,
      pageNum1: 1,
      pageSize1: 10,

      tableColumnPatientInfo: [], //表格2基本信息表头
      tableColumnOtherInfo: [], //表格2除基本信息外表头
      dataList2: [], //表格2数据
      total2: 0,
      pageNum2: 1,
      pageSize2: 10,

      dictList: {}, //数据字典
      isUnfold: true, // 是否展开

      exportAlertShow: false, //导出弹窗是否显示
      exportType: "1", //导出数据类型
      variableNum: 3, //研究变量数量

      chemCheckList: [], //检验项列表
      techCheckList: [], //检查项列表
      props: {
        value: "itemName",
        label: "itemName",
        children: "children",
        lazy: true,
        lazyLoad: this.getSelectCheckItemList,
      },
      propsCode: {
        value: "itemCode",
        label: "itemCode",
        children: "children",
        lazy: true,
        lazyLoad: this.getSelectCheckItemList,
      },
      downloadUrl1: "", //导出方式1 下载地址
      downloadUrl2: "", //导出方式2 下载地址
      fullscreenLoading: false,
      isShowDownloadListAlert: false,
      fileTotal: 0,
      fileQueryParams: {
        pageNum: 1,
        pageSize: 10,
        exportType: "", //下载弹窗筛选 - 导出方式
      },
      downloadFileList: [], //下载文件列表
      domain: "", //文件拼接的域名
      tableData: {}, //表对象 {"sd_patient_info": [表下面的字段集合]}
      showHistory: false, //队列记录弹窗是否显示
      showHistoryLoading: false,
      historyQuery: {
        pageNum: 1,
        pageSize: 10,
        rgId: "",
      },
      historyList: [], //队列记录列表
      historyTotal: "", //队列记录总数
      selectedUserList: [], //勾选的患者数据
      userListAllSelections: [], //选中的总人数
      subjectAndGroupTreeList: [], //课题实验组数据
      selectGroupText: "", //选择的课题实验组名称
      isShowPopper: false,
      showRecordList: false, //点击td显示弹窗
      recordList: [], //点击某个td获取的记录列表
      recordColumns: [], //点击td显示列表的表头
      exportFieldTableList: [], //点击应用后组装的变量组数据 , 用于点击数字获取记录接口传参使用
      isFirstLoading: true, //是否是第一次加载
      currentDate: "", //服务器返回的当前日期
      addShow: false, //一开始加入导出记录图标隐藏
      startTop: 0, //一开始加入导出记录的显示的位置
      startLeft: 0,
      checkSearchObj: null, //检查检验当前获取焦点的对象
      createStatisticsFileParams: null, //生存统计文件需要的参数
      currentSubjectIsAutoGroup: 0, //当前查看的队列所属课题是否属于随机入组 0:否 1: 是
      setBaselineAlert: false, //设置基线时间弹窗是否显示
      currentEditBaselineObj: {},//当前编辑的基线对象
      baselineFormRules: {
        baselineType: [
          {required: true, message: '请选择事件类型', trigger: 'change'},
        ],
        fieldComment: [
          {required: true, message: '必填项不可为空', trigger: 'change'}
        ],
        fieldValue: [
          {required: true, message: '必填项不可为空', trigger: 'change'},
        ],
        fieldValue2: [
          {required: true, message: '必填项不可为空', trigger: 'change'},
        ],
        condition: [
          {required: true, message: '必填项不可为空', trigger: 'change'},
        ],
        whichTimes: [
         {required: true, message: '必填项不可为空', trigger: 'change'},
        ],
        eventName: [
         {required: true, message: '必填项不可为空', trigger: 'change'},
        ],
        traceAnchor: [
         {required: true, message: '必填项不可为空', trigger: 'change'},
        ],
        anchorNum: [
         {required: true, message: '必填项不可为空', trigger: 'blur'},
        ],
        anchorUnit: [
         {required: true, message: '必填项不可为空', trigger: 'change'},
        ]
      },

      // 局部loading
      mainBoxLoading: false,
      quickCustomSearchParam:{},

      // 导出申请
      desensitizationAuditObject: 'data_export',
      // 队列锁定状态
      lockStatus: 0,

      exportFields: [], // 导出字段
    };
  },
  computed: {
    ...mapGetters(["currentSelectedDisease"]),
    //列表单元格多条数据回显数字处理
    formatTdValueLength() {
      return (row, colName, index, fieldName) => {
        let arr = row[colName == "患者基本信息" ? colName : colName + index];
        let newArr = arr
          .map((item) => {
            return item[fieldName];
          })
          .filter((_) => _);
        return newArr.length > 0 ? newArr[0].split("||").length : 0;
      };
    },
    formatTdValue() {
      return (row, colName, index, fieldName) => {
        let arr = row[colName == "患者基本信息" ? colName : colName + index];
        let newArr = arr.map((item) => {
          return item[fieldName] || "";
        });
        newArr = newArr.length > 0 ? newArr[0].split("||") : [];
        return newArr.length > 1
          ? newArr[0] + "..."
          : newArr.length == 1
          ? newArr[0]
          : "";
      };
    },
    showTip() {
      return (condition) => {
        return (
          condition == "&gt;" ||
          condition == "&gt;=" ||
          condition == "&lt;" ||
          condition == "&lt;="
        );
      };
    },
    showTipValue() {
      return (condition) => {
        if (condition == "&gt;") {
          return "大于选择的日期, 小于当前日期";
        } else if (condition == "&gt;=") {
          return "大于等于选择的日期, 小于当前日期";
        } else if (condition == "&lt;=") {
          return "小于等于选择的日期, 大于选择的日期前一年日期";
        } else {
          return "小于选择的日期, 大于选择的日期前一年日期";
        }
      };
    }
  },
  components: {
    selectField,
    vennModel,
    selectStatisticalType,
    selectExportField,
    Upload
  },
  mounted() {
    // this.$modal.loading();
    this.mainBoxLoading = true
    this.cgId = this.$route.query.cgId || "";
    this.variableNum = this.$route.query.varsTotal || 3;
    if (this.cgId) {
      // this.historyQuery.rgId = this.cgId;
      this.getProjectDetail();
      this.getExportRecordList();
      this.$router.replace({ query: {} });
    }
    this.getSubjectAndGroupTree();
    setTimeout((_) => {
      this.isFirstLoading = false;
    }, 2000);
  },
  activated() {
    if (this.isFirstLoading) return false;
    let cgId = this.$route.query.cgId || "";
    if (cgId) {
      this.$router.replace({ query: {} });
      this.cgId = cgId;
      this.getProjectDetail();
      this.getSubjectAndGroupTree();
    }
  },
  methods: {
    //获取检查检验一级数据
    getSelectParentList() {
      return selectParentList().then((res) => {
        let chemCheckList = res.data.chemCheckList || [];
        let techCheckList = res.data.techCheckList || [];
        this.chemCheckList = chemCheckList;
        this.techCheckList = techCheckList;
      });
    },
    //根据parentId动态获取检查检验二级
    async getSelectCheckItemList(node, resolve) {
      if (node.level == 1) {
        if (!node.data.children) {
          let data = await selectCheckItemList({
            parentId: node.data.dcheckId,
          }).then((res) => {
            res.data.forEach((item) => (item.leaf = true));
            if (node.data.itemType == 1) {
              for (let i = 0; i < this.chemCheckList.length; i++) {
                if (this.chemCheckList[i].dcheckId == node.data.dcheckId) {
                  this.chemCheckList[i].children = res.data;
                }
              }
            } else {
              for (let i = 0; i < this.techCheckList.length; i++) {
                if (this.techCheckList[i].dcheckId == node.data.dcheckId) {
                  this.techCheckList[i].children = res.data;
                }
              }
            }
            return res.data;
          });
          resolve(data);
        } else {
          if(node.children.length > 0) {
              resolve([])
          } else {
              resolve(node.data.children)
          }
          // resolve(node.data.children);
        }
      }
    },
    //此方法用于检查检验条件回显时获取二级数据
    getSelectCheckItemList1(obj, i, type) {
      selectCheckItemList({ parentId: obj.dcheckId }).then((res) => {
        let list = res.data || [];
        list.forEach((item) => (item.leaf = true));
        if (list.length == 0) {
          obj.leaf = true;
        }
        obj.children = list;
        if (type == 1) {
          this.$set(this.chemCheckList, i, obj);
        } else {
          this.$set(this.techCheckList, i, obj);
        }
      });
    },
    //检查检验查询前的钩子
    async cascaderFilter(value) {
      let obj = this.checkSearchObj;
      let elementName = obj.fieldName.split("__")[0];
      if (value.length > 2) {
        let params = {
          itemType: obj.tableName == "sd_chemcheck_doc" ? 1 : 0,
        };
        if (elementName == "item_name" || elementName == "subitem_name") {
          params.itemName = value;
        } else {
          params.itemCode = value;
        }
        await selectCheckItemList(params).then((res) => {
          let objData = {};
          let data = res.data || [];
          data.forEach((item) => {
            if (item.parentId != 0) {
              item.leaf = true;
              if (objData[item.parentId]) {
                objData[item.parentId].push(item);
              } else {
                objData[item.parentId] = new Array(item);
              }
            }
          });
          let arrKey =
            obj.tableName == "sd_chemcheck_doc"
              ? "chemCheckList"
              : "techCheckList";
          for (let k in objData) {
            this[arrKey].forEach((item, index) => {
              if (item.dcheckId == k) {
                this.$set(item, "children", objData[k]);
              }
            });
          }
        });
        return false;
      } else {
        return false;
      }
    },
    cascaderFocus(item) {
      this.checkSearchObj = item;
    },
    //检查检验自定义检索
    filterNode(data, value) {
      if (!value) return true;
      return (
        data.label &&
        data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1
      );
    },
    //获取课题实验组树
    getSubjectAndGroupTree() {
      listSubject({
        diseaseSyscode: this.currentSelectedDisease.diseaseSyscode,
      }).then((response) => {
        let list = [];
        response.rows.forEach((item) => {
          if (item.groupList && item.groupList.length > 0) {
            list.push(item);
          }
        });
        if (this.cgId) {
          list.forEach((subjectItem) => {
            subjectItem.groupList.forEach((groupItem) => {
              if (this.cgId == groupItem.rgId) {
                this.lockStatus = groupItem.lockStatus
                this.selectGroupText = `${subjectItem.subjectName}/${groupItem.groupName}`;
                this.currentSubjectIsAutoGroup = subjectItem.isAutoGroup;
              }
            });
          });
        } else if (list.length > 0) {
          this.cgId = list[0].groupList[0].rgId;
          this.lockStatus = list[0].groupList[0].lockStatus
          // this.historyQuery.rgId = this.cgId;
          this.selectGroupText = `${list[0].subjectName}/${list[0].groupList[0].groupName}`;
          this.currentSubjectIsAutoGroup = list[0].isAutoGroup;
          this.getProjectDetail();
          this.getExportRecordList();
        } else {
          this.mainBoxLoading = false
          // this.$modal.closeLoading();
        }
        this.subjectAndGroupTreeList = list;
      });
    },
    //选择实验组
    selectGroup(row, rgId, rgName) {
      if (rgId == this.cgId) return false;
      this.lockStatus = row.lockStatus
      this.currentSubjectIsAutoGroup = row.isAutoGroup;
      this.cgId = rgId;
      // this.historyQuery.rgId = rgId;
      this.selectGroupText = `${row.subjectName}/${rgName}`;
      this.pageNum1 = 1;
      this.total1 = 0;
      this.dataList1 = [];
      this.userListAllSelections = [];
      this.resetVarGroupList();
      this.getProjectDetail();
      this.getExportRecordList();
      this.isShowPopper = false;
    },
    //获取项目详情
    async getProjectDetail() {
      await this.getSelectParentList();
      getCaseGroupInfo(this.cgId).then((res) => {
        res.data.searchType = 1;
        this.projectData = res.data;
        // this.closePage();
        let num = 0;
        if (res.data.variableGroupJson) {
          let data = JSON.parse(res.data.variableGroupJson);
          let tableNames = [];
          data.forEach((item) => {
            item.isShow = true;
            if (item.variateList && item.variateList.length > 0) {
              item.variateList.forEach((item1) => {
                if (item1.fieldName != "EMPI__00") {
                  num += 1;
                }
                if (item1.dictCode && !this.dictList[item1.dictCode]) {
                  this.getDictData(item1.dictCode);
                }
                if (tableNames.indexOf(item1.tableName) == -1) {
                  tableNames.push(item1.tableName);
                }
                if (item1.fieldValue && item1.fieldValue.length > 0) {
                  if (item1.tableName == "sd_chemcheck_doc") {
                    for (let i = 0; i < this.chemCheckList.length; i++) {
                      if (this.chemCheckList[i].itemName == item1.fieldValue[0]) {
                        this.getSelectCheckItemList1(this.chemCheckList[i], i, 1);
                        break;
                      }
                    }
                  } else {
                    for (let i = 0; i < this.techCheckList.length; i++) {
                      if (this.techCheckList[i].itemName == item1.fieldValue[0]) {
                        this.getSelectCheckItemList1(this.techCheckList[i], i, 2);
                        break;
                      }
                    }
                  }
                }
              });
            }
          });
          this.getTableFindFields(tableNames);
          this.variableNum = num;
          this.varGroupList = data;
        } else {
          let tableNames = [];
          //将变量组重置, 不然会有上次选中的变量数据
          this.varGroupList = [
            {
              isShow: true,
              name: "患者基本信息",
              variateList: [
                //研究变量列表
                {
                  fieldName: "EMPI__00",
                  fieldComment: "本系统患者主索引",
                  tableComment: "患者基本信息表",
                  tableName: "sd_patient_info",
                  fieldValue: "",
                  whichTimes: "",
                },
                {
                  fieldName: "encount_id__00",
                  fieldComment: "就诊号",
                  tableComment: "患者基本信息表",
                  tableName: "sd_patient_info",
                  fieldValue: "",
                  whichTimes: "",
                },
                {
                  fieldName: "pat_name__01",
                  fieldComment: "患者姓名",
                  tableComment: "患者基本信息表",
                  tableName: "sd_patient_info",
                  fieldValue: "",
                  whichTimes: "",
                },
                {
                  fieldName: "gender__02",
                  fieldComment: "性别",
                  tableComment: "患者基本信息表",
                  tableName: "sd_patient_info",
                  dictCode: "sd_dict_sex",
                  fieldValue: "",
                  whichTimes: "",
                },
                {
                  fieldName: "birthday__03",
                  fieldComment: "出生日期",
                  fillingRate: "100%",
                  tableComment: "患者基本信息表",
                  tableName: "sd_patient_info",
                  fieldValue: "",
                  whichTimes: "",
                },
              ],
            }
            // {
            //   isShow: true,
            //   conditionBaseline: {
            //     tableName: "",
            //     tableComment: "",
            //     fieldName: "",
            //     fieldComment: "",
            //     condition: "in",
            //     fieldValue: "",
            //     fieldValue2: [],
            //     whichTimes: "3",
            //     traceAnchor: "1",
            //     anchorNum: "30",
            //     anchorUnit: "1",
            //     anchorRange: "1",
            //     onlyInHospital: "0",
            //   },
            // },
          ];
          this.varGroupList.forEach((item) => {
            if (item.variateList && item.variateList.length > 0) {
              item.variateList.forEach((item1) => {
                if (item1.fieldName != "EMPI__00") {
                  num += 1;
                }
                if (item1.dictCode && !this.dictList[item1.dictCode]) {
                  this.getDictData(item1.dictCode);
                }
                if (tableNames.indexOf(item1.tableName) == -1) {
                  tableNames.push(item1.tableName);
                }
              });
            }
          });
          this.getTableFindFields(tableNames);
          this.variableNum = num;
        }
        if (!this.currentDate) {
          getCurrentDate().then((res) => {
            let currentDate = formatDate(res.data);
            this.currentDate = currentDate.substr(0, 10);
            this.useVariate();
          });
        } else {
          this.useVariate();
        }
      });
    },
    //获取表下面的字段列表 - 用于设置回显字段的填充率
    getTableFindFields(tableNames) {
      tableNames.forEach((tableName) => {
        getTableFindElementList({ tableName: tableName, searchFlag: 1 }).then(
          (res) => {
            let list = res.data || [];
            list.forEach((fieldItem) => {
              this.varGroupList.forEach((groupItem) => {
                if (groupItem && groupItem.variateList) {
                  groupItem.variateList.forEach((variateItem) => {
                    if (
                      variateItem.tableName == tableName &&
                      (fieldItem.elementName == variateItem.fieldName ||
                        fieldItem.elementName ==
                          variateItem.fieldName.split("__")[0])
                    ) {
                      this.$set(variateItem, "fillRate", fieldItem.fillRate);
                    }
                  });
                }
              });
            });
          }
        );
      });
    },

    //获取数据字典
    getDictData(dictCode) {
      this.getDicts(dictCode).then((res) => {
        res.data.forEach((item) => {
          item.label = item.dictLabel;
          item.value = item.dictValue;
          item.raw = { listClass: null };
        });
        this.$set(this.dictList, dictCode, res.data);
      });
    },
    //type: 1添加库内变量 2:添加自定义变量 3:选择基线时间 4选择时间事件 5选择干预事件
    addVariate(type, index) {
      if(type == 1) {
        this.currentEditVarGroupIndex = index;
      }
      if (type == 1) {
        let bool = index == 0 ? true : false;
        this.selectFieldType = 1;
        this.$nextTick((_) => {
          this.$refs.selectField.init(bool);
        });
      } else if (type == 2) {
        this.addVariateAlertShow = true;
        this.$nextTick((_) => {
          this.$refs.variateForm.resetFields();
        });
      } else if (type == 3) {
        this.selectFieldType = 2;
        this.$nextTick((_) => {
          this.$refs.selectField.init();
        });
      }else if (type == 4) {
        this.selectFieldType = 3;
        this.$nextTick((_) => {
          this.$refs.selectField.init();
        });
      }else if (type == 5) {
        this.selectFieldType = 4;
        this.$nextTick((_) => {
          this.$refs.selectField.init();
        });
      }
    },
    //保存自定义变量
    sureAdd() {
      this.$refs.variateForm.validate((valid) => {
        if (valid) {
          let obj = JSON.parse(JSON.stringify(this.variateForm));
          this.varGroupList[this.currentEditVarGroupIndex].variateList.push(
            obj
          );
          this.addVariateAlertShow = false;
        }
      });
    },
    //获取选中的变量
    getField(e) {
      console.log(e);
      if (this.selectFieldType == 1) {
        e.forEach((item) => {
          this.varGroupList[this.currentEditVarGroupIndex].variateList.push({
            fieldName: item.elementName,
            tableComment: item.tableComment,
            tableName: item.tableName,
            fieldComment: item.aliasName,
            dictCode: item.dictCode,
            fieldValue: "",
            whichTimes: "",
            baselineFieldName: item.baselineFieldName,
            computeFlag: item.computeFlag || "",
            fillRate: item.fillRate,
            randomKey: item.randomKey || "",
            fieldType: item.elementType || ''
          });
          if (item.dictCode && !this.dictList[item.dictCode]) {
            this.getDictData(item.dictCode);
          }
        });
      } else {
        let obj = {
          tableName: e.tableName,
          tableComment: e.tableComment,
          fieldName: e.elementName,
          fieldComment: e.aliasName,
          fieldValue: "",
          fieldValue2: [],
          eventName: e.eventName || '',
          eventType: e.eventType || '',
          baseLineFieldName: e.baselineFieldName
        };
        this.currentEditBaselineObj = Object.assign(this.currentEditBaselineObj, obj);
        // this.$set(this.varGroupList[this.currentEditVarGroupIndex], "conditionBaseline",obj);
      }
    },
    //时间范围选择的时候进行判断是否大于一年
    changeConditionBaseline(e, conditionBaseline) {
      if (e && e.length > 0) {
        let startDate = new Date(e[0]);
        let endDate = new Date(e[1]);
        let startYear = startDate.getFullYear();
        let startMonth = startDate.getMonth() + 1;
        let startDay = startDate.getDate();
        let endYear = endDate.getFullYear();
        let endMonth = endDate.getMonth() + 1;
        let endDay = endDate.getDate();
        let sumD = endDay - startDay;
        let sumM = endMonth - startMonth;
        let sumY = endYear - startYear;
        if (sumD < 0) sumM--;
        if (sumM < 0) sumY--;
        if (sumY > 0) {
          conditionBaseline.fieldValue2 = [];
          this.$message.error("基线日期范围不能超过一年!");
        }
      } else {
        conditionBaseline.fieldValue2 = [];
      }
    },
    //删除变量
    delVariate(scope, index) {
      if (index == 0) {
        this.varGroupList[index].variateList.splice(scope.$index + 1, 1);
      } else {
        this.varGroupList[index].variateList.splice(scope.$index, 1);
      }
    },
    //删除基线时间字段
    delBaseLineField(index) {
      this.varGroupList[index].conditionBaseline = {};
      this.varGroupList.forEach((item) => {
        if (item.variateList && item.variateList.length > 0) {
          item.variateList.forEach((fieldItem) => {
            if (fieldItem.whichTimes) {
              fieldItem.whichTimes = "";
            }
          });
        }
      });
    },
    //添加变量组
    addVarGroup() {
      let obj = {
        name: "变量组",
        isShow: true,
        // conditionBaseline: {
        //   tableName: "",
        //   tableComment: "",
        //   fieldName: "",
        //   fieldComment: "",
        //   condition: "in",
        //   fieldValue: "",
        //   fieldValue2: [],
        //   whichTimes: "3",
        //   traceAnchor: "1",
        //   anchorNum: "30",
        //   anchorUnit: "1",
        //   anchorRange: "1",
        //   onlyInHospital: '0'
        // },
        conditionBaseline: {
            tableName: "",
            tableComment: "",
            fieldName: "",
            fieldComment: "",
            condition: "",
            fieldValue: "",
            fieldValue2: [],
            whichTimes: "",
            traceAnchor: "",
            anchorNum: "",
            anchorUnit: "",
            anchorRange: "1",
            onlyInHospital: "0",
            baselineType: "0",
            traceAnchorNext: "",
            anchorNumNext: "",
            anchorUnitNext: "",
            anchorRangeNext: "1",
            eventName: '',
            eventType: ''
        },
        variateList: [],
      };
      this.varGroupList.push(obj);
    },
    changeBaselineType(e) {
      this.currentEditBaselineObj = {
        tableName: "",
        tableComment: "",
        fieldName: "",
        fieldComment: "",
        condition: "",
        fieldValue: "",
        fieldValue2: [],
        whichTimes: "",
        traceAnchor: "",
        anchorNum: "",
        anchorUnit: "",
        anchorRange: "1",
        onlyInHospital: "0",
        baselineType: e,
        traceAnchorNext: "",
        anchorNumNext: "",
        anchorUnitNext: "",
        anchorRangeNext: "1",
        eventName: '',
        eventType: ''
      }
      this.$refs.baselineForm.clearValidate()
    },
    //删除变量组
    delGroup(index) {
      this.varGroupList.splice(index, 1);
    },
    //设置变量组内的基线
    setBaseline(index, item) {
      this.currentEditVarGroupIndex = index;
      this.currentEditBaselineObj = JSON.parse(JSON.stringify(item.conditionBaseline))
      this.setBaselineAlert = true;
    },
    //保存设置的基线
    saveBaseline() {
      this.$refs.baselineForm.validate((valid) => {
        if(valid) {
          let obj = this.currentEditBaselineObj;
          let text = '';
          let whichTimes = '';
          let traceAnchor = '';
          let anchorUnit = '';
          let traceAnchorNext = '';
          let anchorUnitNext = '';
          if(obj.whichTimes) {
            whichTimes = this.dictValTransLabel(obj.whichTimes, this.whichTimesOptions);
          }
          if(obj.traceAnchor) {
            traceAnchor = this.dictValTransLabel(obj.traceAnchor, this.traceAnchorOptions);
          }
          if(obj.anchorUnit) {
            anchorUnit = this.dictValTransLabel(obj.anchorUnit, this.anchorUnitOptions);
          }
          if(obj.traceAnchorNext) {
            traceAnchorNext = this.dictValTransLabel(obj.traceAnchorNext, this.traceAnchorOptions);
          }
          if(obj.anchorUnitNext) {
            anchorUnitNext = this.dictValTransLabel(obj.anchorUnitNext, this.anchorUnitOptions);
          }
          if(obj.baselineType == 0 && obj.fieldName) {
            let condition = '';
            if(obj.condition) {
             condition = this.dictValTransLabel(obj.condition, this.operativeSymbolList);
            }
            if(obj.condition == 'in') {
              text = `${obj.fieldComment}${obj.fieldValue2[0]}至${obj.fieldValue2[1]}范围内${whichTimes}`
            }else {
              text = `${obj.fieldComment}${condition}${obj.fieldValue}${whichTimes}`
            }
            if(obj.traceAnchor && obj.anchorNum && obj.anchorUnit) {
              text += `，${traceAnchor}${obj.anchorNum}${anchorUnit}内`
            }
          } else if(obj.baselineType == 1 && obj.fieldName) {
            text = `${whichTimes}${obj.eventName}在事件${traceAnchor}${obj.anchorNum}${anchorUnit}内`
            if(obj.traceAnchorNext && obj.anchorNumNext && obj.anchorUnitNext) {
              text +=  `和事件${traceAnchorNext}${obj.anchorNumNext}${anchorUnitNext}内`
            }
          }else {
            let condition = '';
            if(obj.condition) {
             condition = this.dictValTransLabel(obj.condition, this.conditionList);
            }
            let val = '';
            if(obj.fieldValue2 && obj.fieldValue2.length > 0) {
              val = obj.fieldValue2[1];
            } else {
              val = obj.fieldValue
            }
            text = `${whichTimes}${obj.eventName}${condition}${val}在事件${traceAnchor}${obj.anchorNum}${anchorUnit}内`
            if(obj.traceAnchorNext && obj.anchorNumNext && obj.anchorUnitNext) {
              text +=  `和事件${traceAnchorNext}${obj.anchorNumNext}${anchorUnitNext}内`
            }
          }
          this.$set(this.varGroupList[this.currentEditVarGroupIndex], 'baselineDescribe', text);
          this.varGroupList[this.currentEditVarGroupIndex].conditionBaseline = Object.assign(this.varGroupList[this.currentEditVarGroupIndex].conditionBaseline, this.currentEditBaselineObj);
          this.setBaselineAlert = false;
        }
      })
    },
    //字典值转换成文本
    dictValTransLabel(val, dictList) {
      let obj = dictList.filter(item => item.value == val);
      return obj[0].label;
    },
    //重置组
    resetGroup() {},
    changeTableType(type) {
      if (type == 1) {
        if (this.dataList1.length == 0) {
          this.useVariate();
        }
      } else if (type == 2) {
        if (this.dataList2.length == 0) {
          this.useVariate();
        }
      }
    },
    //点击应用
    useVariate(eventSource) {
      let params = {
        exportFieldTableList: [],
        variableGroupJson: JSON.stringify(this.varGroupList),
        // conditionBaseline: {},
        rgId: this.cgId,
        formType: this.formType,
      };
      let varGroupList = JSON.parse(JSON.stringify(this.varGroupList));
      //判断变量组是否大于2 , 并且判断大于2的变量组中是否选择了字段, 如存在字段必须要选择基线时间 ,导出需要用到
      // if (varGroupList.length > 2) {
      //   let isExistsField = varGroupList.filter((item, index) => {
      //     if (index > 1) {
      //       return item.variateList.length > 0;
      //     }
      //   });
      //   if ((isExistsField.length > 0 && varGroupList[1].conditionBaseline.condition == "in" &&
      //       varGroupList[1].conditionBaseline.fieldValue2.length < 2) || (varGroupList[1].conditionBaseline.condition != "in" &&
      //       !varGroupList[1].conditionBaseline.fieldValue)
      //   ) {
      //     this.$confirm(`请先在变量组中选择基线时间后进行查询`, "提示", {
      //       confirmButtonText: "确定",
      //       type: "info",
      //       showCancelButton: false,
      //       closeOnClickModal: false,
      //     }).then((_) => {})
      //       .catch((_) => {});
      //     return false;
      //   }
      // }
      // this.$modal.loading();
      this.mainBoxLoading = true
      this.loading = true;
      if (this.formType == 1) {
        let arr = [];
        let num = 0;
        try {
          varGroupList.forEach((item, index) => {
            let conditionBaseline = {};
            if (item.conditionBaseline && item.conditionBaseline.fieldName) {
              conditionBaseline = JSON.parse(JSON.stringify(item.conditionBaseline));
              conditionBaseline.onlyInHospital = item.conditionBaseline.onlyInHospital == 1 ? true : false;
              if(conditionBaseline.baselineType === '0') {
                if (conditionBaseline.condition == "in") {
                  if (item.conditionBaseline.fieldValue2 && item.conditionBaseline.fieldValue2.length == 2) {
                    conditionBaseline.fieldValue = item.conditionBaseline.fieldValue2[0] + " 00:00:00";
                    conditionBaseline.fieldValue2 = item.conditionBaseline.fieldValue2[1] + " 23:59:59";
                  } else {
                    conditionBaseline.fieldValue2 = "";
                  }
                } else if (conditionBaseline.condition == "&gt;" || conditionBaseline.condition == "&gt;=") {
                  // if (this.currentDate > conditionBaseline.fieldValue) {
                  //   conditionBaseline.fieldValue2 = this.currentDate;
                  // } else {
                  //   conditionBaseline.fieldValue2 = conditionBaseline.fieldValue;
                  //   conditionBaseline.fieldValue = this.currentDate;
                  // }
                  conditionBaseline.fieldValue2  = ''
                } else if (conditionBaseline.condition == "&lt;" || conditionBaseline.condition == "&lt;=") {
                  // let selectDate = new Date(conditionBaseline.fieldValue);
                  // let prevYearDate = ((selectDate) => {
                  //   let year = selectDate.getFullYear() - 1;
                  //   let month = String(selectDate.getMonth() + 1);
                  //   let day = String(selectDate.getDate());
                  //   return `${year}-${month.padStart(2, 0)}-${day.padStart(2, 0)}`;
                  // })(selectDate);
                  // conditionBaseline.fieldValue2 = conditionBaseline.fieldValue;
                  // conditionBaseline.fieldValue = prevYearDate;
                  conditionBaseline.fieldValue2  = ''
                } else {
                  conditionBaseline.fieldValue2 = "";
                }
              } else if(conditionBaseline.baselineType === '1') {
                conditionBaseline.fieldValue2 = "";
              } else {
                if(conditionBaseline.tableName == 'sd_chemcheck_doc' && (conditionBaseline.fieldName == 'subitem_name' || conditionBaseline.fieldName == 'subitem_code')) {
                  conditionBaseline.fieldValue = item.conditionBaseline.fieldValue2.pop() || '';
                  conditionBaseline.fieldValue2 = ''
                } else if(conditionBaseline.tableName == 'sd_techcheck_doc' && (conditionBaseline.fieldName == 'subitem_name' || conditionBaseline.fieldName == 'check_subitem')) {
                  conditionBaseline.fieldValue = item.conditionBaseline.fieldValue2.pop() || '';
                  conditionBaseline.fieldValue2 = ''
                } else {
                  conditionBaseline.fieldValue2 = ''
                }
              }
            } else if(index > 0){
              this.mainBoxLoading = false
              // this.$modal.closeLoading();
              this.loading = false;
              this.$confirm(`请在变量组${index}中设置基线后进行查询`, "提示", {
                  confirmButtonText: "确定",
                  type: "info",
                  showCancelButton: false,
                  closeOnClickModal: false,
                }).then((_) => {})
                  .catch((_) => {});
                throw new Error("");
            }
            // params.conditionBaseline = conditionBaseline;

            let obj = {
              variableGroup: index == 0 ? "患者基本信息" : "变量组" + index,
              exportFieldList: [],

            };
            if(index > 0) {
              obj.conditionBaseline = conditionBaseline;
            }
            item.variateList.forEach((item1, index1) => {
              if (item1.fieldType == "custom") {
                obj.exportFieldList.push(item1);
              } else if (item1.fieldName) {
                let fieldName = item1.fieldName.split("__")[0];
                item1.fieldName = `${fieldName}__${index}${index1}`;
                //将变量组里面的字段名一并修改, 不然列表根据变量组中的字段名取不到值
                this.varGroupList[index].variateList[index1].fieldName = `${fieldName}__${index}${index1}`;
                obj.exportFieldList.push(item1);
              }
              if (item1.tableName == "sd_chemcheck_doc" && (item1.fieldName.split("__")[0] == "subitem_name" || item1.fieldName.split("__")[0] == "subitem_code")) {
                item1.fieldValue = item1.fieldValue.length > 1 ? item1.fieldValue[1] : "";
              } else if (item1.tableName == "sd_techcheck_doc" && (item1.fieldName.split("__")[0] == "subitem_name" || item1.fieldName.split("__")[0] == "check_subitem")) {
                if (item1.fieldName.split("__")[0] == "check_subitem") {
                  let code = item1.fieldValue.length > 1 ? item1.fieldValue[1] : "";
                  if (code) {
                    code = code.split("-")[0];
                  }
                  item1.fieldValue = code;
                } else {
                  item1.fieldValue = item1.fieldValue.length > 1 ? item1.fieldValue[1] : "";
                }
              }
              if (item1.fieldName != "EMPI__00") {
                num += 1;
              }
            });
            // if(obj.exportFieldList.length > 0) {
            arr.push(obj);
            // }
          });
        } catch (e) {
          return false;
        }
        this.variableNum = num;
        params.varsTotal = num;
        params.exportFieldTableList = arr;
      } else if (this.formType == 2) {
        let obj = {};
        varGroupList.forEach((item) => {
          item.variateList.forEach((item1) => {
            if (!obj[item1.tableName]) {
              let data = {
                aliasName: item1.tableComment,
                tableComment: item1.tableComment,
                tableName: item1.tableName,
                exportFieldList: [item1],
              };
              obj[item1.tableName] = data;
            } else {
              obj[item1.tableName].exportFieldList.push(item1);
            }
          });
        });
        let arr = Object.values(obj);
        params.exportFieldTableList = arr;
      }
      params.variableGroupJson = JSON.stringify(this.varGroupList);
      //如果是点击变量组下面的应用按钮 , 或删除患者重新获取数据时把选中的用户清空 分页设置为第一页
      if (eventSource == "btn") {
        this[`pageNum${this.formType}`] = 1;
        this.userListAllSelections = [];
      }
      if (this.projectData.searchType == 1) {
        // params.elasticsearchVO = JSON.parse(
        //   this.projectData.conditionSearchJson
        // );
        params.elasticsearchVO = {};
        params.elasticsearchVO.page = this[`pageNum${this.formType}`] - 1;
        params.elasticsearchVO.pageSize = this[`pageSize${this.formType}`];
        this.getList1(params);
      } else if (this.projectData.searchType == 2) {
        params.conditionSearchObject = JSON.parse(
          this.projectData.conditionSearchJson
        );
        params.conditionSearchObject.pageNum = this[`pageNum${this.formType}`];
        params.conditionSearchObject.pageSize =
          this[`pageSize${this.formType}`];
        this.getList2(params);
      } else if (this.projectData.searchType == 3) {
        params.sdPatientInfo = JSON.parse(this.projectData.conditionSearchJson);
        // params.sdPatientInfo.pageNum = this[`pageNum${this.formType}`];
        // params.sdPatientInfo.pageSize = this[`pageSize${this.formType}`];
        params.pageNum = this[`pageNum${this.formType}`];
        params.pageSize = this[`pageSize${this.formType}`];
        this.getList3(params);
      }
      this.createStatisticsFileParams = params;
      this.exportFieldTableList = params.exportFieldTableList;
    },
    //重置
    reset() {},
    //searchType == 1时调用
    getList1(params) {
      this.quickCustomSearchParam = params
      quickCustomSearch(params)
        .then((res) => {
          if (this.formType == 1) {
            this.tableColumn1 = JSON.parse(JSON.stringify(this.varGroupList));
            // console.log('999999999', this.tableColumn1)
            // this.tableColumn1.splice(1, 1);
            this.$nextTick(() => {
              this.$refs.userTable.doLayout();
            });
            this.dataList1 = res.rows || [];
            this.total1 = res.total || 0;
            this.$nextTick(() => {
              this.setSelectRow();
            });
          } else if (this.formType == 2) {
            let exportTable = params.exportFieldTableList;
            let data = res.rows || [];
            let tableColumnOtherInfo = {};
            exportTable.forEach((item, index) => {
              if (item.tableName != "sd_patient_info") {
                if (data && data.length > 0) {
                  data.forEach((item1, index1) => {
                    let length = 0;
                    if (
                      item1[item.tableName] &&
                      item1[item.tableName].length > length
                    ) {
                      length = item1[item.tableName];
                      let arr = [];
                      item1[item.tableName].forEach((item2, index2) => {
                        arr.push({
                          tableName: item.tableComment + (index2 + 1),
                          exportFieldList: item.exportFieldList,
                          index: index2,
                        });
                      });
                      tableColumnOtherInfo[item.tableName] = arr;
                    }
                  });
                }
              }
            });
            //将表字段合并为一个数组,用于表格循环表头
            let formatOtherTableField = [];
            for (let key in tableColumnOtherInfo) {
              formatOtherTableField = [
                ...formatOtherTableField,
                ...tableColumnOtherInfo[key],
              ];
            }
            this.tableColumnPatientInfo = exportTable[0].exportFieldList;
            this.tableColumnOtherInfo = formatOtherTableField;
            this.dataList2 = data;
            this.total2 = res.total || 0;
          }
          this.loading = false;
          this.mainBoxLoading = false
          // this.$modal.closeLoading();
        })
        .catch((_) => {
          this.mainBoxLoading = false
          // this.$modal.closeLoading();
          this.loading = false;
        });
    },

    //searchType == 2时调用
    getList2(params) {
      customSearch(params).then((res) => {
        if (this.formType == 1) {
          this.tableColumn1 = JSON.parse(JSON.stringify(this.varGroupList));
          this.tableColumn1.splice(1, 1);
          this.dataList1 = res.rows || [];
          this.total1 = res.total || 0;
        } else if (this.formType == 2) {
          let exportTable = params.exportFieldTableList;
          let data = res.rows || [];
          let tableColumnOtherInfo = {};
          exportTable.forEach((item, index) => {
            if (item.tableName != "sd_patient_info") {
              if (data && data.length > 0) {
                data.forEach((item1, index1) => {
                  let length = 0;
                  if (
                    item1[item.tableName] &&
                    item1[item.tableName].length > length
                  ) {
                    length = item1[item.tableName];
                    let arr = [];
                    item1[item.tableName].forEach((item2, index2) => {
                      arr.push({
                        tableName: item.tableComment + (index2 + 1),
                        exportFieldList: item.exportFieldList,
                        index: index2,
                      });
                    });
                    tableColumnOtherInfo[item.tableName] = arr;
                  }
                });
              }
            }
          });
          //将表字段合并为一个数组,用于表格循环表头
          let formatOtherTableField = [];
          for (let key in tableColumnOtherInfo) {
            formatOtherTableField = [
              ...formatOtherTableField,
              ...tableColumnOtherInfo[key],
            ];
          }
          this.tableColumnPatientInfo = exportTable[0].exportFieldList;
          this.tableColumnOtherInfo = formatOtherTableField;
          this.dataList2 = data;
          this.total2 = res.total || 0;
        }
        this.loading = false;
      });
    },

    //searchType == 3时调用
    getList3(params) {
      patientIdCustomSearch(params).then((res) => {
        if (this.formType == 1) {
          this.tableColumn1 = JSON.parse(JSON.stringify(this.varGroupList));
          this.tableColumn1.splice(1, 1);
          this.dataList1 = res.rows;
          this.total1 = res.total || 0;
        } else if (this.formType == 2) {
          let exportTable = params.exportFieldTableList;
          let data = res.rows || [];
          let tableColumnOtherInfo = {};
          exportTable.forEach((item, index) => {
            if (item.tableName != "sd_patient_info") {
              if (data && data.length > 0) {
                data.forEach((item1, index1) => {
                  let length = 0;
                  if (
                    item1[item.tableName] &&
                    item1[item.tableName].length > length
                  ) {
                    length = item1[item.tableName];
                    let arr = [];
                    item1[item.tableName].forEach((item2, index2) => {
                      arr.push({
                        tableName: item.tableComment + (index2 + 1),
                        exportFieldList: item.exportFieldList,
                        index: index2,
                      });
                    });
                    tableColumnOtherInfo[item.tableName] = arr;
                  }
                });
              }
            }
          });
          //将表字段合并为一个数组,用于表格循环表头
          let formatOtherTableField = [];
          for (let key in tableColumnOtherInfo) {
            formatOtherTableField = [
              ...formatOtherTableField,
              ...tableColumnOtherInfo[key],
            ];
          }
          this.tableColumnPatientInfo = exportTable[0].exportFieldList;
          this.tableColumnOtherInfo = formatOtherTableField;
          this.dataList2 = data;
          this.total2 = res.total || 0;
        }
        this.loading = false;
      });
    },
    onExport(e) {
      this.desensitizationAuditObject = e
      this.exportAlertShow = true;
    },
    //显示导出弹窗
    showExportAlert() {
      this.exportAlertShow = true;
      // getCasegroup(this.cgId).then(res => {
      //   this.downloadUrl1 = res.data.downloadUrl1;
      //   this.downloadUrl2 = res.data.downloadUrl2;
      // })
    },
    onExportAlertClose() {
      this.exportFields = []
      if (this.$refs.selectExportField) {
        this.$refs.selectExportField.tableList = []
      }
    },
    //
    showSelectExportField() {
      this.$refs.selectExportField.init(this.exportFields);
    },
    getExportField(field) {
      this.exportFields = field;
    },

    onTagRemove(list, index, parentIndex, tableName, fieldName) {
      list.splice(index, 1)
      if (list.length == 0) {
        this.exportFields.splice(parentIndex, 1)
      }

      if (this.$refs.selectExportField) {
        this.$refs.selectExportField.tableList.forEach(item => {
          if (item.tableName == tableName) {
            item.fieldList.forEach(item1 => {
              if (item1.elementName == fieldName) {
                item1.checked = false
              }
            })
          }
        })
      }
    },

    exportData() {
      let params = {
        exportFieldTableList: [],
        rgId: this.cgId,
        formType: this.exportType,
        exportType: this.exportFormat,
        desensitizationAuditObject: this.desensitizationAuditObject
        // conditionBaseline: {},
      };
      // 添加附件
      if (this.$refs.uploadRef?.fileList.length) {
        params.sysTaskFileInfoList = this.$refs.uploadRef?.fileList.map(item => {
          return {
            fileUrl: item.url,
            remark: JSON.stringify({size: item.size, name: item.name})
          }
        })
      }

      if (this.exportType == 2) {
        delete params.conditionBaseline;
        params.exportFieldTableList = this.exportFields;
      } else {
        let arr = [];
        let varGroupList = JSON.parse(JSON.stringify(this.varGroupList));
        varGroupList.forEach((item, index) => {
          let conditionBaseline = {};
          // if (index == 1) {
            if (item.conditionBaseline && item.conditionBaseline.fieldName) {
              conditionBaseline = JSON.parse(
                JSON.stringify(item.conditionBaseline)
              );
              if (conditionBaseline.condition == "in") {
                if (
                  item.conditionBaseline.fieldValue2 &&
                  item.conditionBaseline.fieldValue2.length == 2
                ) {
                  conditionBaseline.fieldValue =
                    item.conditionBaseline.fieldValue2[0] + " 00:00:00";
                  conditionBaseline.fieldValue2 =
                    item.conditionBaseline.fieldValue2[1] + " 23:59:59";
                } else {
                  conditionBaseline.fieldValue2 = "";
                }
              } else if (
                conditionBaseline.condition == "&gt;" ||
                conditionBaseline.condition == "&gt;="
              ) {
                if (this.currentDate > conditionBaseline.fieldValue) {
                  conditionBaseline.fieldValue2 = this.currentDate;
                } else {
                  conditionBaseline.fieldValue2 = conditionBaseline.fieldValue;
                  conditionBaseline.fieldValue = this.currentDate;
                }
              } else if (
                conditionBaseline.condition == "&lt;" ||
                conditionBaseline.condition == "&lt;="
              ) {
                let selectDate = new Date(conditionBaseline.fieldValue);
                let prevYearDate = ((selectDate) => {
                  let year = selectDate.getFullYear() - 1;
                  let month = String(selectDate.getMonth() + 1);
                  let day = String(selectDate.getDate());
                  return `${year}-${month.padStart(2, 0)}-${day.padStart(
                    2,
                    0
                  )}`;
                })(selectDate);
                conditionBaseline.fieldValue2 = conditionBaseline.fieldValue;
                conditionBaseline.fieldValue = prevYearDate;
              } else {
                conditionBaseline.fieldValue2 = "";
              }
              conditionBaseline.onlyInHospital =
                item.conditionBaseline.onlyInHospital == 1 ? true : false;
              params.conditionBaseline = conditionBaseline;
            }
          // } else {
            item.variateList.forEach((item1) => {
              if (
                item1.tableName == "sd_chemcheck_doc" &&
                (item1.fieldName.split("__")[0] == "item_code" ||
                  item1.fieldName.split("__")[0] == "item_name" ||
                  item1.fieldName.split("__")[0] == "subitem_name" ||
                  item1.fieldName.split("__")[0] == "subitem_code")
              ) {
                if (item1.fieldValue.length > 1) {
                  item1.fieldComment = item1.fieldValue[1];
                  item1.fieldComment += this.formatTableLabel(item1.whichTimes);
                  item1.fieldValue =
                    item1.fieldValue.length > 1 ? item1.fieldValue[1] : "";
                }
              } else if (
                item1.tableName == "sd_techcheck_doc" &&
                (item1.fieldName.split("__")[0] == "check_item" ||
                  item1.fieldName.split("__")[0] == "item_name" ||
                  item1.fieldName.split("__")[0] == "subitem_name" ||
                  item1.fieldName == "check_subitem")
              ) {
                if (item1.fieldValue.length > 1) {
                  item1.fieldComment = item1.fieldValue[1];
                  item1.fieldComment += this.formatTableLabel(item1.whichTimes);
                }
                if (
                  item1.fieldName.split("__")[0] == "check_item" ||
                  item1.fieldName.split("__")[0] == "check_subitem"
                ) {
                  let code =
                    item1.fieldValue.length > 1 ? item1.fieldValue[1] : "";
                  if (code) {
                    code = code.split("-")[0];
                  }
                  item1.fieldValue = code;
                } else {
                  item1.fieldValue =
                    item1.fieldValue.length > 1 ? item1.fieldValue[1] : "";
                }
              } else {
                item1.fieldComment += this.formatTableLabel(item1.whichTimes);
              }
            });
            let obj = {
              variableGroup: index == 0 ? "患者基本信息" : "变量组" + index,
              exportFieldList: item.variateList,
            };
            if(index > 0) {
              obj.conditionBaseline = conditionBaseline
            }
            arr.push(obj);
          // }
        });
        params.exportFieldTableList = arr;
        params.elasticsearchVO = {};
        params.elasticsearchVO.page = this.pageNum1 - 1;
        params.elasticsearchVO.pageSize = this.pageSize1;
      }
      this.$prompt('自定义文件名称', '系统提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        params.customExportFileName = value
        this.exportExcel(params);
      }).finally(() => {
      });

    },
    //获取导出的下载记录
    exportExcel(params) {
      this.fullscreenLoading = true;
      customExport3(params)
        .then((res) => {
          // this.download(res.msg);
          this.$nextTick((_) => {
            this.fullscreenLoading = false;
            this.getPoint();
          });
        })
        .catch((_) => {
          this.fullscreenLoading = false;
        });
    },

    //获取图标的坐标
    getPoint() {
      this.startTop = this.$refs.exportBtn.getBoundingClientRect().top;
      this.startLeft = this.$refs.exportBtn.getBoundingClientRect().left;
      this.exportAlertShow = false;
      this.$message.success("导出成功, 去导出记录里面查看文件!");
      this.addShow = true; //加入导出记录图标显示
    },
    // 进入前的状态
    beforeEnter(el) {
      el.style.left = this.startLeft + "px";
      el.style.top = this.startTop + "px";
      el.style.transform = "scale(1)";
    },
    // 进入中
    enter(el) {
      // 需要调用元素的offset操作，才有过渡效果，否则会马上执行
      el.offsetWidth;
      // 导出记录图标的位置 exportRecord
      const sizeTop = this.$refs.exportRecord.getBoundingClientRect().top;
      const sizeLeft = this.$refs.exportRecord.getBoundingClientRect().left;
      el.style.left = sizeLeft + "px";
      el.style.top = sizeTop + "px";
      el.style.transform = "scale(0.7)";
    },
    // 进入后
    afterEnter() {
      this.addShow = false;
    },
    //显示下载弹窗
    showDownloadListAlert() {
      this.isShowDownloadListAlert = true;
      this.getExportRecordList();
    },
    //获取导出记录列表
    getExportRecordList(type) {
      if (type == "search") {
        this.fileTotal = 0;
        this.fileQueryParams.pageNum = 1;
      }
      let params = this.fileQueryParams;
      params.rgId = this.cgId;
      getExportRecord(params).then((res) => {
        let list = res.data.rows || [];
        this.downloadFileList = list;
        this.fileTotal = res.data.total;
        this.domain = res.domain;
      });
    },
    //下载文件
    download(row, title = "", artist = "") {
      const eleLink = document.createElement("a"); // 新建A标签
      eleLink.href = this.domain + row.downloadUrl; // 下载的路径
      // eleLink.download = `${title} - ${artist}`; // 设置下载的属性，可以为空
      eleLink.style.display = "none";
      document.body.appendChild(eleLink);
      eleLink.click(); // 触发点击事件
      document.body.removeChild(eleLink);

      addCount({peId: row.peId, downloadTimes: row.downloadTimes}).then(() => {
        this.getExportRecordList()
      })
    },
    //删除文件
    delFile(fileId, row) {
      this.$confirm(`是否确认删除该文件?`, "提示", {
        confirmButtonText: "确定",
        type: "info",
        // showCancelButton: false,
        closeOnClickModal: false,
        // closeOnPressEscape: false,
        // showClose: false,
      })
        .then((res) => {
          delExportRecordFile(fileId).then((res) => {
            this.showDownloadListAlert();
            this.$message.success("删除成功");
          });
        })
        .catch((_) => {});
    },
    //修改筛选条件
    editCondition(item) {
      if (item.searchType == 1) {
        this.$router.push({ path: "/biodata", query: { cgId: item.pId } });
      } else if (item.searchType == 2) {
        this.$router.push({ path: "/advanced", query: { cgId: item.pId } });
      } else if (item.searchType == 3) {
        this.$router.push({ path: "/precise", query: { cgId: item.pId } });
      } else if (item.searchType == 6) {
        this.$router.push({ path: "/eventSearch", query: { cgId: item.pId } });
      }
      this.showHistory = false;
    },
    //每一行的回调方法
    rowClassName: function ({ row }) {
      if (row.fieldName === "EMPI__00") {
        return "hideRow";
      }
    },
    closePage() {
      if (
        this.currentSelectedDisease.diseaseSyscode !=
        this.projectData.diseaseSyscode
      ) {
        this.$confirm(
          `当前病种已切换为‘${this.currentSelectedDisease.diseaseAlias}’，与您正在查看的研究队列所属病种不一致, 需要重新获取数据!`,
          "提示",
          {
            confirmButtonText: "确定",
            type: "info",
            showCancelButton: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showClose: false,
          }
        )
          .then((res) => {
            // this.$nextTick((_) => {
            //   const obj = { path: "/casegroup/initial" };
            //   this.$tab.closeOpenPage(obj);
            // });
            this.cgId = "";
            this.variableNum = 3;
            // this.historyQuery.rgId = "";
            this.projectData = {
              projectName: "",
              searchType: "1",
            };
            this.dataList1 = [];
            this.total1 = 0;
            this.selectGroupText = "";
            this.resetVarGroupList();
            this.getSubjectAndGroupTree();
            this.getSelectParentList();
          })
          .catch((_) => {});
      }
    },
    //变量组重置为默认值
    resetVarGroupList() {
      this.varGroupList = [
        {
          isShow: true,
          name: "患者基本信息",
          variateList: [
            //研究变量列表
            {
              fieldName: "EMPI__00",
              fieldComment: "本系统患者主索引",
              tableComment: "患者基本信息表",
              tableName: "sd_patient_info",
              fieldValue: "",
              whichTimes: "",
            },
            {
              fieldName: "encount_id__00",
              fieldComment: "就诊号",
              tableComment: "患者基本信息表",
              tableName: "sd_patient_info",
              fieldValue: "",
              whichTimes: "",
            },
            {
              fieldName: "pat_name__01",
              fieldComment: "患者姓名",
              tableComment: "患者基本信息表",
              tableName: "sd_patient_info",
              fieldValue: "",
              whichTimes: "",
            },
            {
              fieldName: "gender__02",
              fieldComment: "性别",
              tableComment: "患者基本信息表",
              tableName: "sd_patient_info",
              dictCode: "sd_dict_sex",
              fieldValue: "",
              whichTimes: "",
            },
            {
              fieldName: "birthday__03",
              fieldComment: "出生日期",
              fillingRate: "100%",
              tableComment: "患者基本信息表",
              tableName: "sd_patient_info",
              fieldValue: "",
              whichTimes: "",
            },
          ],
        }
      ];
      this.tableColumn1 = JSON.parse(JSON.stringify(this.varGroupList));
      this.tableColumn1.splice(1, 1);
    },
    //表头数据进行处理
    formatTableLabel(type) {
      if (type == 1) {
        return "(最近一次)";
      } else if (type == 2) {
        return "(全部)";
      } else if (type == 3) {
        return "(首次)";
      } else if (type == 4) {
        return "(末次)";
      } else if (type == 5) {
        return "(MAX)";
      } else if (type == 6) {
        return "(MIN)";
      } else if (type == 7) {
        return "(AVG)";
      } else {
        return "";
      }
    },

    view(row, type) {
      let query = {}
      for (let key in row) {
        if (key.split("__")[0] == "EMPI" || key.split("__")[0] == "empi") {
            query.empi = row[key]
        }

        if (type == 'encount_id') {
          if (key.split("__")[0] == "encount_id") {
            query.encountId = row[key]
          }
        }
      }

      this.$jumpPatient360({ path: "/cdrView", query }, query)

    },
    //选择用户
    selectUser(val) {
      this.selectedUserList = val;
      this.$nextTick(() => {
        this.changePageCoreRecordData();
      });
    },
    //设置表格数据选中
    setSelectRow() {
      if (
        !this.userListAllSelections ||
        this.userListAllSelections.length <= 0
      ) {
        this.$refs.userTable.clearSelection();
        return;
      }
      // 标识当前行的唯一键的名称
      // let idKey = "encount_id__00";
      let fn = (row) => {
        return row.encount_id__00 || row.encount_id__01 || row.EMPI__00
      }
      let selectAllIds = [];
      this.userListAllSelections.forEach((row) => {
        selectAllIds.push(fn(row));
      });
      this.$refs.userTable.clearSelection();
      for (var i = 0; i < this.dataList1.length; i++) {
        if (selectAllIds.indexOf(fn(this.dataList1[i])) >= 0) {
          this.$refs.userTable.toggleRowSelection(this.dataList1[i], true);
        }
      }
    },
    // 记忆选择核心方法
    changePageCoreRecordData() {
      // 标识当前行的唯一键的名称
      let fn = (row) => {
        return row.encount_id__00 || row.encount_id__01 || row.EMPI__00
      }
      // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算
      if (this.userListAllSelections.length <= 0) {
        this.selectedUserList.forEach((row) => {
          this.userListAllSelections.push(row);
        });
        return;
      }
      // 总选择里面的key集合
      let selectAllIds = [];
      this.userListAllSelections.forEach((row) => {
        selectAllIds.push(fn(row));
      });
      let selectIds = [];
      // 获取当前页选中的id
      this.selectedUserList.forEach((row) => {
        selectIds.push(fn(row));
        // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里
        if (selectAllIds.indexOf(fn(row)) < 0) {
          this.userListAllSelections.push(row);
        }
      });
      let noSelectIds = [];
      // 得到当前页没有选中的id
      this.dataList1.forEach((row) => {
        if (selectIds.indexOf(fn(row)) < 0) {
          noSelectIds.push(fn(row));
        }
      });
      noSelectIds.forEach((id) => {
        if (selectAllIds.indexOf(id) >= 0) {
          for (let i = 0; i < this.userListAllSelections.length; i++) {
            if (fn(this.userListAllSelections[i]) === id) {
              // 如果总选择中有未被选中的，那么就删除这条
              this.userListAllSelections.splice(i, 1);
              break;
            }
          }
        }
      });
    },
    delUser() {
      let keys = Object.keys(this.userListAllSelections[0])
      let empis = this.userListAllSelections.map(item => {
        return {
          rgId: this.cgId,
          empi: item[keys.filter(item => item.includes('EMPI') || item.includes('empi') )],
          encountId: item[keys.filter(item => item.includes('encount'))]
        }
      })
      if (empis.length > 100) {
        this.$message.error("单次删除不能超过一百条数据");
        return false;
      }
      if (empis.length > 0) {
        this.$confirm(
          `确定将选择的${empis.length}例患者从初筛队列删除吗?`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "info",
          }
        )
          .then((_) => {
            this.loading = true;
            delUser(empis)
              .then((res) => {
                this.$message.success("删除成功!");
                this.useVariate("btn");
              })
              .catch((_) => {
                this.loading = false;
              });
          })
          .catch((_) => {});
      }
    },
    selectSaveType(e) {
      if (this.lockStatus == 1) {
        return this.$message.error("当前队列已锁定，禁止操作！");
      }
      console.info("保存类型:"+e)
      if(e==2||e=="2"){
        console.info("请求参数为:",this.quickCustomSearchParam)
        this.saveResearchQueueAll()
        return
      }
      this.saveResearchQueue(e)
    },

    //保存为研究队列 type: 1保存已勾选的数据 2保存全部数据
    saveResearchQueue(type) {
      this.loading = true;
      let sdResearchQueueDataList = [];
      let tableColumn1 = JSON.parse(JSON.stringify(this.tableColumn1));
      tableColumn1 = tableColumn1.splice(1, 1);
      let keys = Object.keys(this.userListAllSelections[0])
      this.userListAllSelections.forEach((item) => {
        let obj = {
          rsId: this.projectData.rsId,
          rgId: this.projectData.rgId,
          empi: item[keys.filter(item => item.includes('EMPI') || item.includes('empi'))],
          patName: item[keys.filter(item => item.includes('pat_name'))],
          gender: item[keys.filter(item => item.includes('gender'))],
          birthday: item[keys.filter(item => item.includes('birthday'))],
          encountId: item[keys.filter(item => item.includes('encount'))],
          dataFrom: 1,
          dataJson: JSON.stringify(item),
          tableHeadJson: JSON.stringify(tableColumn1),
        };
        sdResearchQueueDataList.push(obj);
      });

      this.$confirm(
        `确定将选择的${sdResearchQueueDataList.length}例患者保存到研究队列吗?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "info",
        }
      )
        .then((_) => {
          saveResearchQueue({sdResearchQueueDataList: sdResearchQueueDataList})
          .then((res) => {
            this.loading = false;
            this.$message.success("保存成功!");
            //保存成功将列表勾选的数据取消勾选
            this.userListAllSelections = [];
            this.$refs.userTable.clearSelection();
            setTimeout((_) => {
              this.$router.push({
                path: "/casegroup/studied",
                query: {
                  rgId: this.projectData.rgId,
                  rsId: this.projectData.rsId,
                },
              });
            }, 500);
          }).catch((_) => {
            this.loading = false;
          });
        }).catch((_) => {});
    },
    saveResearchQueueAll() {
      this.$confirm(
        `确定将全部的患者保存到研究队列吗?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "info",
        }
      )
        .then((_) => {
          saveResearchQueueAll(this.quickCustomSearchParam)
            .then((res) => {
              this.loading = false;
              this.$message.success("保存成功!");
              //保存成功将列表勾选的数据取消勾选
              this.userListAllSelections = [];
              this.$refs.userTable.clearSelection();
              setTimeout((_) => {
                this.$router.push({
                  path: "/casegroup/studied",
                  query: {
                    rgId: this.projectData.rgId,
                    rsId: this.projectData.rsId,
                  },
                });
              }, 500);
            }).catch((_) => {
              this.loading = false;
            });
        }).catch((_) => { });
    },
    //获取初筛队列添加记录
    getHistoryList() {
      let params = this.historyQuery;
      params.rgId = this.cgId;
      listCasegroup(params).then((response) => {
        this.historyList = response.rows;
        this.historyTotal = response.total;
        this.showHistory = true;
      });
    },
    //跳转到课题
    toSubjectPage() {
      this.$router.push({ path: "/subject" });
    },
    getOpenEvent() {
      this.showModel = false;
    },
    //点击单元格获取详细记录
    moreRecoedDetail(row, fieldName, tableName, colIndex, fieldIndex, subItem, colName) {
      this.showHistoryLoading = true;
      let params = {
        // rgId: this.cgId,
        // empi: row.EMPI__00,
        // fieldName: fieldName,
        // isViewAllSelect: false,
        // tableName: tableName,
        // exportFieldTableList: this.exportFieldTableList,
        // variableGroupIndex: `${colIndex},${fieldIndex}`,

        tableName: tableName,
        fieldName: fieldName,
        ids: row[colName+colIndex][0][fieldName+'__pk'],
        dictCode: this.exportFieldTableList[colIndex].exportFieldList[fieldIndex].dictCode || '',
        fieldType: this.exportFieldTableList[colIndex].exportFieldList[fieldIndex].fieldType || '',
        baselineFieldName: this.exportFieldTableList[colIndex].exportFieldList[fieldIndex].baselineFieldName || ''
      };
      this.recordList = [];
      getTableFindElementList({ tableName: tableName, searchFlag: 1 }).then(
        (res) => {
          // recordColumns
          let arr = [];
          let arr1 = [];
          this.showRecordList = true;
          res.data.forEach((item) => {
            let obj = {
              fieldName: item.elementName,
              aliseName: item.aliasName,
              dictCode: item.dictCode,
            };
            if (item.elementName == fieldName.split("__")[0]) {
              if (subItem) obj.aliseName = subItem[1];
              if (item.dictCode && !this.dictList[item.dictCode]) {
                this.getDictData(item.dictCode);
              }
              arr.push(obj);
            } else if (item.baselineFlag == 1) {
              arr1.push(obj);
            }
          });
          //定义两个arr是为了让基线时间在字段后面的列显示
          this.recordColumns = [...arr, ...arr1];
          selectVariableDetails(params).then((res) => {
            this.recordList = res.data || [];
          });
          this.showHistoryLoading = false;
        }
      );
    },
    //韦恩图保存队列后 , 判断保存的是否是当前选择的队列, 如果是则进行刷新列表
    refreshList(rgId) {
      if (rgId == this.cgId) {
        this.useVariate();
      }
    },

    //跳转统计分析
    toStatis() {
      this.$refs.selectStatisticalType.init(this.createStatisticsFileParams);
    },
    getStatisticalTypeValue(val) {},

    onExportTypeChange(e) {
      if (e == '2') {
        this.exportFormat = '2'
      }
    }
  },
};
</script>

<style lang="scss">
.projectDetail-page {
  min-height: calc(100vh - 84px);
  background: #f8f8f8;
  padding: 0 0px 20px;
  .p-head-box {
    background: #fff;
    padding: 15px 20px;
    margin-bottom: 10px;
    line-height: 24px;
    .fl-head {
      float: left;
      width: 500px;
    }
    .fr-head {
      margin-left: 520px;
    }
    .p-name {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      padding-right: 20px;
    }
    .p-info {
      font-size: 15px;
      color: #333;
    }
    .sel-box {
      overflow: hidden;
      .label {
        font-size: 14px;
        color: #555;
        float: left;
        line-height: 32px;
      }
      .sel-div {
        margin: 0 10px 0 72px;
        height: 34px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 0 10px;
        font-size: 14px;
        color: #666;
        line-height: 32px;
      }
      .arr-icon {
        float: right;
        margin-top: 10px;
      }
      .subject-text {
        margin-right: 30px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  .p-main-box {
    overflow: hidden;
    padding: 15px 20px;
    background: #fff;
    .top-box {
      overflow: hidden;
      margin-bottom: 10px;
      .fl-title {
        float: left;
        line-height: 32px;
      }
    }
    .fl-box {
      float: left;
      width: 500px;
      height: 78vh;
      border: 1px solid #eee;
      background: #fff;
      // padding:0px 0 12px 12px;
      position: relative;
      overflow: auto;
      transition: 0.5s ease all;
      .fl-box-content {
        height: 70vh;
        overflow: auto;
        padding: 12px;
      }
      .group-item {
        border: 1px solid #8bc0f7;
        margin-top: 25px;
        padding: 10px;
        border-radius: 3px;
        transition: 0.5s ease all;
      }
      .group-item:last-child {
        // border: 0px;
      }
      .set-group-height {
        height: 54px;
        overflow: hidden;
      }
      .c-item {
        margin-bottom: 10px;
        .label {
          font-size: 14px;
          font-weight: bold;
          color: #333;
          line-height: 20px;
          margin-bottom: 5px;
        }
        .field-box {
          overflow: hidden;
          .operate {
            float: right;
          }
          .f-name {
            font-size: 14px;
            color: #f51e1e;
            line-height: 32px;
            text-decoration: underline;
          }
          .baseLine-field {
            font-size: 14px;
            line-height: 32px;
          }
        }
        .filter-box {
          .box {
            margin-bottom: 5px;
            .set-w {
              width: 98px;
              margin-right: 5px;
            }
            .set-w1 {
              width: 220px;
              margin-right: 5px;
            }
            .set-w2 {
              width: 65px;
              margin-right: 5px;
            }
            .whichTimes {
              width: 75px;
            }
            .icon-warning {
              display: inline-block;
              margin: 0 5px 0 -3px;
              font-size: 20px;
              color: #666;
            }
          }
        }
        .sel-cascader {
          width: 117px;
          float: right;
          .el-input__inner {
            padding-left: 3px;
          }
        }
      }
      .f-btn-group {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 15px 10px;
        text-align: center;
        border-top: 1px solid #eee;
      }
      .baseline-describe-text {
        .describe-text {
          margin-bottom: 5px;
        }
        .text-label {
          float: left;
          font-size: 14px;
          color: #606266;
          line-height: 24px;
        }
        .text-content {
          margin: 0 0 0 68px;
          .text {
            display: inline-block;
            font-size: 14px;
            line-height: 24px;
            color:#666;
            padding-right: 6px;
          }
          .edit-btn {
            padding:5px 0px;
          }
        }
      }
    }
    .fl-hidden {
      // display: none;
      width: 0px;
      border: 0px;
    }
    .fr-box {
      margin-left: 520px;
      transition: 0.5s ease all;
      .fr-head-box {
        // margin-left: 520px;
        margin-bottom: 10px;
        overflow: hidden;
        .selectUserNum {
          font-size: 16px;
          color: #666;
          line-height: 32px;
          .user-nums {
            font-size: 18px;
            color: #1890ff;
            padding: 0 2px;
          }
        }
      }
      .fr-btn-group {
        float: right;
        .table-type {
          float: left;
          margin-right: 50px;
        }
      }
      .dropdown-box {
        margin: 0 10px;
      }
    }
    .set-mar-l {
      margin-left: 0;
    }
  }
  .export-box {
    top: 10vh !important;
    .label {
      display: inline-block;
      font-size: 15px;
      line-height: 20px;
      margin: 0 10px 0 0;
    }
    .img-box {
      margin-top: 20px;
    }
    .img {
      width: 100%;
      display: block;
      margin-top: 10px;
    }
    .download-btn {
      margin: 10px 0 0;
      .tip {
        font-size: 12px;
        color: #f51e1e;
        padding-left: 10px;
      }
    }
  }
  ::v-deep .hideRow {
    display: none;
  }
  .search-box {
    padding: 0 0 20px 0;
    .s-btn {
      margin-left: 10px;
    }
  }
  .exportRecord-btn {
    position: fixed;
    bottom: 50px;
    right: 0px;
    width: 54px;
    height: 54px;
    // padding: 9px 0;
    background: #1890ff;
    font-size: 16px;
    color: #fff;
    text-align: center;
    line-height: 18px;
    // border-radius: 50%;
    box-shadow: 0 0 10px 1px #3594ef;
    cursor: pointer;
    z-index: 10;
    border-radius: 6px 0 0 6px;
  }
  .exportRecord-btn:hover {
  }
  .history-btn {
    bottom: 110px;
    border-radius: 6px 0 0 6px;
    background: #0bafa7;
    box-shadow: 0 0 10px 1px #16a7a0;
  }
  .history-btn img {
    margin-top: 2px;
  }
  ::v-deep.o-select {
    .el-input__inner {
      padding-left: 3px !important;
      padding-right: 10px;
    }
  }
  .text-overhide {
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding: 0 5px;
  }

  .img_js {
    width: 48px;
    position: fixed;
    right: 0;
    top: 50vh;
    transition: all 1s;
    z-index: 99999;
  }
}
.pop-subject {
  padding: 0px 0px 10px !important;
  .subject-item {
    color: #333;
    font-weight: bold;
    text-align: left;
    .fr-icon {
      color: #999;
      float: right;
      margin-top: 4px;
    }
  }
  .group-box {
    text-align: left;
    .group-item {
      // padding-right: 5%;
      color: #666;
      display: inline-block;
      cursor: pointer;
      width: 50%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .active {
      color: #179e84;
      font-weight: bold;
    }
  }
  .set-tab-header-h {
    padding: 0;
    height: 34px;
  }
}
.set-baseline-alert {
  .set-w {
    width: 106px;
    // margin-right: 10px;
  }
  .set-w1 {
    width: 230px;
    // margin-right: 10px;
  }
  .set-w2 {
    width: 65px;
    // margin-right: 10px;
  }
  .set-w3 {
    width: 200px;
    // margin-right: 10px;
  }
  .whichTimes {
    width: 90px;
  }
  .icon-warning {
    display: inline-block;
    margin: 0 5px 0 -3px;
    font-size: 20px;
    color: #666;
  }
  .text-span {
    font-size: 16px;
    padding-right: 5px;
  }
  .set-mar-b-10 {
    margin-bottom: 18px;
  }
}

.set-baseline-alert {
  .el-popover__reference-wrapper {
    display: flex;
    align-items: center;
    .el-icon-warning-outline {
      cursor: pointer;
      font-size: 18px;
      margin-left: 4px;
    }
  }
}
.projectDetail-page {
  .el-form-item__label {
    display: inline-flex !important;
    align-items: center;
  }
  .export-fields-box {
    border: 1px solid #eee;
    // padding: 10px;
    .field-item {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #eee;
      &:last-child {
        border-bottom: 0;
      }
      .table-name {
        width: 120px;
        font-size: 14px;
        color: #666;
        line-height: 36px;
        // font-weight: bold;
        margin-right: 10px;
        padding: 0 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap
      }
      .field-list {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        border-left: 1px solid #eee;
        padding: 10px 10px 0 10px;
        .field-tag {
          margin-right: 10px;
          margin-bottom: 10px
        }
      }
    }
  }
}
</style>
