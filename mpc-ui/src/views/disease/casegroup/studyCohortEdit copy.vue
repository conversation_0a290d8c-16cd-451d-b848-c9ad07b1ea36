<template>
    <div class="studyCohortEdit-page" v-loading="loading">
        <!-- 左侧 -->
        <div class="fl-box" :class="!isLeftUnfold ? 'setWidth' : ''">
            <div class="fl-content" >
                <div class="content-title">表单目录</div>
                <div class="form-dom-card-list" v-if="formJson && formJson.widgetList && formJson.widgetList.length > 0">
                    <div class="card-label" :class="item.id == currentClickCardId ? 'active' : ''" @click="chooseCard(item)"
                    v-if="item.type == 'card'" v-for="(item, index) in formJson.widgetList" :key="index">
                        {{item.options.label}}
                    </div>
                </div>
            </div>
            <div class="arrow-box" @click="isLeftUnfold = !isLeftUnfold">
                <i v-if="isLeftUnfold" class="el-icon-arrow-left"></i>
                <i v-else class="el-icon-arrow-right"></i>
            </div>
        </div>
        <!-- 右侧 -->
        <div class="fr-box" :class="!isRightUnfold ? 'setWidth' : ''">
            <div class="fr-content">
                <div class="content-title">参考信息</div>
                <el-collapse v-model="activeNames" accordion>
                    <!-- v-if="rightTableListData[`${tableItem.tableEnName}`].length > 0" -->
                    <el-collapse-item v-for="(tableItem, tableItemIndex) in curreentClickCardTableNames" :key="tableItemIndex" :name="tableItem.tableEnName">
                        <template slot="title">
                            {{tableItem.tableCnName}}
                            <el-tooltip class="item" effect="dark" content="查询搜索" placement="top">
                                <i class="header-icon el-icon-search s-icon" @click.stop="search(tableItem.tableEnName)"></i>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" content="重置查询" placement="top">
                                <i class="header-icon el-icon-refresh s-icon" @click.stop="searchList('reset', tableItem.tableEnName, tableItem.displayFieldList)"></i>
                            </el-tooltip>
                            <!-- <i class="header-icon el-icon-search s-icon" @click.stop="search(tableItem.tableEnName)"></i> -->
                            <!-- <i class="header-icon el-icon-refresh s-icon" @click.stop="searchList('reset', tableItem.tableEnName)"></i> -->

                            <!-- <el-button type="primary" size="mini" @click="searchList('reset', tableItem.tableEnName)">重置数据</el-button> -->
                        </template>
                        <el-table v-if="rightTableHeadData[`${tableItem.tableEnName}`]" :data="rightTableListData[`${tableItem.tableEnName}`]" border stripe height="65vh">
                            <el-table-column type="expand" v-if="tableMaxHeader && tableMaxHeader.length">
                                <template slot-scope="props">
                                    <el-form label-position="left" inline class="demo-table-expand">
                                        <el-form-item :label="col.aliasName" v-for="col in tableMaxHeader" :key="col.elementName">
                                            <div v-if="col.dictCode">
                                                <dict-tag :options="dictList[col.dictCode]" :value="props.row[col.elementName]"/>
                                            </div>
                                            <span v-else>{{ props.row[col.elementName] }}</span>
                                        </el-form-item>
                                    </el-form>
                                </template>
                            </el-table-column>
                            <el-table-column v-for="col in tableHeader"
                                :prop="col.elementName"
                                :key="col.elementName"
                                :label="col.aliasName"
                                >
                                <template slot-scope="scope">
                                    <div v-if="col.dictCode">
                                        <dict-tag :options="dictList[col.dictCode]" :value="scope.row[col.elementName]"/>
                                    </div>
                                    <div v-else-if="col.elementName == 'empi'">
                                        <el-button
                                            size="mini"
                                            type="text"
                                            @click="onView(scope.row)"
                                            >
                                            {{scope.row.empi || scope.row.EMPI}}
                                        </el-button>
                                    </div>
                                    <div v-else>
                                        {{scope.row[col.elementName]}}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="50px">
                                <template slot-scope="scope">
                                    <el-button type="text" size="small" @click="chooseItemData(scope.row)">选择</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <pagination
                            v-if="tableItem.total && tableItem.total > 0"
                            :total="tableItem.total"
                            :page.sync="tableItem.pageNum"
                            :limit.sync="tableItem.pageSize"
                            :pager-count="5"
                            layout="total, prev, pager, next"
                            @pagination="searchList('page', tableItem.tableEnName, tableItem.displayFieldList)"
                        />
                    </el-collapse-item>
                </el-collapse>
            </div>
            <div class="arrow-box" @click="isRightUnfold = !isRightUnfold">
                <i v-if="isRightUnfold" class="el-icon-arrow-right"></i>
                <i v-else class="el-icon-arrow-left"></i>
            </div>
        </div>
        <!-- 中间表单 -->
        <div class="mid-box" :class="{'setMarL' : !isLeftUnfold, 'setMarR': !isRightUnfold}">
            <v-form-render :form-json="formJson" :global-dsv="globalDsv" :form-data="formData" :option-data="optionData" ref="vFormRef"></v-form-render>
            <div class="btn-group">
                <el-button type="primary" size="default" v-if="type == 'add'" @click="submitForm">提交</el-button>
                <el-button type="primary" size="default" v-else @click="submit">提交</el-button>
                <!-- <el-button type="" size="default" @click="getTemplate">重置</el-button> -->
            </div>
        </div>
        <!-- 修改原因弹窗 -->
        <el-dialog
            title="修改字段及原因"
            :visible.sync="showCauseAlert"
            width="900px"
            @close="showCauseAlert = false">
            <el-form class="form-box" :model="changeForm" ref="form" label-width="80px" :inline="false" size="normal">
                <el-form-item label="修改说明">
                    <el-input type="textarea" rows="3" v-model="changeForm.updateExplain"></el-input>
                </el-form-item>
                <el-card class="card-item" shadow="always" v-if="item.fields.length > 0" :body-style="{ padding: '20px'}" v-for="(item, index) in changeForm.changeFieldObj" :key="index">
                    <div slot="header" v-if="item.card">
                        <span>{{item.card}}</span>
                    </div>
                    <!-- card body -->
                    <el-table :data="item.fields" border stripe>
                        <el-table-column
                            prop="updateFieldLabel"
                            label="字段名">
                        </el-table-column>
                         <el-table-column
                            prop="updateBeforeValue"
                            label="原值">
                            <template slot-scope="scope">
                                <div v-if="scope.row.dict && scope.row.dsEnabled">
                                    {{dictFormat(scope.row.dict, scope.row.updateBeforeValue)}}
                                </div>
                                <div v-else-if="scope.row.optionItems">
                                    <div v-if="scope.row.updateBeforeValue && Array.isArray(scope.row.updateBeforeValue)">
                                        {{
                                            scope.row.optionItems.filter(item => {
                                                return scope.row.updateBeforeValue.indexOf(item.value) != -1
                                            }).map(item => {
                                                return item.label
                                            }).join(',')
                                        }}
                                    </div>
                                    <div v-else>
                                        {{
                                            scope.row.optionItems.filter(item => {
                                                return item.value == scope.row.updateBeforeValue
                                            }).map(item => {
                                                return item.label
                                            })[0]
                                        }}
                                    </div>
                                </div>
                                <div v-else>{{scope.row.updateBeforeValue}}</div>
                            </template>
                        </el-table-column>
                         <el-table-column
                            prop="updateAfterValue"
                            label="当前值">
                            <template slot-scope="scope">
                                <div v-if="scope.row.dict && scope.row.dsEnabled">
                                    {{dictFormat(scope.row.dict, scope.row.updateAfterValue)}}
                                </div>
                                <div v-else-if="scope.row.optionItems">
                                     <div v-if="scope.row.updateAfterValue && Array.isArray(scope.row.updateAfterValue)">
                                        {{
                                            scope.row.optionItems.filter(item => {
                                                return scope.row.updateAfterValue.indexOf(item.value) != -1
                                            }).map(item => {
                                                return item.label
                                            }).join(',')
                                        }}
                                    </div>
                                    <div v-else>
                                        {{
                                            scope.row.optionItems.filter(item => {
                                                return item.value == scope.row.updateAfterValue
                                            }).map(item => {
                                                return item.label
                                            })[0]
                                        }}
                                    </div>
                                </div>
                                <div v-else>{{scope.row.updateAfterValue}}</div>
                            </template>
                        </el-table-column>
                         <el-table-column
                            prop="updateCause"
                            label="修改原因">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.updateCause" placeholder="" size="small" clearable></el-input>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-form>
            <span slot="footer">
                <el-button @click="showCauseAlert = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </el-dialog>
        <!-- 查询弹窗 -->
        <el-dialog
            title="搜索"
            :visible.sync="showSearchAlert"
            width="940px"
            @close="showSearchAlert = false">
            <el-row :gutter="20" v-if="searchForm[currentSearchTableName]">
                <el-col :span="24" :offset="0" v-for="(formItem, formIndex) in searchForm[currentSearchTableName].fieldList" :key="formIndex">
                    <el-form :model="formItem" ref="queryForm" :inline="true" label-width="60px">
                        <el-form-item  prop="element" label="字段">
                            <el-select class="set-width-150" v-model="formItem.element" placeholder="请选择" filterable size="small" @change="changeFieldName($event, formIndex)">
                                <el-option v-for="item in mddFieldObj[searchForm[currentSearchTableName].tableName]"
                                    :key="item.elementName"
                                    :label='item.aliasName'
                                    :value="item.elementName">
                                    <span>{{ item.aliasName }}</span>
                                    <span style="color: #1890ff;padding-left:4px; float:right">填充率{{item.fillRate}}</span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item  prop="condition" label="运算符">
                            <el-select class="set-width-100"  v-model="formItem.condition" placeholder="请选择" filterable size="small">
                                <el-option v-for="item in operativeSymbolList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="参数" prop="elemetnValue">
                            <el-select class="set-width-150" v-if="formItem.dictCode" v-model="formItem.elemetnValue" placeholder="请选择" size="small" filterable :disabled="formItem.condition == 'empty' || formItem.condition == 'notEmpty'">
                                <el-option v-for="item in dictList[formItem.dictCode]"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            <el-date-picker unlink-panels  class="set-width-150" v-else-if="formItem.elementType == 'datetime'"
                                v-model="formItem.elemetnValue"
                                value-format="yyyy-MM-dd"
                                type="date"
                                size="small"
                                :disabled="formItem.condition == 'empty' || formItem.condition == 'notEmpty'"
                                placeholder="选择日期">
                            </el-date-picker>
                            <el-input class="set-width-150"v-else v-model="formItem.elemetnValue" :disabled="formItem.condition == 'empty' || formItem.condition == 'notEmpty'" placeholder="请输入" clearable  size="small"></el-input>
                        </el-form-item>
                         <el-form-item  prop="link" label="条件" v-if="searchForm[currentSearchTableName].fieldList.length > 1">
                            <el-select  class="set-width-100" v-model="formItem.link" placeholder="请选择条件" filterable size="small">
                                <el-option v-for="item in conditionOption"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="" >
                            <el-button type="danger" icon="el-icon-minus" circle size="mini" v-if="searchForm[currentSearchTableName].fieldList.length > 1" @click="delSearchLine(formIndex)"></el-button>
                            <el-button type="primary" icon="el-icon-plus" circle size="mini" v-if="formIndex == searchForm[currentSearchTableName].fieldList.length - 1" @click="addSearchLine"></el-button>
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
            <span slot="footer">
                <el-button @click="showSearchAlert = false">取消</el-button>
                <el-button type="primary" @click="searchList">查询</el-button>
            </span>
        </el-dialog>

    </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import {getFormTemplate, saveFormData, findFormData} from '@/api/form/form'
import {updateCasegroup} from '@/api/disease/casegroup'
import {getSdResearchQueueDataDetail, getSearchDataByTableNameAndEmpi, saveFormForMongo, addFormForMongo} from '@/api/disease/caseManage'
import {listElement} from '@/api/system/mddelement'
import $ from 'jquery'
export default {
    name: 'StudyCohortEdit',
    data() {
        return {
            loading: false,
            globalDsv: {
                vueAppBaseApi: process.env.VUE_APP_BASE_API,
                userToken: getToken(),
            },
            formJson: {}, //form表单模板json
            type: '', //add:新增 edit:编辑
            formKey: '', //表单编号
            empi: '', //患者EMPI编码
            rsId: '', //课题ID
            rgId: '', //实验组ID
            formData: {},
            oldFormData: null, //表单原始数据
            optionData: {},
            currentClickCardWidgetList: {}, //当前点击左侧菜单子级节点
            currentClickCardId: null, //当前点击左侧菜单的ID
            curreentClickCardTableNames: [], //当前点击卡片下面所有字段的表名
            rightTableHeadData: {}, //右侧表格表头
            rightTableListData: {}, //右侧表格数据
            dictList: {}, //数据字典列表
            userInfo: {},
            showCauseAlert: false, //填写原因弹窗是否显示
            changeForm: {
                updateExplain: '',
                changeFieldObj: {}
            }, //有变动的字段
            isLeftUnfold: true, //左侧是否展开
            isRightUnfold: true, //左侧是否展开
            showSearchAlert: false, //查询弹窗
            searchForm: {
                // sd_diagnostic_info: {
                //     tableName: '',
                //     fieldList: [
                //         {
                //             link: '',
                //             element: '',
                //             condition: '',
                //             elemetnValue: '',
                //         }
                //     ]
                // }
            },
            currentSearchTableName: '', //当前点击要查询的表名
            displayFieldList: [],
            mddFieldObj: {}, //模型字段
            operativeSymbolList: [
                {label: '大于', value: '&gt;'},
                {label: '小于', value: '&lt;'},
                {label: '大于等于', value: '&gt;='},
                {label: '小于等于', value: '&lt;='},
                {label: '等于', value: '='},
                {label: '不等于', value: '!='},
                {label: '包含', value: 'contains'},
                {label: '不包含', value: 'notContains'},
                {label: '为空', value: 'empty'},
                {label: '不为空', value: 'notEmpty'},
            ],
            conditionOption: [
                {
                    label: '并且',
                    value: 'and'
                },
                {
                    label: '或者',
                    value: 'or'
                }
            ],
            tableHeader: [],
            tableMaxHeader: [],
            activeNames: []
        }
    },
    watch: {
        isLeftUnfold(newVal, oldVal) {
            if(newVal) {
                setTimeout(_ => {
                    $('.fl-content').show();
                },200)
            }else {
                $('.fl-content').hide();
            }
        }
    },
    mounted() {
        this.formKey = this.$route.query.formKey || this.formKey;
        this.type = this.$route.query.type || 'add';
        this.empi = this.$route.query.empi || '';
        this.rgId = this.$route.query.rgId || '';
        this.rsId = this.$route.query.rsId || '';
        if(this.formKey) {
            this.getTemplate();
        }
        // 挂载全局函数给表单调用
        window.msCohortFn = this.msCohortFn
    },
    methods: {
        test() {
            // this.isShow = true;
        },
        //获取表单json数据
        getTemplate() {
            this.loading = true;
            getFormTemplate({formKey: this.formKey})
            .then(res => {
                this.formJson = JSON.parse(res.data.templateJson);
                this.$nextTick(_ => {
                    this.$refs.vFormRef.setFormJson(this.formJson);
                    // this.$refs.vFormRef.addEC('myTest', this);
                })
                if(this.empi) {
                    this.getFormData();
                }else {
                    this.loading = false;
                }
            })
        },

        //获取表单字段数据
        getFormData() {
            let params = {
                rgId: this.rgId,
                empi: this.empi
            }
            getSdResearchQueueDataDetail(params)
            .then(res => {
                let formData = res.data;
                this.oldFormData = JSON.parse(JSON.stringify(res.data));
                this.userInfo = {
                    address: formData.address,
                    age: formData.age,
                    del_flag: formData.del_flag,
                    birthday: formData.birthday,
                    create_time: formData.create_time,
                    disease_syscode: formData.disease_syscode,
                    gender: formData.gender,
                    id_card: formData.id_card,
                    id_type: formData.id_type,
                    nationality: formData.nationality,
                    native_province: formData.native_province,
                    pat_name: formData.pat_name,
                    phone: formData.phone
                }
                this.$refs.vFormRef.setFormData(formData);
                this.loading = false;
            })
        },
        //点击左侧card名称 获取对应的列表数据
        chooseCard(row, obj) {
            let tableNames = [];
            let displayFieldList = []
            if (row) {
                this.currentClickCardWidgetList = row.widgetList;
                this.currentClickCardId = row.id;
                //设置页面滚动到当前点击的card位置
                this.formJson.widgetList.forEach((item, index) => {
                    if(item.id == row.id) {
                        let top = $('.el-form>.container-wrapper').eq(index)[0].offsetTop;
                         $('.mid-box').stop().animate({
                            scrollTop: top - 96
                        });
                    }
                })
                //递归循环当前card下面所有字段组件, 获取全部表名(过滤后的)
                function recursiveFn(arr) {
                    for(let i = 0; i < arr.length; i++) {
                        if(arr[i].options.fieldName && arr[i].options.fieldName.length > 1) {
                            if(tableNames.indexOf(arr[i].options.fieldName[0]) == -1) {
                                tableNames.push(arr[i].options.fieldName[0])
                            }
                            // 获取字段
                            if (displayFieldList.indexOf(arr[i].options.fieldName[1]) == -1) {
                                displayFieldList.push(arr[i].options.fieldName[1])
                            }
                        }else if(arr[i].widgetList) {
                            recursiveFn(arr[i].widgetList)
                        }else if(arr[i].cols && arr[i].cols) {
                            recursiveFn(arr[i].cols)
                        }
                    }
                }
                recursiveFn(row.widgetList);
            } else {
                tableNames = obj.tableNames
                displayFieldList = obj.displayFieldList
            }
            if(!this.empi) return false;

            let curreentClickCardTableNames = [];
            //根据去重后的表名请求接口
            tableNames.forEach(tableItem => {
                let params = {
                    tableName: tableItem,
                    displayFieldList: displayFieldList,
                    rgId: this.rgId,
                    empi: this.empi,
                    pageNum: 1,
                    pageSize: 10
                }
                //根据表名获取需要显示的字段和列表数据, 如果已经存在数据则不再进行请求
                listElement({tableName: params.tableName})
                .then(res => {
                    let fieldList = res.data || [];
                    let list = [];
                    let mddFieldList = [];
                    getSearchDataByTableNameAndEmpi(params)
                    .then(res => {
                        let maxFields = []
                        let tabList = res.data.datas || [];
                        let total = res.data.total || 0;
                        tabList.forEach(item => {
                            item.tableChinaName = res.data.tableNameComment || '';

                            displayFieldList.forEach(d => {
                                if (item[d] && String(item[d]).length >= 30) {
                                    maxFields.push(d)
                                }
                            })
                        })
                        if(tabList.length > 0) {
                            let cols = Object.keys(tabList[0])
                            let index = cols.findIndex(c => c == 'EMPI')
                            cols.splice(index, 1, 'empi')
                            fieldList.forEach(item => {
                              if (item.elementName === 'empi' || item.elementName === 'EMPI') {
                                item.elementName = item.elementName.toLowerCase();
                              }
                                if(cols.includes(item.elementName)) {
                                    let obj = {
                                        aliasName: item.aliasName,
                                        elementName: item.elementName,
                                        isMax: maxFields.includes(item.elementName)
                                    };
                                    if(item.dictCode) {
                                        obj.dictCode = item.dictCode;
                                        this.getDictData(item.dictCode);

                                        //如果模型字段为数据字典
                                        //进行列表数据循环, 列表中的字段和模型字段相同 将列表数据中加一个字典code属性(字段名+_dictCode),用于表单插入数据时字典翻译
                                        tabList.forEach(tabItem => {
                                            if(tabItem[item.elementName]) {
                                                tabItem[item.elementName+'_dictCode'] = item.dictCode
                                            }
                                        })
                                    }
                                    list.push(obj);
                                    mddFieldList.push(item);
                                }
                            })
                        }else {
                            fieldList.forEach(item => {
                                if(item.searchFlag == 1) {
                                    let obj = {
                                        aliasName: item.aliasName,
                                        elementName: item.elementName
                                    };
                                    if(item.dictCode) {
                                        obj.dictCode = item.dictCode;
                                        this.getDictData(item.dictCode)
                                    }
                                    list.push(obj)
                                    mddFieldList.push(item);
                                }
                            })
                        }
                        this.$set(this.rightTableHeadData, params.tableName, list)
                        curreentClickCardTableNames.push({tableEnName: tableItem, displayFieldList: displayFieldList, tableCnName: tabList.length > 0 ? tabList[0].tableChinaName : '', pageNum: 1, pageSize: 10, total})
                        this.$set(this.rightTableListData, params.tableName, tabList);
                        this.$set(this.mddFieldObj, params.tableName, mddFieldList);

                        let headerList = list.filter(item => !item.isMax)
                        this.tableMaxHeader = []
                        if (headerList.length > 4) {
                            this.tableHeader = headerList.filter(item => !item.isMax).slice(0, 4)
                            list.forEach(item => {
                                if (!this.tableHeader.some(t => t.elementName == item.elementName)) {
                                    this.tableMaxHeader.push(item)
                                }
                            })
                        } else {
                            this.tableHeader = headerList
                            this.tableMaxHeader = list.filter(item => item.isMax)
                        }

                        this.activeNames = curreentClickCardTableNames.map(cur => cur.tableEnName)
                        this.curreentClickCardTableNames = curreentClickCardTableNames;

                    })
                })
                // if(!this.rightTableHeadData[params.tableName]) {
                // }else {
                //     curreentClickCardTableNames.push({tableEnName: tableItem, displayFieldList: displayFieldList, tableCnName: this.rightTableListData[tableItem].length > 0 ? this.rightTableListData[tableItem][0].tableChinaName : ''})
                // }
            })

            this.curreentClickCardTableNames = curreentClickCardTableNames;
            
            
        },
        // 表单组件聚焦后带出相关信息
        msCohortFn(res, fields) {
            let fieldName = res.field.options.fieldName
            if (!fieldName || !fieldName.length) return console.error('字段未关联')
            this.currentClickCardWidgetList = [res.$parent.$parent.$parent.$parent.widget]
            this.chooseCard(null, { tableNames: [fieldName[0]], displayFieldList:  [fieldName[1], ...fields] })
        },
        //查询
        search(tableName) {
            this.currentSearchTableName =tableName
            if(!this.searchForm[tableName]) {
                let obj = {
                    tableName,
                    fieldList: [{
                        link: 'and',
                        element: '',
                        condition: '',
                        elemetnValue: ''
                    }]
                };
                this.$set(this.searchForm, tableName, obj)
                this.searchForm[tableName] = obj
            }
            this.showSearchAlert = true;
        },
        //添加一行查询条件
        addSearchLine() {
            this.searchForm[this.currentSearchTableName].fieldList.push(
                {
                    link: 'and',
                    element: '',
                    condition: '',
                    elemetnValue: ''
                }
            )
            if(!this.searchForm[this.currentSearchTableName].fieldList[0].link) {
                this.searchForm[this.currentSearchTableName].fieldList[0].link = 'and'
            }
        },
        //删除一行查询条件
        delSearchLine(index) {
            this.searchForm[this.currentSearchTableName].fieldList.splice(index, 1);
            if(this.searchForm[this.currentSearchTableName].fieldList.length == 1) {
                this.searchForm[this.currentSearchTableName].fieldList[0].link = ''
            }
        },
        //查询条件切换字段
        changeFieldName(elementName, formItemIndex) {
            let formItem = this.searchForm[this.currentSearchTableName].fieldList[formItemIndex];
            let fieldList = this.mddFieldObj[this.searchForm[this.currentSearchTableName].tableName];
            fieldList.find(item => {
                if(item.elementName == elementName) {
                    formItem.elementType = item.elementType;
                    formItem.dictCode = item.dictCode || '';
                    formItem.elemetnValue = ''
                }
            })
        },
        //查询
        searchList(type = 'page', tableName, displayFieldList) {
            if(tableName) {
                this.currentSearchTableName = tableName;
                this.displayFieldList = displayFieldList
            }
            let fields = []
            if(this.searchForm[this.currentSearchTableName]) {
                if(type == 'reset') {
                    this.searchForm[this.currentSearchTableName].fieldList = [{link: 'and', element: '', condition: '', elemetnValue: ''}];
                }else {
                    fields = JSON.parse(JSON.stringify(this.searchForm[this.currentSearchTableName].fieldList))
                }
            }
            if(fields.length > 0) {
                fields[fields.length - 1].link = '';
            }
            let params = {
                tableName: this.currentSearchTableName,
                rgId: this.rgId,
                empi: this.empi,
                fields,
                displayFieldList: this.displayFieldList,
                pageNum: 1,
                pageSize: 10
            }
            let tableItem = {};
            //拿当前要查询的表名跟右侧匹配  获取到当前要用的分页
            this.curreentClickCardTableNames.forEach(item => {
                if(item.tableEnName == params.tableName) {
                    tableItem = item;
                }
            })
            if(type == 'page') {
                params.pageSize = tableItem.pageSize;
                params.pageNum = tableItem.pageNum;
            }else {
                params.pageSize = tableItem.pageSize;
                tableItem.pageNum = 1
            }
            this.$modal.loading();
            getSearchDataByTableNameAndEmpi(params)
            .then(res => {
                let tabList = res.data.datas || [];
                let total = res.data.total || 0;
                if(tabList.length > 0) {
                    let fieldList = this.rightTableHeadData[params.tableName];
                    fieldList.forEach(item => {
                        if(item.dictCode) {
                            //如果模型字段为数据字典
                            //进行列表数据循环, 列表中的字段和模型字段相同 将列表数据中加一个字典code属性(字段名+_dictCode),用于表单插入数据时字典翻译
                            tabList.forEach(tabItem => {
                                if(tabItem[item.elementName]) {
                                    tabItem[item.elementName+'_dictCode'] = item.dictCode
                                }
                            })
                        }
                    })
                    this.$set(this.rightTableListData, params.tableName, tabList)
                }else {
                    this.$set(this.rightTableListData, params.tableName, tabList)
                }
                tableItem.total = total;
                this.$modal.closeLoading();
                this.showSearchAlert = false;
            }).catch(_ => {
                this.$modal.closeLoading();
            })
        },
        //获取数据字典
        getDictData(dictCode) {
            if(!this.dictList[dictCode]) {
                this.getDicts(dictCode).then(res => {
                    res.data.forEach(item => {
                        item.label = item.dictLabel;
                        item.value = item.dictValue;
                        item.raw = { listClass: null };
                    });
                    this.$set(this.dictList, dictCode, res.data);
                });
            }
        },
        //选择右侧某一条数据
        chooseItemData(row) {
            let _this = this;
            //获取表单内所有容器组件
            // let allContainer = this.$refs.vFormRef.getContainerWidgets();
            //设置某个字段的值
            // this.$refs.vFormRef.setFieldValue('sd_patient_info_and_admit_no', '1111');
            let widgetList = this.currentClickCardWidgetList;
            let commonFieldObj = {}; //非子表单的单个字段数据对象 {字段1:'',字段2: ''}
            function recursiveFn(arr) {
                for(let i = 0; i < arr.length; i++) {
                    console.log(arr[i]);
                    if(arr[i].type == 'sub-form') {
                        let newObj = {};
                        //单行子表单字段的值
                        let subFormFieldData = _this.$refs.vFormRef.getSubFormValues(arr[i].options.name);
                        arr[i].widgetList.forEach(fieldItem => {
                            let filedNames = fieldItem.options.name.split('_and_');
                            if(filedNames.length > 1 && row[filedNames[1]] != undefined) {
                                //如果是输入框进行判断是否为字典, 如果是字典将进行翻译, 不是字典则直接存值
                                if(fieldItem.type == 'input' || fieldItem.type == 'textarea') {
                                    if(row[filedNames[1]+'_dictCode']) {
                                        newObj[fieldItem.options.name] = _this.dictFormat(row[filedNames[1]+'_dictCode'], row[filedNames[1]])
                                    }else {
                                        newObj[fieldItem.options.name] = row[filedNames[1]];
                                    }
                                }else {
                                    newObj[fieldItem.options.name] = row[filedNames[1]];
                                }
                            }
                        })
                        if(subFormFieldData.length > 0) {
                            //判断最后一条是否为空, 如果所有字段为空则进行更行, 否则追加一条
                            let subFormLastField = subFormFieldData[subFormFieldData.length -1];
                            if(!Object.values(subFormLastField).some(i=>!!i)) {
                                subFormFieldData.splice(subFormFieldData.length-1, 1, newObj )
                            }else {
                                subFormFieldData.push(newObj)
                            }
                        }
                    }else if(arr[i].type == 'grid-sub-form') {
                        //多行子表单组件
                        let gridSubFormDom = _this.$refs.vFormRef.getWidgetRef(arr[i].options.name);
                        //多行子表单下面的字段对象
                        let gridSubFormFindFields = gridSubFormDom.fieldWidgetList;
                        let newObj = {};
                        gridSubFormFindFields.forEach((fieldItem, index) => {
                            let filedNames = fieldItem.options.name.split('_and_');
                            if(filedNames.length > 1 && row[filedNames[1]] != undefined) {
                                //如果是输入类型组件进行判断是否为字典, 如果是字典将进行翻译, 不是字典则直接存值
                                if(fieldItem.type == 'input' || fieldItem.type == 'textarea') {
                                    if(row[filedNames[1]+'_dictCode']) {
                                        newObj[fieldItem.options.name] = _this.dictFormat(row[filedNames[1]+'_dictCode'], row[filedNames[1]])
                                    }else {
                                        newObj[fieldItem.options.name] = row[filedNames[1]];
                                    }
                                }else {
                                    newObj[fieldItem.options.name] = row[filedNames[1]];
                                }
                            }
                        })
                        //多行子表单字段的值
                        let gridSubFormFieldData = _this.$refs.vFormRef.getSubFormValues(arr[i].options.name);
                        if(gridSubFormFieldData.length > 0) {
                            //判断最后一条是否为空, 如果所有字段为空则进行更行, 否则追加一条
                            let gridSubFormLastField = gridSubFormFieldData[gridSubFormFieldData.length -1];
                            if(!Object.values(gridSubFormLastField).some(i=>!!i)) {
                                gridSubFormFieldData.splice(gridSubFormFieldData.length-1, 1, newObj )
                            }else {
                                gridSubFormFieldData.push(newObj)
                            }
                        }
                    }else if(arr[i].widgetList) {
                        recursiveFn(arr[i].widgetList)
                    }else if(arr[i].cols && arr[i].cols) {
                        recursiveFn(arr[i].cols)
                    }else{
                        //非子表单里面的字段进行赋值
                        let fieldItem = arr[i];
                        let filedNames = fieldItem.options.name.split('_and_');
                        if(filedNames.length > 1 && row[filedNames[1]] != undefined) {
                            if(fieldItem.type == 'input' || fieldItem.type == 'textarea') {
                                if(row[filedNames[1]+'_dictCode']) {
                                    commonFieldObj[fieldItem.options.name] = _this.dictFormat(row[filedNames[1]+'_dictCode'], row[filedNames[1]])
                                }else {
                                    commonFieldObj[fieldItem.options.name] = row[filedNames[1]];
                                }
                            }else {
                                commonFieldObj[fieldItem.options.name] = row[filedNames[1]];
                            }
                        }
                    }
                }
            }
            recursiveFn(widgetList);
            let formData = this.$refs.vFormRef.getFormData(false);
            console.log(formData,'formData', widgetList);

            formData = {
                ...formData,
                ...commonFieldObj
            }
            this.$refs.vFormRef.setFormData(formData)
        },
        onView(row) {
            this.$jumpPatient360({ path: "/cdrView", query: { empi: row.empi || row.EMPI } }, row)
        },
        submit() {
            let oldFormData = this.oldFormData;
            this.$refs.vFormRef.getFormData().then(formData => {
                //表单所有容器
                let allContainer = this.$refs.vFormRef.getContainerWidgets();
                //获取表单中所有字段的json
                let allFormFields = this.$refs.vFormRef.getFieldWidgets();
                let _this = this;
                let changeFieldObj = {}; //记录修改的数据对象集合

                for(let k in formData) {
                    //如果是子表单, 多行子表单为数组结构单独处理
                    if(Array.isArray(formData[k])) {
                        let fields = [];
                        //循环子表单数组
                        formData[k].forEach((item, index) => {
                            //如果字表单的数据索引大于老的数据, 就是新增的
                            if(oldFormData[k] && oldFormData[k].length > 0) {
                                if(index > oldFormData[k].length -1) {
                                    for(let itemK in item) {
                                        let fieldJson = allFormFields.filter(item => item.name == itemK)[0];
                                        let fieldOption = fieldJson.field.options;
                                        let obj = {
                                            dsEnabled: fieldOption.dsEnabled,
                                        };
                                        if(fieldOption.dict && fieldOption.dsEnabled) {
                                            _this.getDictData(fieldOption.dict);
                                            obj.dict = fieldOption.dict
                                        } else if(fieldOption.optionItems && fieldOption.optionItems.length > 0) {
                                            obj.optionItems = fieldOption.optionItems
                                        }
                                        let fieldName = itemK.split('_and_');
                                        if(item[itemK]) {
                                            fields.push({
                                                updateTableName: fieldName.length > 1 ? fieldName[0] : '', //表名
                                                updateFieldName: fieldName.length > 1 ? fieldName[1] : fieldName[0], //字段名
                                                updateFieldLabel: fieldOption.label, //字段label
                                                updateBeforeValue: '', //原始值
                                                updateAfterValue: item[itemK], //新值
                                                updateCause: '', //修改原因
                                                ...obj
                                            })
                                        }
                                    }
                                }else {
                                    // 否则就去比较当前这条数据里面的每个字段的值是否修改过
                                    for(let itemK in item) {
                                        let fieldJson = allFormFields.filter(item => item.name == itemK)[0];
                                        let fieldOption = fieldJson.field.options;
                                        let obj = {
                                            dsEnabled: fieldOption.dsEnabled,
                                        };
                                        if(fieldOption.dict && fieldOption.dsEnabled) {
                                            _this.getDictData(fieldOption.dict);
                                            obj.dict = fieldOption.dict
                                        } else if(fieldOption.optionItems && fieldOption.optionItems.length > 0) {
                                            obj.optionItems = fieldOption.optionItems
                                        }
                                        let fieldName = itemK.split('_and_');
                                        if(item[itemK] != oldFormData[k][index][itemK]) {
                                            fields.push({
                                                updateTableName: fieldName.length > 1 ? fieldName[0] : '', //表名
                                                updateFieldName: fieldName.length > 1 ? fieldName[1] : fieldName[0], //字段名
                                                updateFieldLabel: fieldOption.label, //字段label
                                                updateBeforeValue: oldFormData[k][index][itemK], //原始值
                                                updateAfterValue: item[itemK], //新值
                                                updateCause: '', //修改原因
                                                ...obj
                                            })
                                        }
                                    }
                                }
                            }else {
                                for(let itemK in item) {
                                    let fieldJson = allFormFields.filter(item => item.name == itemK)[0];
                                    let fieldOption = fieldJson.field.options;
                                    let obj = {
                                        dsEnabled: fieldOption.dsEnabled,
                                    };
                                    if(fieldOption.dict && fieldOption.dsEnabled) {
                                        _this.getDictData(fieldOption.dict);
                                        obj.dict = fieldOption.dict
                                    } else if(fieldOption.optionItems && fieldOption.optionItems.length > 0) {
                                        obj.optionItems = fieldOption.optionItems
                                    }
                                    let fieldName = itemK.split('_and_');
                                    if(item[itemK]) {
                                        fields.push({
                                            updateTableName: fieldName.length > 1 ? fieldName[0] : '', //表名
                                            updateFieldName: fieldName.length > 1 ? fieldName[1] : fieldName[0], //字段名
                                            updateFieldLabel: fieldOption.label, //字段label
                                            updateBeforeValue: '', //原始值
                                            updateAfterValue: item[itemK], //新值
                                            updateCause: '', //修改原因
                                            ...obj
                                        })
                                    }
                                }
                            }
                        })
                        //如果修改的字段数大于0
                       if(fields.length > 0) {
                           changeFieldObj[k] = fields;
                       }
                    }else {
                        let fieldName = k.split('_and_');
                        let newFieldValue = formData[k];
                        let oldFieldValue = oldFormData[k];
                        let fieldJson = this.$refs.vFormRef.getWidgetRef(k);
                        let fieldOption = fieldJson.field.options;
                        if(Array.isArray(newFieldValue) && Array.isArray(oldFieldValue) && newFieldValue.join(',') === oldFieldValue.join(',')) {
                            break;
                        }
                        if((oldFieldValue != undefined && oldFieldValue != newFieldValue) || (oldFieldValue == undefined && newFieldValue && newFieldValue.length != 0)) {
                            let obj = {
                                dsEnabled: fieldOption.dsEnabled || '',
                            };
                            if(fieldOption.dict && fieldOption.dsEnabled) {
                                _this.getDictData(fieldOption.dict);
                                obj.dict = fieldOption.dict
                            } else if(fieldOption.optionItems && fieldOption.optionItems.length > 0) {
                                obj.optionItems = fieldOption.optionItems
                            }
                            changeFieldObj[k] = {
                                updateTableName: fieldName.length > 1 ? fieldName[0] : '', //表名
                                updateFieldName: fieldName.length > 1 ? fieldName[1] : fieldName[0], //字段名
                                updateFieldLabel: fieldOption.label, //字段label
                                updateBeforeValue: oldFieldValue, //原始值
                                updateAfterValue: newFieldValue, //新值
                                updateCause: '', //修改原因
                                ...obj
                            }
                        }
                    }
                }
                /**
                 * 对于修改的字段进行分组, 以Card进行分组, 不在card容器的字段统一放在custom.fields
                 */
                let changeData = {
                    custom: {
                        card: '',
                        fields: [],
                    }
                }
                for(let k in changeFieldObj) {
                    allContainer.forEach(item => {
                        if(item.type == 'card') {
                            if(item.container.widgetList && item.container.widgetList.length > 0) {
                                recursiveFn(item.container.widgetList)
                            }
                            function recursiveFn (arr) {
                                for(let i = 0; i < arr.length; i++) {
                                    if(arr[i].options.name == k) {
                                        //如果是数组说明是子表单活多行子表单
                                        if(Array.isArray(changeFieldObj[k])) {
                                            if(changeData[item.container.options.name]) {
                                                changeData[item.container.options.name].card = item.container.options.label;
                                                changeData[item.container.options.name].fields = changeFieldObj[k];
                                            }else {
                                                changeData[item.container.options.name] = {
                                                    card: item.container.options.label,
                                                    fields: changeFieldObj[k]
                                                }
                                            }
                                            //否则就是单字段 嵌套在card容器里面
                                        }else {
                                            if(changeData[item.container.options.name]) {
                                                changeData[item.container.options.name].card = item.container.options.label;
                                                changeData[item.container.options.name].fields.push(changeFieldObj[k])
                                            }else {
                                                changeData[item.container.options.name] = {
                                                    card: item.container.options.label,
                                                    fields: [changeFieldObj[k]]
                                                }
                                            }
                                        }
                                        //已经取过值的将其删除, 剩余的字段就是不在card容器里面的
                                        delete changeFieldObj[k]
                                        break
                                    }else if(arr[i].widgetList) {
                                        recursiveFn(arr[i].widgetList)
                                    }else if(arr[i].cols) {
                                        recursiveFn(arr[i].cols)
                                    }
                                }
                            }
                        }
                    })
                }
                //将剩余未分组的字段统一放在custom.fields里面
                for(let k in changeFieldObj) {
                    if(Array.isArray(changeFieldObj[k])) {
                        changeData.custom.fields = [...changeData.custom.fields, ...changeFieldObj[k]]
                    }else {
                        changeData.custom.fields.push(changeFieldObj[k])
                    }
                }
                this.changeForm.changeFieldObj = changeData;
                let isShow = false;
                for(let key in changeData) {
                    if(changeData[key].fields && changeData[key].fields.length > 0) {
                        this.showCauseAlert = true;
                        return false;
                    }
                }
                if(!this.showCauseAlert) {
                    this.submitForm();
                }
            })
        },

        //提交操作
        submitForm() {
            this.$refs.vFormRef.getFormData().then(formData => {
                if(this.showCauseAlert) {
                   this.showCauseAlert = false;
                }
                let _this = this;
                this.loading = true;
                 let params = {
                    rgId: this.rgId,
                    empi: this.empi
                }
                let userOldInfo = null;
                if(this.type == 'add') {
                    let userInfo = {
                        address: '',
                        age: '',
                        del_flag: '',
                        birthday: '',
                        create_time: '',
                        disease_syscode: '',
                        gender: '',
                        id_card: '',
                        id_type: '',
                        nationality: '',
                        native_province: '',
                        pat_name: '',
                        phone: ''
                    }
                    for(let k in formData) {
                        if(typeof formData[k] == 'array') {
                            formData[k].forEach(row => {
                                for(let row_k in row) {
                                    let fieldNames = row_k.split('_and_');
                                    if(fieldNames.length > 1) {
                                        if(fieldNames[0] == 'sd_patient_info' && userInfo[fieldNames[1]] != undefined && !userInfo[fieldNames[1]]) {
                                            userInfo[fieldNames[1]] = row[row_k]
                                        }
                                    }
                                }
                            })
                        }else {
                            let fieldNames = k.split('_and_');
                            if(fieldNames.length > 1) {
                                if(fieldNames[0] == 'sd_patient_info' && userInfo[fieldNames[1]] != undefined && !userInfo[fieldNames[1]]) {
                                    userInfo[fieldNames[1]] = formData[k]
                                }
                            }
                        }
                    }
                    userOldInfo = userInfo;
                    params.idCardNo = userOldInfo.id_card;
                    params.phoneNumber = userOldInfo.phone;
                }else {
                    let basicInfo = [
                        'sd_patient_info_and_pat_name',
                        'sd_patient_info_and_gender',
                        'sd_patient_info_and_age',
                        'sd_patient_info_and_phone',
                        'sd_patient_info_and_birthday',
                        'sd_patient_info_and_id_type',
                        'sd_patient_info_and_id_card',
                        'sd_patient_info_and_address'
                    ]
                    basicInfo.forEach(item => {
                        if(formData[item] == undefined) {
                            formData[item] = this.oldFormData[item];
                        }
                    })
                    userOldInfo = this.userInfo;
                    //将修改的字段组装成需要的格式
                    let queueUpdateLogs = {
                        empi: this.empi,
                        formId: this.formKey,
                        updateExplain: this.changeForm.updateExplain,
                        fields: []
                    }
                    let changeFieldObj = this.changeForm.changeFieldObj;
                    for(let key in changeFieldObj) {
                        if(changeFieldObj[key].fields && changeFieldObj[key].fields.length > 0) {
                            changeFieldObj[key].fields.forEach(item => {
                                if(Array.isArray(item.updateAfterValue)) {
                                    item.updateAfterValue = item.updateAfterValue.join(',')
                                }
                                if(Array.isArray(item.updateBeforeValue)) {
                                    item.updateBeforeValue = item.updateBeforeValue.join(',')
                                }
                                let obj = {
                                    card: key,
                                    cardLabel: changeFieldObj[key].card || '',
                                    ...item
                                }
                                queueUpdateLogs.fields.push(obj);
                            })
                        }
                    }
                    params.queueUpdateLogs = queueUpdateLogs;
                }
                formData = {
                    ...formData,
                    ...userOldInfo,
                    // subFormKeys
                }
                params.dataJson = JSON.stringify(formData);
                if(this.type == 'add') {
                    addFormForMongo(params)
                    .then(res => {
                        this.loading = false;
                        this.$message.success('添加成功!');
                        const obj = { path: "/casegroup/studied", query: {rsId: this.rsId, rgId: this.rgId}};
                        this.$tab.closeOpenPage(obj);
                    })
                }else {
                    saveFormForMongo(params)
                    .then(res => {
                        this.loading = false;
                        this.$message.success('保存成功!');
                        const obj = { path: "/casegroup/studied", query: {rsId: this.rsId, rgId: this.rgId}};
                        this.$tab.closeOpenPage(obj);
                    })
                }

            }).catch( function(error) {

            })
        },
        //数据字典转文本
        dictFormat(dictName, val) {
            return this.selectDictLabel(this.dictList[dictName], val);
        }
    }
}
</script>

<style lang="scss" scoped>
    .studyCohortEdit-page {
        min-height: calc(100vh - 104px);
        background: #f8f8f8;
        padding: 10px;
        .content-title {
            font-size: 18px;
            line-height: 24px;
            color: #333;
            margin-bottom: 10px;
            position: relative;
            font-weight: bold;
            padding: 0 0 0 8px;
        }
        .content-title::before {
            content: "";
            position: absolute;
            top: 3px;
            left: -2px;
            height: 18px;
            width: 4px;
            background: #1890ff;
        }
        .fl-box {
            float: left;
            width: 240px;
            background: #fff;
            min-height: calc(100vh - 104px);
            position: relative;
            transition: 0.2s ease all;
            .fl-content {
                padding: 10px;
            }
            .form-dom-card-list {
                .card-label {
                    font-size: 16px;
                    line-height: 20px;
                    border-bottom: 1px solid #ddd;
                    padding: 8px 5px;
                    color: #666;
                    cursor: pointer;
                }
                .active {
                    // background: #1890ff;
                    color: #1890ff;
                }
            }
            .arrow-box {
                position: absolute;
                top: 50%;
                right: -9px;
                height: 40px;
                background: #d2d1cc;
                line-height: 40px;
                color: #fff;
                font-weight: bold;
                font-size: 22px;
                margin-top: -20px;
                cursor: pointer;
            }
        }
        .fr-box {
            float: right;
            width: 490px;
            background: #fff;
            position: relative;
            transition: 0.2s ease all;
            .arrow-box {
                position: absolute;
                top: 50%;
                left: -9px;
                height: 40px;
                background: #d2d1cc;
                line-height: 40px;
                color: #fff;
                font-weight: bold;
                font-size: 22px;
                margin-top: -20px;
                cursor: pointer;
            }
            .fr-content {
                height: calc(100vh - 104px);
                padding: 10px;
                overflow: auto;
            }
        }
        .setWidth {
            width: 0px !important;
        }
        .mid-box {
            margin: 0 500px 0 250px;
            background: #fff;
            height: calc(100vh - 104px);
            padding: 10px;
            display: flow-root;
            overflow: auto;
            transition: 0.2s ease margin
        }
        .setMarL {
            margin-left: 20px;
        }
        .setMarR {
            margin-right: 20px;
        }
        .btn-group {
            text-align: center
        }
        .form-box {
            height: 70vh;
            overflow: auto;
        }
        .card-item {
            margin-bottom: 10px;
        }
        .s-icon {
            margin: 0 0 0 20px;
            float: right;
            font-size: 20px;
            color: #1890ff;
        }
        .set-width-150 {
            width: 150px;
        }
        .set-width-100 {
            width: 120px;
        }
    }

    ::v-deep .demo-table-expand {
        padding: 0 12px;
        .el-form-item {
            margin-right: 0;
            margin-bottom: 8px;
            width: 100%;
             .el-form-item__label {
                width: 100px !important;
                color: #99a9bf !important;
                line-height: 24px;
            }
            .el-form-item__content {
                line-height: 24px;
            }
        }
    }
</style>
