<template>
  <div class="app-container dataAssertManage-page">
    <el-row :gutter="20">
      <el-col :span="24" :offset="0" v-for="(formItem, formIndex) in queryParams" :key="formIndex">
        <el-form :model="formItem" ref="queryForm" :inline="true" v-show="showSearch" label-width="60px">
          <el-form-item label="视图" prop="tableName" >
            <el-select v-model="formItem.tableName" placeholder="请选择" filterable size="small" @change="chooseTable($event, formIndex)">
              <el-option v-for="item in modulTableList"
                         :key="item.tableName"
                         :label="item.aliasName"
                         :value="item.tableName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item  prop="fieldName" label="字段">
            <el-select v-model="formItem.fieldName" placeholder="请选择" filterable size="small" @change="changeFieldName($event, formIndex)">
              <el-option v-for="item in searchFieldsObj[formItem.tableName]"
                         :key="item.elementName"
                         :label='item.aliasName'
                         :value="item.elementName">
                         <span>{{ item.aliasName }}</span>
                         <span style="color: #1890ff;padding-left:4px; float:right">填充率{{item.fillRate}}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item  prop="conditionOperator" label="运算符">
            <el-select v-model="formItem.conditionOperator" placeholder="请选择" filterable size="small">
              <el-option v-for="item in operativeSymbolList"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="参数" prop="conditionStartValue">
            <el-select v-if="formItem.dictCode" v-model="formItem.conditionStartValue" placeholder="请选择" size="small" filterable :disabled="formItem.conditionOperator == 'empty' || formItem.conditionOperator == 'notEmpty'">
              <el-option v-for="item in dictObj[formItem.dictCode]"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
            <el-date-picker unlink-panels 
              v-else-if="formItem.elementType == 'datetime'"
              v-model="formItem.conditionStartValue"
              value-format="yyyy-MM-dd"
              type="date"
              size="small"
              class="set-date-width"
              :disabled="formItem.conditionOperator == 'empty' || formItem.conditionOperator == 'notEmpty'"
              placeholder="选择日期">
            </el-date-picker>
            <el-input v-else v-model="formItem.conditionStartValue" :disabled="formItem.conditionOperator == 'empty' || formItem.conditionOperator == 'notEmpty'" placeholder="请输入" clearable  size="small"></el-input>
          </el-form-item>
          <el-form-item label="" >
            <el-button type="danger" icon="el-icon-minus" circle size="mini" v-if="queryParams.length > 1" @click="delSearchLine(formIndex)"></el-button>
            <el-button type="primary" icon="el-icon-plus" circle size="mini" v-if="formIndex == queryParams.length - 1" @click="addSearchLine"></el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div class="btn-box">
      <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      <el-button type="primary" plain icon="el-icon-search" size="mini" @click="getList('search')">搜索</el-button>
      <el-button type="warning" plain size="mini" icon="el-icon-download" @click="exportFile">导出</el-button>
      <el-button type="primary"  icon="el-icon-plus" size="mini" @click="addTab">添加列表</el-button>
    </div>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-tabs v-model="currentTab" type="card"  @tab-remove="removeTab" >
      <el-tab-pane
        v-for="(item, index) in tabList"
        :key="item.tableName"
        :label="item.total == 0 ? item.aliasName : item.aliasName + '('+(item.showLimit && item.showLimit == item.total ? `${item.showLimit}+` : item.total)+')'"
        :name="item.tableName"
        :closable="index == 0 ? false : true"
      >
        <el-table :data="item.tableList" border stripe v-loading="item.loading" @cell-dblclick="view">
          <el-table-column :show-overflow-tooltip="true" align="center" prop="pat_name" label="患者姓名">
            <template slot-scope="scope">
              <el-link type="primary" @click="view(scope.row)">{{ scope.row.pat_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" prop="gender" label="患者性别">
            <template slot-scope="scope">
              <div>
                <dict-tag v-if="sexIsDict" :options="dictObj['sd_dict_sex']" :value="scope.row.gender"/>
                <span v-else>{{scope.row.gender}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" prop="age" label="患者年龄"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" prop="id_card" label="身份证号"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" v-if="col.elementName != 'EMPI' && col.elementName != 'id_card' && col.elementName != 'pat_name' && col.elementName != 'gender' && col.elementName != 'age'" align="center" v-for="col in fieldsObj[item.tableName]"
                           :prop="col.elementName"
                           :key="col.elementName"
                           :label="col.aliasName">
            <template slot-scope="scope">
              <div v-if="col.dictCode">
                <dict-tag :options="dictObj[col.dictCode]" :value="scope.row[col.elementName]"/>
              </div>
              <div v-else class="content-text">{{scope.row[col.elementName]}}</div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="item.total > 0"
          :total="item.total"
          :page.sync="item.pageNum"
          :limit.sync="item.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>

    <el-dialog
      title="选择列表"
      :visible.sync="showAlert"
      width="500px"
      @close="showAlert = false"
      top="25vh">
      <div>
        <el-checkbox-group v-model="checkList">
          <el-checkbox class="cb-item" :label="item.tableName" v-for="(item, index) in modulTableList" :disabled="item.tableName == 'sd_patient_info'" :key="index">{{item.aliasName}}</el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer">
          <el-button @click="showAlert = false">取消</el-button>
          <el-button type="primary" @click="sure">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { listModel } from "@/api/system/mddmodel";
  import {listElement} from '@/api/system/mddelement'
  import {listData, exportDataAssets} from '@/api/disease/dataAssets'
  import { saveAs } from 'file-saver'

  export default {
    name: 'DataAssertManage',
    data() {
      return {
        showAlert: false,
        checkList: [], //选中的列表
        showSearch: true,
        queryParams: [{
          tableName: '',
          fieldName: '',
          conditionOperator: '',
          conditionStartValue: ''
        }],
        // operativeSymbolList: [
        //   {label: '大于', value: '>'},
        //   {label: '小于', value: '<'},
        //   {label: '大于等于', value: '>='},
        //   {label: '小于等于', value: '<='},
        //   {label: '等于', value: '='},
        //   {label: '不等于', value: '!='},
        //   {label: '包含', value: 'contains'},
        //   {label: '不包含', value: 'notContains'},
        //   {label: '为空', value: 'empty'},
        //   {label: '不为空', value: 'notEmpty'},
        // ],

        operativeSymbolList: [
          {label: '大于', value: '&gt;'},
          {label: '小于', value: '&lt;'},
          {label: '大于等于', value: '&gt;='},
          {label: '小于等于', value: '&lt;='},
          {label: '等于', value: '='},
          {label: '不等于', value: '!='},
          {label: '包含', value: 'contains'},
          {label: '不包含', value: 'notContains'},
          {label: '为空', value: 'empty'},
          {label: '不为空', value: 'notEmpty'},
        ],
        currentTab: null,
        tabList: [
          // {
          //     name: '病例列表',
          // id: '1',
          //     column: [
          //         {
          //             label: '姓名',
          //             prop: 'name',
          //             id: 1
          //         },
          //         {
          //             label: '年龄',
          //             prop: 'age',
          //             id: 2
          //         },
          //         {
          //             label: '性别',
          //             prop: 'sex',
          //             id: 3
          //         },
          //         {
          //             label: '出生日期',
          //             prop: 'birthday',
          //             id: 4
          //         }
          //     ],
          //     tableList: [
          //         {
          //             name: '测试',
          //             age: '20',
          //             sex: '男',
          //             birthday: '1999-01-01'
          //         },
          //         {
          //             name: '测试111',
          //             age: '20',
          //             sex: '男',
          //             birthday: '1999-01-01'
          //         }
          //     ]
          // }
        ],
        modulTableList: [], //表
        fieldsObj: {} , //所有表下面的字段
        searchFieldsObj: {}, //查询下拉选项字段
        dictObj: {}, //数据字典集合
        sexIsDict: true, //患者信息表下面的性别是否配置了字典
      }
    },
    watch: {
      currentTab(newVal) {
        if(newVal && newVal != 0) {
          let tabItem = this.tabList.filter(item => {
            if(item.tableName == newVal) {
              return item
            }
          })[0];
          //判断当前tab下是否存在tableList , 存在不进行请求操作
          if(!tabItem.tableList) {
            this.getList();
          }
        }
      }
    },
    mounted() {
      this.getModulList();
    },
    methods: {
      //添加一行查询条件
      addSearchLine() {
        this.queryParams.push(
          {
            tableName: '',
            fieldName: '',
            conditionOperator: '',
            conditionStartValue: ''
          }
        )
      },
      //删除一行查询条件
      delSearchLine(index) {
        this.queryParams.splice(index, 1)
      },
      /** 查询数据模型列表 */
      getModulList() {
        this.$modal.loading();
        this.loading = true;
        listModel({modelType: 1}).then((response) => {
          let list = response.rows || [];
          list.forEach((item, index) => {
            item.total = 0;
            item.pageNum = 1;
            item.pageSize = 10;
            item.loading = false;
            if(item.tableName == 'sd_patient_info') {
              this.chooseTable(item.tableName);
              this.checkList.push(item.tableName);
              this.tabList.push(item);
              this.currentTab = item.tableName;
            }
          })
          this.modulTableList = list;
          // if(this.modulTableList.length > 0) {
          //    this.chooseTable(this.modulTableList[0].tableName);
          //    this.checkList.push(this.modulTableList[0].tableName);
          //    this.tabList.push(this.modulTableList[0]);
          //    this.currentTab = this.modulTableList[0].tableName;
          // }
        });
      },
      //选择表  获取表下面的字段
      chooseTable(tableName, formIndex = null) {
        let fieldList = [];
        if(!this.fieldsObj[tableName]) {
          listElement({tableName: tableName})
            .then(res => {
              let list = res.data || [];
              let list1 = [];
              list.forEach((item, index) => {
                if(item.listFlag == 1) {
                  list1.push(item)
                  if(item.dictCode) {
                    if (item.dictCode && !this.dictObj[item.dictCode]) {
                      this.getDictData(item.dictCode);
                    }
                  }
                  if(tableName == 'sd_patient_info' && item.elementName == 'gender' && !item.dictCode) {
                    this.sexIsDict = false;
                  }
                }
              })
              //进行排序处理
              list1.sort((a, b) => {
                if(a.sortNum > b.sortNum) {
                  return 1
                } else {
                  return -1
                }
              })
              this.$set(this.fieldsObj, tableName, list1);
              if(formIndex != null) {
                this.queryParams[formIndex].fieldName = '';
              }
            })
          listElement({tableName: tableName, searchFlag: 1})
            .then(res => {
              let list = res.data || [];
              this.$set(this.searchFieldsObj, tableName, list);
            })
        }
      },

      //获取数据字典
      getDictData(dictCode) {
        this.getDicts(dictCode).then(res => {
          res.data.forEach(item => {
            item.label = item.dictLabel;
            item.value = item.dictValue;
            item.raw = { listClass: null };
          });
          this.$set(this.dictObj, dictCode, res.data);
        });
      },
      getList(type) {
        let tabItem = this.tabList.filter(item => {
          if(item.tableName == this.currentTab) {
            return item
          }
        })[0];
        tabItem.loading = true;
        if(type == 'search') {
          tabItem.pageNum = 1;
        }
        let params = {
          page: tabItem.pageNum - 1,
          pageSize: tabItem.pageSize,
          tabViewName: this.currentTab,
          searchList: this.queryParams
        }
        listData(params)
          .then(res => {
            let list = res.data.dataAssetsList || [];
            this.$set(tabItem, 'tableList', list);
            tabItem.total = res.data.total;
            tabItem.showLimit = res.data.showLimit;
            tabItem.loading = false;
            this.$modal.closeLoading();
          })
      },
      //重置
      resetQuery() {
        this.queryParams = [
          {
            tableName: '',
            fieldName: '',
            conditionOperator: '',
            conditionStartValue: ''
          }
        ]
        this.getList('search');
      },
      //显示弹窗
      addTab() {
        this.checkList = this.tabList.map(item =>{return item.tableName})
        this.showAlert = true;
      },
      sure() {
        let arr = [];
        this.checkList.forEach(tableName => {
          for(let i = 0; i < this.modulTableList.length; i++) {
            if(tableName == this.modulTableList[i].tableName) {
              arr.push(this.modulTableList[i])
              this.chooseTable(tableName)
            }
          }
        })
        this.tabList = arr;
        this.showAlert = false;
      },
      //查询条件切换字段
      changeFieldName(elementName, formItemIndex) {
        let formItem = this.queryParams[formItemIndex];
        let fieldList = this.searchFieldsObj[formItem.tableName];
        fieldList.find(item => {
          if(item.elementName == elementName) {
            if (item.dictCode && !this.dictObj[item.dictCode]) {
              this.getDictData(item.dictCode);
            }
            formItem.elementType = item.elementType;
            formItem.dictCode = item.dictCode || '';
          }
        })
      },
      removeTab(tableName) {
        let tabs = this.tabList;
        let activeName = this.currentTab;
        if (activeName == tableName) {
          tabs.forEach((tab, index) => {
            if (tab.tableName == tableName) {
              let nextTab = tabs[index + 1] || tabs[index - 1];
              if (nextTab) {
                activeName = nextTab.tableName;
              }
            }
          })
        }
        this.currentTab = activeName;
        this.tabList = tabs.filter(tab => tab.tableName !== tableName);
      },
      //导出
      exportFile() {
        let tabItem = this.tabList.filter(item => {
          if(item.tableName == this.currentTab) {
            return item
          }
        })[0];
        let params = {
          page: tabItem.pageNum - 1,
          pageSize: tabItem.pageSize,
          tabViewName: this.currentTab,
          searchList: this.queryParams
        }
        const loading = this.$loading({
          lock: true,
          text: '正在下载数据，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportDataAssets(params)
          .then(res => {
            const blob = new Blob([res])
            saveAs(blob, `data_${new Date().getTime()}.xlsx`);
            loading.close();
          })
        // this.download('disease/dataAssets/export', params, `data_${new Date().getTime()}.xlsx`);
      },
      //
      view(row) {
        this.$jumpPatient360({ path: "/cdrView", query: { empi: row.EMPI } }, row);
      }
    }
  }
</script>

<style lang="scss" >
  .dataAssertManage-page {
    .el-button--mini.is-circle {
      padding: 5px;
    }
    .btn-box {
      margin: 10px 0 0 20px;
    }
    .cb-item {
      margin-bottom: 10px;
    }
    .el-form-item {
      margin-bottom: 4px;
    }
    .content-text {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .set-date-width {
      width: 215px;
    }
  }
</style>
