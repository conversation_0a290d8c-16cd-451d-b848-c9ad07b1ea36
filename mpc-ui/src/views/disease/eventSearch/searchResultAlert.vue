<template>
  <div>
    <el-dialog title="查询结果" :visible.sync="isShow" width="1000px" @close="isShow = false" :append-to-body="false"
      :modal-append-to-body="false">
      <el-table :data="list" border stripe>
        <el-table-column label="姓名" prop="patName"></el-table-column>
        <el-table-column label="性别" prop="gender">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sd_dict_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="年龄" prop="age"></el-table-column>
        <el-table-column label="手机号" prop="phone"></el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="130">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="view(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="pageNum" :limit.sync="pageSize"
        @pagination="getList()" />
      <span slot="footer">
        <el-button type="primary" @click="isShow = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getEventSearchResult } from '@/api/disease/caseManage'
export default {
  dicts: ["sd_dict_sex"],
  data() {
    return {
      isShow: false,
      list: [],
      selectKey: '',
      total: 0,
      pageNum: 1,
      pageSize: 10,
    }
  },
  methods: {
    init(key) {
      this.selectKey = key;
      this.pageNum = 1;
      this.getList();
      this.isShow = true;
    },
    getList() {
      this.$modal.loading();
      let params = {
        selectKey: this.selectKey,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      getEventSearchResult(params)
        .then(res => {
          this.list = res.rows || [];
          this.total = res.total || 0;
          this.$modal.closeLoading();
        }).catch(_ => {
          this.$modal.closeLoading();
        })
    },
    view(row) {
      let empi = row.EMPI ?? row.empi;
      this.$jumpPatient360({ path: "/cdrView", query: { empi: empi } }, row);
    },
  }
}
</script>

<style></style>
