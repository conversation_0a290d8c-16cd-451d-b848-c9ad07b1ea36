<template>
  <div class="searchpage app-container">
    <div class="head-box">
      <el-form
        :model="searchForm"
        ref="form"
        label-width="90px"
        :inline="true"
        size="small"
        label-position="right">
        <div class="first-line"> 
          <el-form-item class="keyWord-ipt" label="关键词检索" prop="keyWord">
            <el-input
              size="medium "
              class="set-ipt-width"
              v-model="searchForm.keyWord"
              placeholder="诊断名称&入院录或出院记录&手术&检查(多个关键词使用空格隔开)"
              clearable>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button :disabled="showLoading" plain type="primary" icon="el-icon-search" size="medium " @click="search">搜索</el-button>
            <el-button icon="el-icon-refresh" size="medium " @click="resetForm">重置</el-button>
            <el-button type="primary" icon="el-icon-plus" size="medium " :disabled="total == 0" @click="addProject">添加到初筛队列</el-button>
          </el-form-item>
          <div class="fr-btn-group">
            <el-button class="btn" type="text" size="medium" icon="el-icon-search" @click="toPage('/advanced')">高级查询</el-button>
            <el-button class="btn" type="text" size="medium" icon="el-icon-user-solid" @click="toPage('/precise')">患者编号检索</el-button>
          </div>
        </div>
        <div class="set-bg">
          <el-form-item label="就诊类型" prop="cureType">
            <el-radio-group v-model="searchForm.cureType" @change="search">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button :label="dict.value" v-if="dict.label == '门诊' || dict.label == '住院'" v-for="(dict, index) in dict.type.sd_dict_cureType" :key="index">{{dict.label}}</el-radio-button>
            </el-radio-group>
            <!-- <el-select v-model="searchForm.cureType" clearable class="set-sel-width">
              <el-option v-for="(dict, index) in dict.type.sd_dict_cureType" :label="dict.label" :value="dict.value" :key="index"></el-option>
            </el-select> -->
          </el-form-item>
          <el-form-item label="就诊时间" prop="treatmentDate">
            <el-date-picker unlink-panels 
              class="set-date-width"
              v-model="searchForm.treatmentDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="患者性别" prop="gender">
            <!-- <el-select v-model="searchForm.gender" clearable class="set-sel-width">
              <el-option v-for="(dict, index) in dict.type.sd_dict_sex" :label="dict.label" :value="dict.value" :key="index"></el-option>
            </el-select> -->
            <el-radio-group v-model="searchForm.gender"  @change="search">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button v-for="(dict, index) in dict.type.sd_dict_sex" :label="dict.value" :key="index">{{dict.label}}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="患者年龄段">
            <!-- <el-row :gutter="0">
              <el-col :span="11" :offset="0"> -->
                <el-input placeholder="输入年龄" v-model="searchForm.age1" class="set-sel-ipt-width">
                  <template slot="prepend">
                    <el-select class="selectType" v-model="searchForm.ageType1">
                      <el-option label="大于" value="("></el-option>
                      <el-option label="大于等于" value="["></el-option>
                    </el-select>
                  </template>
                </el-input>
              <!-- </el-col>
              <el-col :span="2"> -->
                <span style="text-align: center; padding: 0 5px;">-</span>
              <!-- </el-col>
              <el-col :span="11" :offset="0"> -->
                <el-input
                  placeholder="输入年龄"
                  v-model="searchForm.age2"
                  class="set-sel-ipt-width">
                  <template slot="prepend">
                    <el-select class="selectType" v-model="searchForm.ageType2">
                      <el-option label="小于" value=")"></el-option>
                      <el-option label="小于等于" value="]"></el-option>
                    </el-select>
                  </template>
                </el-input>
              <!-- </el-col>
            </el-row> -->
          </el-form-item>
         
        </div>
      </el-form>
    </div>
    <div v-if="caseList.length > 0">
      <div class="total">筛选结果<span>共{{ total }}名患者</span></div>
      <div class="table-list" >
        <div class="list-item" v-for="(item, index) in caseList" :key="index">
          <el-row :gutter="10">
            <el-col :span="5" :offset="0">
              <span class="label">就诊院区:</span>
              <div class="field-value">{{item.searchHit.sourceAsMap.sourceHospital == 'null' ? '' : item.searchHit.sourceAsMap.sourceHospital}}</div>
            </el-col>
            <el-col :span="5" :offset="0">
              <span class="label">就诊类型:</span>
              <div class="field-value">
                <dict-tag :options="dict.type.sd_dict_cureType" :value="item.searchHit.sourceAsMap.cure_type"/>
              </div>
            </el-col>
            <!-- <el-col :span="5" :offset="0">
              <span class="label">主索引号:</span>
              <div class="field-value">{{item.searchHit.sourceAsMap.empi}}</div>
            </el-col> -->
            <el-col :span="5" :offset="0">
              <span class="label">就诊科室:</span>
              <div class="field-value">{{item.searchHit.sourceAsMap.dept_name}}</div>
            </el-col>
             <el-col :span="5" :offset="0">
              <span class="label">就诊日期:</span>
              <div class="field-value">{{item.searchHit.sourceAsMap.admit_data && item.searchHit.sourceAsMap.admit_data != 'null' ? formatDate(item.searchHit.sourceAsMap.admit_data) : ''}}</div>
            </el-col>
            <el-col :span="4" :offset="0">
              <span class="label">门诊/住院号:</span>
              <div class="field-value">{{item.searchHit.sourceAsMap.admit_no}}</div>
            </el-col>
            <!-- <el-col :span="5" :offset="0">
              <span class="label">就诊编号:</span>
              <div class="field-value">test</div>
            </el-col> -->
            
          </el-row>
          <el-row :gutter="10">
            <el-col :span="5" :offset="0">
              <span class="label">患者姓名:</span>
              <div class="field-value set-pointer" @click="view(item)">
                <span class="pat-name">{{item.searchHit.sourceAsMap.pat_name}}</span>
                <img class="view-icon" src="@/assets/images/disease/view-360.png">
              </div>
            </el-col>
            <el-col :span="5" :offset="0">
              <span class="label">患者性别:</span>
              <div class="field-value">
                <!-- <dict-tag :options="dict.type.sd_dict_sex" :value="item.searchHit.sourceAsMap.gender"/> -->
                {{dictFormat(item.searchHit.sourceAsMap.gender)}}
              </div>
            </el-col>
            <el-col :span="5" :offset="0">
              <span class="label">患者年龄:</span>
              <div class="field-value">{{item.searchHit.sourceAsMap.age}}</div>
            </el-col>
            <el-col :span="5" :offset="0">
              <span class="label">手机号:</span>
              <div class="field-value">{{item.searchHit.sourceAsMap.phone}}</div>
            </el-col>
            <el-col :span="4" :offset="0">
              <span class="label">家庭住址:</span>
              <div class="field-value">{{item.searchHit.sourceAsMap.address}}</div>
            </el-col>
          </el-row>
          <!-- <el-row :gutter="10">
            <el-col :span="24" :offset="0">
              <span class="label">病案资料:</span>
              <div class="field-value">test</div>
            </el-col>
          </el-row> -->
          <el-row :gutter="10" v-if="item.highlightMap.diagnostic_name && item.highlightMap.diagnostic_name.length > 0">
            <el-col :span="24" :offset="0">
              <span class="label set-col">诊断名:</span>
              <div class="field-value">
                <div class="item-box" v-for="(item, index) in item.highlightMap.diagnostic_name"
                  :key="index"
                  v-html="item">
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-if="item.highlightMap.admission_record && item.highlightMap.admission_record.length > 0">
            <el-col :span="24" :offset="0">
              <span class="label set-col">入院录:</span>
              <div class="field-value">
                <div class="item-box" v-for="(item, index) in item.highlightMap.admission_record"
                  :key="index"
                  v-html="item">
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-if="item.highlightMap.discharge_record && item.highlightMap.discharge_record.length > 0">
            <el-col :span="24" :offset="0">
              <span class="label set-col">出院记录:</span>
              <div class="field-value">
                <div class="item-box" v-for="(item, index) in item.highlightMap.discharge_record"
                  :key="index"
                  v-html="item">
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-if="item.highlightMap.surgical_record && item.highlightMap.surgical_record.length > 0">
            <el-col :span="24" :offset="0">
              <span class="label set-col">手术记录:</span>
              <div class="field-value">
                <div class="item-box" v-for="(item, index) in item.highlightMap.surgical_record"
                  :key="index"
                  v-html="item">
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-if="item.highlightMap.check_finds && item.highlightMap.check_finds.length > 0">
            <el-col :span="24" :offset="0">
              <span class="label set-col">检查所见:</span>
              <div class="field-value">
                <div class="item-box" v-for="(item, index) in item.highlightMap.check_finds"
                  :key="index"
                  v-html="item">
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-if="item.highlightMap.check_results && item.highlightMap.check_results.length > 0">
            <el-col :span="24" :offset="0">
              <span class="label set-col">检查结果:</span>
              <div class="field-value">
                <div class="item-box" v-for="(item, index) in item.highlightMap.check_results"
                  :key="index"
                  v-html="item">
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="searchForm.pageNum"
        :limit.sync="searchForm.pageSize"
        @pagination="rapidSearch"
      />
    </div>
    <contentNull v-else-if="isFirstLoading || !isSearchData" tipText="请输入查询条件进行搜索" :imgSrc="require('@/assets/images/img-null1.png')"></contentNull> 
    <contentNull v-else tipText="暂无数据" :imgSrc="require('@/assets/images/img-null3.png')" :width="300"></contentNull> 
    <!-- <el-dialog
      title="添加到初筛队列"
      :visible.sync="showSavaProjectAlert"
      width="440px"
      :close-on-click-modal="false"
      @close="showSavaProjectAlert = false">
      <el-form
        :model="projectForm"
        ref="projectForm"
        label-width="90px"
        :inline="false"
        size="normal"
      >
        <el-form-item label="课题" prop="rsId"
          :rules="[
            { required: true, message: '请选择课题', trigger: 'change' },
          ]">
          <el-select v-model="projectForm.rsId" placeholder="请选择课题" filterable>
            <el-option
              v-for="item in topicList"
              :key="item.rsId"
              :label="item.subjectName"
              :value="item.rsId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="projectForm.rsId" prop="rgId" label="初筛队列"
          :rules="[
            { required: true, message: '请选择初筛队列', trigger: 'change' },
          ]">
          <el-select v-model="projectForm.rgId" placeholder="请选择初筛队列" filterable>
            <el-option
              v-for="item in experimentalGroupList"
              :key="item.rgId"
              :label="item.groupName"
              :value="item.rgId"
            ></el-option>
          </el-select>
          <el-button class="add-group-btn" type="text" size="mini" icon="el-icon-plus" @click="showAddAlert">创建队列</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="showSavaProjectAlert = false">取消</el-button>
        <el-button type="primary" @click="saveProject">保存</el-button>
      </span>
    </el-dialog> -->

    <!-- 添加研究队列弹窗 -->
    <!-- <addStudyCohortAlert ref="addStudyCohortAlert" :topicList="topicList" @getGrouplist="getGrouplist"></addStudyCohortAlert> -->
     <!-- 保存初筛队列弹窗 -->
    <saveInitialQueueAlert ref="saveInitialQueueAlert" @saveProject="saveProject"></saveInitialQueueAlert>
  </div>
</template>
<script>
import {
  caseSearch,
  getTopicList,
  getGrouplist,
  addProject,
  searchById,
  caseSearchByCondition,
  caseSearchConditionByCondition,
  customExport,
  updateProject
} from "@/api/disease/caseManage";
import { getCasegroup } from "@/api/disease/casegroup";
import { formatDate } from "@/utils";
import { downloadFile } from "@/utils/request";
import { selectMddElementList } from "@/api/system/mddmodel";
import { getTableFindElementList } from "@/api/system/mddelement";
import { mapGetters } from "vuex";
import $ from "jquery";
import contentNull from '@/components/contentNull'
import addStudyCohortAlert from './addStudyCohortAlert'
import saveInitialQueueAlert from './saveInitialQueueAlert'
export default {
  dicts: ["sd_dict_sex", "sd_dict_cureType"],
  name: "Biodata",
  data() {
    return {
      formatDate,
      isFirstLoading: true, //是否第一次加载
      isSearchData: false, //是否搜索过数据
      showLoading: false, //加载中弹窗
      showSavaProjectAlert: false, //保存项目弹窗
      searchType: "1", //搜索类型: 1快速查询 2条件筛选 3.ID精确检索
      showSearch: false,
      total: 0,
      checkedIds: [], //列表选中的id
      searchForm: {
        keyWord: "", //关键词
        selectType: "1", //查询类型
        treatmentDate: [], //就诊时间
        gender: '', //性别
        cureType: '', //就诊类型
        treatmentDepartment: [], //就诊科室
        surgeryName: [], //手术名称
        pageNum: 1,
        pageSize: 10,
        ageType1: "(",
        age1: "",
        ageType2: ")",
        age2: ""
      },
      caseList: [], //快速筛选列表
      projectForm: {
        rsId: "",
        rgId: "",
        projectName: "", //项目名称
        projectInfo: "", //项目描述
        projectCode: "", //项目编号
        elasticsearchVO: "", //模糊查询条件
        resultSetMode: "1", //保存类型 0: 查询条件 1: 查询结果
      },
      topicList: [], //课题列表
      experimentalGroupList: [], //实验组列表
      customFormList: [], //自定义表单列表
      operativeSymbolList: {
        ">": "大于",
        "<": "小于",
        ">=": "大于等于",
        "<=": "小于等于",
        "=": "等于",
        "!=": "不等于",
        contains: "包含",
        notContains: "不包含",
        empty: "为空",
        notEmpty: "不为空"
      },
      cgId: null, //项目ID,
      projectDetail: null,
    };
  },
  components: {
    contentNull,
    addStudyCohortAlert,
    saveInitialQueueAlert
  },
  computed: {
    ...mapGetters(["currentSelectedDisease"]),
  },
  watch: {
    // "projectForm.rsId": function(newVal, oldVal) {
    //   this.projectForm.rgId = "";
    //   this.experimentalGroupList = [];
    //   if (newVal) {
    //     this.getGrouplist();
    //   }
    // },
    // searchType: function(newVal, oldVal) {
    //   this.checkedIds = [];
    // },
    // currentSelectedDisease: {
    //   handler(newVal, oldVal) {
    //     this.resetForm();
    //   },
    //   deep: true,
    //   immediate: true
    // },

    // 监听路由是否变化
    // $route(to, from) {
      
    //   //当详情页进入此页面后, 在点击左侧菜单 则会进入if判断
    //   if (!to.query.cgId && this.cgId) {
    //     // 把最新id赋值给定义在data中的id
    //     this.cgId = ""; 
    //     this.resetForm(false);
    //   }
    // }
  },
  mounted() {
    let cgId = this.$route.query.cgId || '';
    if(cgId) {
      this.cgId = cgId;
      this.getProjectDetail(cgId);
    }else {
      this.isFirstLoading = false;
    }
  },
  activated() {
    let cgId = this.$route.query.cgId;
    if (cgId && cgId != this.cgId && !this.isFirstLoading) {
      this.resetForm();
      this.cgId = cgId;
      this.getProjectDetail(cgId);
    }
  },
  methods: {
    //获取项目详情
    getProjectDetail(pId) {
      getCasegroup(pId).then(res => {
        this.projectDetail = res.data;
        this.projectForm.rsId = res.data.rsId;
        //增加延时赋值 因为rsID赋值会触发监听事件,会将rgID进行清空处理
        setTimeout(_ => {
          this.projectForm.rgId = res.data.rgId;
        }, 2000);
        this.projectForm.projectName = res.data.projectName; //项目名称
        this.projectForm.projectInfo = res.data.projectInfo; //项目描述
        this.projectForm.projectCode = res.data.projectCode; //项目编号
        this.projectForm.resultSetMode = res.data.resultSetMode;
        this.searchType = res.data.searchType;
        let conditionSearchJson = JSON.parse(res.data.conditionSearchJson);
        this.$nextTick(_ => {
          let obj = {
            keyWord: conditionSearchJson.keyWord,
            selectType: "1", //查询类型
            treatmentDate: [], //就诊时间
            gender: conditionSearchJson.gender ? String(conditionSearchJson.gender) : null,
            cureType: conditionSearchJson.cureType,
            treatmentDepartment: [],
            surgeryName: [],
            pageNum: 1,
            pageSize: 10,
            ageType1: conditionSearchJson.ageRange.substr(0, 1),
            age1: "",
            ageType2: conditionSearchJson.ageRange.substr(-1),
            age2: ""
          };
          let ages = conditionSearchJson.ageRange.substring(
            1,
            conditionSearchJson.ageRange.length - 1
          );
          if (ages) {
            ages = ages.split(",");
            obj.age1 = ages[0];
            obj.age2 = ages[1];
          }
          if (conditionSearchJson.cureStartDate && conditionSearchJson.cureEndDate) {
            obj.treatmentDate = [
              conditionSearchJson.cureStartDate,
              conditionSearchJson.cureEndDate
            ];
          }
          this.searchForm = obj;
          this.search();
        });
      });
    },
    view(row) {
      let empi = row.searchHit.sourceAsMap.empi;
      this.$jumpPatient360({ path: "/cdrView", query: { empi: empi } }, row.searchHit.sourceAsMap);
    },
    //查询
    search() {
      this.searchForm.pageNum = 1;
      this.rapidSearch();
    },
    //快速查询
    rapidSearch() {
      if(!this.searchForm.keyWord) {
        this.$message.error("请输入查询内容!");
        return false;
      }
      if (this.searchForm.age2 && !this.searchForm.age1) {
        this.$message.error("请将年龄段填写完整!");
        return false;
      }
      this.$modal.loading();
      let params = {
        indexName: "medical_record",
        keyWord: this.searchForm.keyWord,
        ageRange: `${this.searchForm.ageType1}${this.searchForm.age1}${
          this.searchForm.age1 ? "," : ""
        }${this.searchForm.age2}${this.searchForm.ageType2}`,
        gender: this.searchForm.gender,
        cureType: this.searchForm.cureType,
        cureStartDate: this.searchForm.treatmentDate
          ? this.searchForm.treatmentDate[0]
          : "",
        cureEndDate: this.searchForm.treatmentDate
          ? this.searchForm.treatmentDate[1]
          : "",
        page: this.searchForm.pageNum - 1,
        pageSize: this.searchForm.pageSize,
        diseaseSyscode: this.currentSelectedDisease.diseaseSyscode
      };
      this.showLoading = true;
      caseSearch(params)
        .then(res => {
          let list = res.data.histResultList || [];
          this.caseList = list;
          this.total = res.data.total || 0;
          this.showLoading = false;
          this.extra = res.data.extra || '';
          this.isFirstLoading = false;
          this.isSearchData = true;
          this.$modal.closeLoading();
        })
        .catch(err => {
          this.showLoading = false;
          this.$modal.closeLoading();
        });
    },

    //重置
    resetForm(isSearch = true) {
      // this.$refs.form.resetFields();
      // this.searchForm.age1 = "";
      // this.searchForm.age2 = "";
      this.searchForm = {
        keyWord: "", //关键词
        selectType: "1", //查询类型
        treatmentDate: [], //就诊时间
        gender: null, //性别
        cureType: null, //就诊类型
        treatmentDepartment: [], //就诊科室
        surgeryName: [], //手术名称
        pageNum: 1,
        pageSize: 10,
        ageType1: "(",
        age1: "",
        ageType2: ")",
        age2: ""
      }
      this.caseList = [];
      this.total = 0;
      this.projectForm = {
        rsId: "",
        rgId: "",
        projectName: "", //项目名称
        projectInfo: "", //项目描述
        projectCode: "", //项目编号
        elasticsearchVO: "", //模糊查询条件
        conditionSearchObject: "", //条件查询条件
        sdPatientInfo: "", //id精确检索条件
        resultSetMode: "1", //保存类型 0: 查询条件 1: 查询结果
      }
      this.projectDetail = null;
      this.isSearchData = false;
      // this.isFirstLoading = true;
      //因为当前页面加了默认选中就诊类型为全部 , 所以每次重置时都需重新调用接口
      // if(isSearch) {
      //   this.search();
      // }
    },

    //新增保存项目
    addProject() {
      // if (this.cgId && this.currentSelectedDisease.diseaseSyscode != this.projectDetail.diseaseSyscode) {
      //   this.$confirm(
      //     `当前病种已切换为‘${this.currentSelectedDisease.diseaseAlias}’，保存后此初筛队列所属病种将变更为‘${this.currentSelectedDisease.diseaseAlias}’，是否继续操作？`,
      //     "提示",
      //     {
      //       confirmButtonText: "是",
      //       cancelButtonText: "否",
      //       type: "warning"
      //     }
      //   ).then(res => {
      //       this.showSavaProjectAlert = true;
      //       this.$nextTick(_ => {
      //         this.$refs.projectForm.resetFields();
      //         this.projectForm.rsId = "";
      //         this.projectForm.rgId = "";
      //       });
      //     }).catch(_ => {});
      // } else {
      //   this.showSavaProjectAlert = true;
      //   this.$nextTick(_ => {
      //     if(!this.cgId) {
      //       this.$refs.projectForm.clearValidate();
      //     }
      //     if (this.projectDetail && this.projectDetail.rsId) {
      //       this.projectForm.rsId = this.projectDetail.rsId;
      //       //增加延时赋值 因为rsID赋值会触发监听事件,会将rgID进行清空处理
      //       setTimeout(_ => {
      //         this.projectForm.rgId = this.projectDetail.rgId;
      //       }, 1000);
      //     }
      //   });
      // }
      // this.getTopicList();
      // this.showSavaProjectAlert = true;
      // this.$nextTick(_ => {
      //   this.$refs.projectForm.resetFields();
      // })
      this.$refs.saveInitialQueueAlert.initData();
    },

    //获取课题列表
    getTopicList() {
      getTopicList({
        diseaseSyscode: this.currentSelectedDisease.diseaseSyscode
      }).then(res => {
        this.topicList = res.data || [];
      });
    },

    //获取实验组列表
    getGrouplist() {
      getGrouplist({ rs_id: this.projectForm.rsId }).then(res => {
        this.experimentalGroupList = res.data || [];
      });
    },
    //保存项目
    saveProject(data, rgId) {
      // this.$refs.projectForm.validate(valid => {
      //   if (valid) {
          // let subjectName = ""; //课题名称
          // this.topicList.forEach(item => {
          //   if (item.rsId == this.projectForm.rsId) {
          //     subjectName = item.subjectName;
          //   }
          // });
          // let groupName = ""; //组名称
          // this.experimentalGroupList.forEach(item => {
          //   if (item.rgId == this.projectForm.rgId) {
          //     groupName = item.groupName;
          //   }
          // });
          let obj = {
            // rsId: this.projectForm.rsId,
            // subjectName: subjectName,
            // rgId: this.projectForm.rgId,
            // groupName: groupName,
            ...data,
            projectName: this.projectForm.projectName,
            projectInfo: this.projectForm.projectInfo,
            searchType: this.searchType,
            projectCode: this.projectForm.projectCode,
            diseaseSyscode: this.currentSelectedDisease.diseaseSyscode,
            resultSetMode: this.projectForm.resultSetMode,
            keyMD5Data: this.extra
          };
          obj.patientTotal = this.total;
          obj.elasticsearchVO = {
              indexName: "medical_record",
              keyWord: this.searchForm.keyWord,
              ageRange: `${this.searchForm.ageType1}${this.searchForm.age1}${
                this.searchForm.age1 ? "," : ""
              }${this.searchForm.age2}${this.searchForm.ageType2}`,
              gender: this.searchForm.gender,
              cureType: this.searchForm.cureType,
              cureStartDate: this.searchForm.treatmentDate[0] || "",
              cureEndDate: this.searchForm.treatmentDate[1] || "",
              page: 0,
              pageSize: 10
          };
           let confirmTipText = data.isAutoGroup == 1 ? `确定将查询的病例保存到${data.subjectName}中吗? 系统将随机把病例入组到课题下的队列中。` : `确定将查询的病例保存到“${data.groupName}”中吗? `
          this.$confirm(`${confirmTipText} 系统会自动过滤已添加的病例!`,"提示",{
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(_ => {
            // this.showSavaProjectAlert = false;
            this.$refs.saveInitialQueueAlert.close();
            const loading = this.$loading({
              lock: true,
              text: '正在保存...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            obj.varsTotal = 3;
            addProject(obj).then(res => {
              loading.close();
              this.$confirm(`保存成功, 在队列记录中查看添加状态!`, '提示', {
                confirmButtonText: "确定",
                showCancelButton: false,
                showClose: false
              }).then(_ => {
                this.$router.push({ path: "/casegroup/initial", query: {cgId: data.isAutoGroup == 1 ? rgId : data.rgId}});
              })
            }).catch(_ => {
              loading.close();
            });;
          }).catch(_ => {})
      //   }
      // });
    },
    //切换查询页面
    toPage(path) {
      this.$router.push({path})
    },
    //数据字典转文本
    dictFormat(val) {
      return this.selectDictLabel(this.dict.type.sd_dict_sex, val);
    },
    //显示添加队列弹窗
    showAddAlert() {
      this.$refs.addStudyCohortAlert.init(this.projectForm.rsId)
    }
    
  }
};
</script>

<style lang="scss">
.searchpage {
  height: 100%;
  padding-top: 0;
  .head-box {
    padding: 15px 0px 0;
    overflow: hidden;
    .first-line {
      overflow: hidden;
    }
    .fr-btn-group {
      float: right;
    }
    .set-ipt-width {
      width: 500px
    }
    .set-date-width {
      width: 220px;
    }
    .set-sel-width {
      width: 110px;
    }
    .set-sel-ipt-width {
      width: 200px;
    }
    .keyWord-ipt {
      .el-form-item__label {
        font-size: 15px;
      }
    }
  }
  .set-bg {
    background: #eee;
    padding: 10px 10px 0px 0;
    margin: 0px 0 10px;
    border-radius: 5px;
    .el-form-item--small.el-form-item {
      margin-bottom: 10px;
    }
  }
  .tab {
    padding: 0 10px;
  }
  .selectType .el-input {
    width: 97px;
  }
  .el-form-item--small .el-radio {
    margin-bottom: 2px;
  }
  .el-checkbox {
    margin-right: 15px;
  }
  .arrow-icon {
    position: absolute;
    top: 49%;
    right: -23px;
    font-size: 23px;
    width: 22px;
    height: 52px;
    background: #0b5ca7;
    line-height: 52px;
    text-align: center;
    color: #fff;
    cursor: pointer;
  }
  .total {
    padding: 0 0 15px 10px;
    font-size: 18px;
    font-weight: bold;
    span {
      font-size: 15px;
      color: #666;
      padding: 0 0 0 10px;
      font-weight: normal;
    }
  }
  .detail-box {
    padding: 0 20px 15px;
    .d-label {
      font-size: 14px;
      font-weight: bold;
    }
    .item-box {
      padding-top: 6px;
    }
  }
  .detail-list {
    padding: 0 20px 5px;
  }
  .set-fr-width {
    width: 0px;
    margin: 0;
    overflow: hidden;
    // transition: .4s ease all;
  }
  .table-list {
    .list-item {
      overflow: hidden;
      border-bottom: 1px solid #ddd;
      padding: 10px 10px;
      .label {
        float: left;
        width: 85px;
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      .set-col {
        color: #3e9942;
      }
      .field-value {
        margin:0 0 5px 85px;
        font-size: 14px;
        color: #666;
      }
      .set-pointer {
        cursor: pointer;
      }
      .view-icon {
        vertical-align: top;
        width: 22px;
        margin-left: 5px;
      }
      .pat-name {
        color: #1890ff;
        text-decoration: underline;
      }
      .set-mar {
        margin-left: 0
      }
    }
    .list-item:nth-child(even) {
      background: #eaf7ff;
    }
  }
  .add-group-btn {
    margin-left: 10px;
  }
}
</style>
