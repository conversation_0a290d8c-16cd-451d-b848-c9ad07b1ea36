<template>
  <div class="searchpage-event app-container" v-loading="showLoading" element-loading-text="正在加载中...">
    <div class="fl-search-box" >
      <div class="search-box">
        <el-tabs v-model="searchType" class="tab">
          <el-tab-pane label="事件查询" name="2"></el-tab-pane>
        </el-tabs>
        <!-- 条件筛选 -->
        <div class="search-field set-pad">
          <conditionEditor ref="conditionEditor" @showEditor="showEditor" @search="search"></conditionEditor>
          <div class="btns">
            <div class="fl">
              <el-button icon="el-icon-plus" size="mini" type="primary"  plain @click="showEditor(3)">时间事件</el-button>
              <el-button icon="el-icon-plus" size="mini" type="primary"  plain @click="showEditor(4)">干预事件</el-button>
            </div>
            <div class="fr">
              <el-button
              :disabled="showLoading"
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="search"
              >搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetForm">重置</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="drag-icon"></div>
    </div>
    <div class="fr-content-box">
      <div class="head-box">
        <div class="total">筛选结果 <span>共{{ extra2 && total == extra2 ? total+'+' : total }}名患者</span></div>
        <div class="fr-btn-group">
          <el-button type="primary" plain icon="el-icon-plus" size="small" :disabled="total == 0" @click="addProject">添加到初筛队列</el-button>
          <el-button class="btn" type="text" size="medium" icon="el-icon-search" @click="toPage('/biodata')">关键词检索</el-button>
          <el-button class="btn" type="text" size="medium" icon="el-icon-user-solid" @click="toPage('/precise')">患者编号检索</el-button>
        </div>
      </div>
      <el-table
        :data="caseList"
        border
        stripe
        @expand-change="load">
        <el-table-column
          prop="pat_name"
          label="患者姓名"
          width="150"
          :key="1"
        ></el-table-column>
        <el-table-column prop="age" label="患者年龄" :key="2"></el-table-column>
        <el-table-column prop="gender" label="患者性别" :key="3">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sd_dict_sex"
              :value="scope.row.gender"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="phone"
          label="手机号"
          :key="4"
        ></el-table-column>
        <el-table-column
          v-for="(value, key, index) in columnObj"
          :prop="value.fieldName"
          :label="value.fieldComment"
          :show-overflow-tooltip="true"
          :key="key">
          <template slot-scope="scope">
            <el-link class="text-overhide" v-if="scope.row[value.tableName+'__'+value.fieldName].split('*').length > 1" type="primary" :underline="false" @click="detailList(value, scope.row)">{{scope.row[value.tableName+'__'+value.fieldName]}}</el-link>
            <span v-else >{{scope.row[value.tableName+'__'+value.fieldName]}}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="130"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="view(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="searchConditionForm.pageNum"
        :limit.sync="searchConditionForm.pageSize"
        @pagination="search('page')"
      />
    </div>
    <!-- 条件查询选择字段弹窗 -->
    <selectField
      ref="selectField"
      @getList="getFieldList"
      :selectFieldType="selectFieldType"
    ></selectField>

      <!-- 点击td上的数字显示历史记录弹窗 -->
    <el-dialog
      title="记录详情"
      :visible.sync="showRecordList"
      width="700px"
      @close="showRecordList = false"
      top="8vh">
      <el-table
        :data="recordList"
        border
        stripe
        height="60vh"
        v-loading="showHistoryLoading">
        <el-table-column
          v-for="col in recordColumns"
          :prop="col.fieldName"
          :key="col.fieldName"
          :label="col.aliseName">
          <template slot-scope="scope">
            <div>{{ scope.row[col.fieldName] }}</div>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer">
        <el-button type="primary" @click="showRecordList = false">关闭</el-button>
      </span>
    </el-dialog>
    <!-- 保存初筛队列弹窗 -->
    <saveInitialQueueAlert ref="saveInitialQueueAlert" @saveProject="saveProject"></saveInitialQueueAlert>
  </div>
</template>
<script>
import { eventRetrievalByCondition, caseSearchConditionByCondition, getEventDetails} from "@/api/disease/caseManage";
import { getCasegroup } from "@/api/scientific/dataCenter";
import { formatDate } from "@/utils";
import { downloadFile } from "@/utils/request";
import selectField from "../components/selectField";
import conditionEditor from "./conditionEditorEvent";
import saveInitialQueueAlert from './saveInitialQueueAlert'
import { selectMddElementList } from "@/api/system/mddmodel";
import { getTableFindElementList } from "@/api/system/mddelement";
import {mapGetters} from 'vuex'
import $ from 'jquery'
export default {
  dicts: ["sd_dict_sex"],
  name: 'AdvancedSearchEvent',
  data() {
    return {
      formatDate,
      showLoading: false, //加载中弹窗
      showSavaProjectAlert: false, //保存项目弹窗
      searchType: "2", //搜索类型: 1快速查询 2条件筛选 3.ID精确检索
      showSearch: false,
      checkedIds: [], //列表选中的id
      searchConditionForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      caseList: [], //条件筛选列表
      projectForm: {
        rsId: "",
        rgId: "",
        projectName: "", //项目名称
        projectInfo: "", //项目描述
        projectCode: "", //项目编号
        conditionSearchObject: "", //条件查询条件
        resultSetMode: '1', //保存类型 0: 查询条件 1: 查询结果
      },
      topicList: [], //课题列表
      experimentalGroupList: [], //实验组列表
      ageList: [],
      conditionSearchRowList: [], //条件筛选勾选的条件字段
      conditionSearchBaselineDate: {}, // 条件筛选勾选的基线字段
      conditionText: "", //条件查询自然文本
      selectFieldType: 1, //1选择结构化字段 2选择基线字段 3:事件检索-时间事件字段 4: 事件检索-干预事件
      cgId: null, //项目ID,
      projectDetail: null,
      isFirstLoading: true, //是否第一次加载
      extra2: '',
      searchWay: '', //1同就诊 2同患者
      eventType: '', //事件类型 1: 单事件 2:比较事件
      columnObj: {}, //表格的列 除患者基本信息4个字段以及重复的字段 所有的查询条件
      showRecordList: false, //查看多条记录详情弹窗
      recordList: [], //记录列表
      recordColumns: [], //记录表头
      showHistoryLoading: false,
    };
  },
  components: {
    selectField,
    conditionEditor,
    saveInitialQueueAlert
  },
  computed: {
    ...mapGetters(['currentSelectedDisease']),
  },

  mounted() {
    let cgId = this.$route.query.cgId;
    if (cgId) {
      this.cgId = cgId;
      this.getProjectDetail(cgId);
    }else {
      this.isFirstLoading = false;
    }
    this.drag();
  },

  activated() {
    let cgId = this.$route.query.cgId;
    if (cgId && cgId != this.cgId && !this.isFirstLoading) {
      this.cgId = cgId;
      this.getProjectDetail(cgId);
    }
    this.drag();
  },
  methods: {
    //获取项目详情
    getProjectDetail(pId) {
      getCasegroup(pId).then((res) => {
        this.projectDetail = res.data;
        this.projectForm.rsId = res.data.rsId;
        //增加延时赋值 因为rsID赋值会触发监听事件,会将rgID进行清空处理
        setTimeout((_) => {
          this.projectForm.rgId = res.data.rgId;
        }, 2000);
        this.projectForm.projectName = res.data.projectName; //项目名称
        this.projectForm.projectInfo = res.data.projectInfo; //项目描述
        this.projectForm.projectCode = res.data.projectCode; //项目编号
        this.projectForm.resultSetMode = res.data.resultSetMode;
        this.searchType = res.data.searchType;
        this.isFirstLoading = false;
        let conditionSearchJson = JSON.parse(res.data.conditionSearchJson);
        this.$nextTick((_) => {
          this.$refs.conditionEditor.conditionSearchRanderData(conditionSearchJson.conditionSearchGroupList, conditionSearchJson.searchWay);
        });
      });
    },
    view(row) {
      let empi = row.empi;
      this.$jumpPatient360({ path: "/cdrView", query: { empi: empi } }, row);
    },
    //查询
    search(type) {
      let data = this.$refs.conditionEditor.getParams();
      if(data.bool) {
        if(type != 'page') {
          this.searchConditionForm.pageNum = 1;

          //将查询条件重复字段去掉, 用于表格列显示
          let list = JSON.parse(JSON.stringify(data.params));
          console.log(list);
          let obj = {};
          //患者信息固定显示的四个字段排除
          let exclusionField = ['pat_name', 'age', 'gender', 'phone'];
          list.forEach(item => {
            item.conditionSearchRowList.forEach(fieldItem => {
              if(exclusionField.indexOf(fieldItem.fieldName) === -1 && !obj[fieldItem.fieldName]) {
                obj[fieldItem.fieldName] = fieldItem
              }
            })
          })
          this.columnObj = obj
        }
        this.conditionSearchRowList = data.params;
        this.searchWay = data.searchWay;
        this.conditionSearch();
      }
    },
    //重置
    resetForm() {
      this.$refs.conditionEditor.reset();
      this.caseList = [];
      this.total = 0;
      this.projectForm = {
        rsId: "",
        rgId: "",
        projectName: "", //项目名称
        projectInfo: "", //项目描述
        projectCode: "", //项目编号
        conditionSearchObject: "", //条件查询条件
        resultSetMode: "1", //保存类型 0: 查询条件 1: 查询结果
      }
      this.projectDetail = null;
    },
    //获取选择的字段
    getFieldList(fieldList) {
      let data = JSON.parse(JSON.stringify(fieldList));
      this.$refs.conditionEditor.getFieldList(data, this.selectFieldType, this.eventType);
    },
    //条件查询
    conditionSearch() {
      if (this.conditionSearchRowList.length == 0) {
        this.$message.error("请选择字段后在进行查询");
        return false;
      }
      this.showLoading = true;
      let params = {
        pageNum: this.searchConditionForm.pageNum,
        pageSize: this.searchConditionForm.pageSize,
        conditionSearchGroupList: this.conditionSearchRowList,
        diseaseSyscode: this.currentSelectedDisease.diseaseSyscode,
        searchWay: this.searchWay
      };
      eventRetrievalByCondition(params).then((res) => {
        this.total = res.total || 0;
        if (res.rows && res.rows.length > 0) {
          res.rows.forEach((item) => {
            item.detailData = null;
          });
        }
        this.caseList = res.rows || [];
        this.extra = res.extra || '';
        this.extra2 = res.extra2 || '';
        this.extra3 = res.extra3 || '';
        this.showLoading = false;
      }).catch(err => {
        this.showLoading = false;
      });
    },
    //条件查询表格 点击行展开时获取详细信息
    load(row, b) {
      if (row.detailData) return false;
      let params = {
        empi: row.empi,
        conditionSearchGroupList: this.conditionSearchRowList,
      };
      caseSearchConditionByCondition(params).then((res) => {
        row.detailData = res.rows;
      });
    },

    addProject() {
      this.$refs.saveInitialQueueAlert.initData();
    },

    //保存项目
    saveProject(data, rgId) {
      let obj = {
        // rsId: this.projectForm.rsId,
        // subjectName: subjectName,
        // rgId: this.projectForm.rgId,
        // groupName: groupName,
        ...data,
        projectName: this.projectForm.projectName,
        projectInfo: this.projectForm.projectInfo,
        searchType: this.searchType,
        projectCode: this.projectForm.projectCode,
        diseaseSyscode: this.currentSelectedDisease.diseaseSyscode,
        resultSetMode: this.projectForm.resultSetMode,
        keyMD5Data: this.extra,
        showLimit: this.extra2,
        keyMD5Where: this.extra3
      };
      obj.patientTotal = this.total,
        obj.conditionSearchObject = {
          conditionSearchGroupList: this.conditionSearchRowList,
          searchWay: this.searchWay
        };

      let confirmTipText = data.isAutoGroup == 1 ? `确定将查询的病例保存到${data.subjectName}中吗? 系统将随机把病例入组到课题下的队列中。` : `确定将查询的病例保存到“${data.groupName}”中吗? `


      // `确定将查询的${this.total}例病例保存到“${groupName}”中吗?
      this.$confirm(`${confirmTipText} 系统会自动过滤已添加的病例!`,"提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(_ => {
        // this.showSavaProjectAlert = false;
        this.$refs.saveInitialQueueAlert.close();
        let loading = this.$loading({
          lock: true,
          text: '正在保存...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        obj.varsTotal = 3;
        addProject(obj).then((res) => {
          loading.close();
          // `计划入组病例数${this.total}，实际入组病例数${res.data}`
          this.$confirm(`保存成功, 在队列记录中查看添加状态!`, '提示', {
            confirmButtonText: "确定",
            showCancelButton: false,
            showClose: false
          }).then(_ => {
            this.$router.push({ path: "/casegroup/initial", query: {cgId: data.isAutoGroup == 1 ? rgId : data.rgId}});
          })
        }).catch(_ => {
          loading.close();
        });
      }).catch(_ => {})
      // }
      // });
    },

    //拖拽改变条件编辑器宽度
    drag() {
      let _this = this;
      $('.drag-icon').mousedown(function(e) {
        $(this).css({
          'background':'#6299eb'
        })
        let startX = e.clientX;
        let leftMarWidth = $('.sidebar-container').width() + 20;
        document.onmousemove = function (e) {
            let flWidth = e.clientX - leftMarWidth;
            if(flWidth > 570 && flWidth < 1130) {
                $('.fl-search-box').css({
                    'width': flWidth+'px'
                })
                $('.fr-content-box').css({
                    'margin-left': flWidth+30+'px'
                })
                _this.$refs.conditionEditor.changeUseBaselineDate();
            }
        };
        // 鼠标松开事件
        document.onmouseup = function (evt) {
          document.onmousemove = null;
          document.onmouseup = null;
          $('.drag-icon').css('background', '');
        };
      })
    },
    //显示条件查询编辑器 type: 1选择结构化字段 2基线字段 3选择时间事件 4选择干预事件 5选择干预事件的第二个比较事件
    showEditor(type) {
      this.selectFieldType = type;
      this.$nextTick(_ => {
        this.$refs.selectField.init();
      })
    },
    //切换查询页面
    toPage(path) {
      this.$router.push({path})
    },
    //查看数据的详情
    detailList(item, row) {
      this.showRecordList = true;
      this.showHistoryLoading = true;
      let idsKey = item.tableName + '__'+ item.fieldName + '__ids';
      let ids = row[idsKey];
      let params = {
        tableName: item.tableName,
        fieldName: item.fieldName,
        ids: ids,
        baselineFieldName: item.baselineFieldName || '',
        dictCode: item.dictCode || ''
      }
      this.recordColumns = [
        {fieldName: item.fieldName, aliseName: item.fieldComment},
        {fieldName: item.baselineFieldName, aliseName: item.baselineFieldAliseName}
      ]
      getEventDetails(params)
      .then(res => {
        this.recordList = res.rows;
        this.showHistoryLoading = false;
      })
    },
  }
};
</script>
<style lang="scss">
.searchpage-event {
  // height: 100%;
  padding-top: 20px;
  overflow: hidden;
  .head-box {
    padding: 0 0 15px 0;
    overflow: hidden;
    .total {
      float: left;
      padding: 10px 0;
      font-size: 16px;
      font-weight: bold;
      span {
        font-size: 15px;
        color: #666;
        padding: 0 0 0 10px;
        font-weight: normal;
      }
    }
    .fr-btn-group {
      text-align: right;
    }
  }
  .fl-search-box {
    float: left;
    width: 570px;
    border: 1px solid #ddd;
    min-height: calc(100vh - 140px);
    position: relative;
    .search-box {
      overflow: hidden;
    }
    .tab {
      padding: 0 10px;
    }
    .search-field {
      padding: 0 10px 0;
      overflow: hidden;
      .btns {
        border-top: 1px solid #eee;
        text-align: center;
        padding: 15px 10px 0 0;
        margin-top: 10px;
        overflow: hidden;
      }
    }
    .set-pad {
      padding: 0 0px 0 10px;
    }
    .arrow-icon {
      position: absolute;
      top: 49%;
      right: -23px;
      font-size: 23px;
      width: 22px;
      height: 52px;
      background: #0b5ca7;
      line-height: 52px;
      text-align: center;
      color: #fff;
      cursor: pointer;
    }
    .drag-icon {
      position: absolute;
      top: 47%;
      right: -10px;
      font-size: 23px;
      width: 11px;
      height: 52px;
      line-height: 52px;
      text-align: center;
      cursor: ew-resize;
      overflow: hidden;
      background: #ccc;
      background-image: url('~@/assets/images/disease/drag-icon1.png') !important;
      background-position: -6px 10px !important;
      background-repeat: no-repeat !important;
      .icon {
        position: relative;
        left: -6px;
        vertical-align: top;
        margin-top: 10px;
      }
    }
    .drag-icon:hover {
      background: #1890ff ;
    }
  }
  .fr-content-box {
    margin-left: 600px;
    .detail-list {
      padding: 0 20px 5px;
    }
  }
  .add-group-btn {
    margin-left: 10px;
  }
  .text-overhide {
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding: 0 5px;
  }
}
</style>
