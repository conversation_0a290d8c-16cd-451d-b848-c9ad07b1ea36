<template>
  <div class="searchpage2 app-container">
    <div class="fl-search-box">
      <div class="search-box">
        <!-- <el-tabs v-model="searchType" class="tab">
          <el-tab-pane label="患者编号检索" name="3"></el-tab-pane>
        </el-tabs> -->
        <!-- ID精确检索 -->
        <div class="search-field">
          <div class="select-box">
            <span>选择编号字段: </span>
            <el-cascader
              class="set-width"
              :props="cascaderProps"
              v-model="searchByIdForm.fieldId"
              size="small"
              @change="handelTableCol"
              ref="cascaderRef"
            ></el-cascader>
          </div>
          <el-input
            class="textarea"
            type="textarea"
            v-model="searchByIdForm.userIds"
            resize="none"
            :placeholder="`每行输入一个编号, 按回车进行换行,例如:\n1111\n2222\n3333`"
            size="normal"
          ></el-input>
          <div class="btns">
            <el-button icon="el-icon-refresh" size="mini" @click="resetForm"
              >重置</el-button
            >
            <el-button
              :disabled="showLoading"
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="search"
              >搜索</el-button
            >
          </div>
        </div>
      </div>
      <div class="drag-icon"></div>
    </div>
    <div class="fr-content-box" >
      <div class="head-box">
        <div class="total">筛选结果 <span>共{{ total }}名患者</span></div>
        <div class="fr-btn-group">
          <el-button type="primary" plain icon="el-icon-plus" size="small" :disabled="total == 0" @click="addProject">添加到初筛队列</el-button>
          <el-button class="btn" type="text" size="medium" icon="el-icon-search" @click="toPage('/biodata')">关键词检索</el-button>
          <el-button class="btn" type="text" size="medium" icon="el-icon-user-solid" @click="toPage('/advanced')">高级查询</el-button>
        </div>
      </div>
      <el-table
        :data="caseList"
        border
        stripe>
        <el-table-column
          prop="encountId"
          label="就诊号"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-link type="primary" @click="view(scope.row, 'encount_id')">{{
                scope.row.encountId
              }}</el-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="patName"
          label="患者姓名"
          align="center"
          show-overflow-tooltip
        >
        <template slot-scope="scope">
          <el-link type="primary" @click="view(scope.row, 'empi')">{{
              scope.row.patName
            }}</el-link>
        </template>
        </el-table-column>
        <el-table-column prop="gender" label="患者性别" width="80" align="center">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sd_dict_sex"
              :value="scope.row.gender"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="birthday"
          label="出生日期"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="phone"
          label="手机号"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="cureDate"
          label="就诊日期"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column v-for="col in columnList" :prop="col.value" :label="col.label" :key="col.value"></el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="searchByIdForm.pageNum"
        :limit.sync="searchByIdForm.pageSize"
        @pagination="search"
      />
    </div>
    <!-- <el-dialog
      title="添加到初筛队列"
      :visible.sync="showSavaProjectAlert"
      width="440px"
      :close-on-click-modal="false"
      @close="showSavaProjectAlert = false">
      <el-form
        :model="projectForm"
        ref="projectForm"
        label-width="90px"
        :inline="false"
        size="normal">
        <el-form-item
          label="课题"
          prop="rsId"
          :rules="[
            { required: true, message: '请选择课题', trigger: 'change' },
          ]">
          <el-select
            v-model="projectForm.rsId"
            placeholder="请选择课题"
            filterable
          >
            <el-option
              v-for="item in topicList"
              :key="item.rsId"
              :label="item.subjectName"
              :value="item.rsId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="projectForm.rsId"
          prop="rgId"
          label="初筛队列"
          :rules="[
            { required: true, message: '请选择初筛队列', trigger: 'change' },
          ]"
        >
          <el-select
            v-model="projectForm.rgId"
            placeholder="请选择初筛队列"
            filterable>
            <el-option
              v-for="item in experimentalGroupList"
              :key="item.rgId"
              :label="item.groupName"
              :value="item.rgId">
            </el-option>
          </el-select>
          <el-button class="add-group-btn" type="text" size="mini" icon="el-icon-plus" @click="showAddAlert">创建队列</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="showSavaProjectAlert = false">取消</el-button>
        <el-button type="primary" @click="saveProject">保存</el-button>
      </span>
    </el-dialog> -->
    <!-- 添加队列弹窗 -->
    <!-- <addStudyCohortAlert ref="addStudyCohortAlert" :topicList="topicList" @getGrouplist="getGrouplist"></addStudyCohortAlert> -->
     <!-- 保存初筛队列弹窗 -->
    <saveInitialQueueAlert ref="saveInitialQueueAlert" @saveProject="saveProject"></saveInitialQueueAlert>
  </div>
</template>
<script>
import {
  getTopicList,
  getGrouplist,
  addProject,
  searchById,
  customExport,
  updateProject,
} from "@/api/disease/caseManage";
import { getCasegroup } from "@/api/disease/casegroup";
import { downloadFile } from "@/utils/request";
import { selectMddElementList } from "@/api/system/mddmodel";
import { getTableFindElementList } from "@/api/system/mddelement";
import {mapGetters} from 'vuex'
import $ from 'jquery'
import addStudyCohortAlert from './addStudyCohortAlert'
import saveInitialQueueAlert from './saveInitialQueueAlert'
export default {
  dicts: ["sd_dict_sex"],
  name: 'Precise',
  data() {
    return {
      showLoading: false, //加载中弹窗
      showSavaProjectAlert: false, //保存项目弹窗
      searchType: "3", //搜索类型: 1快速查询 2条件筛选 3.ID精确检索
      searchByIdForm: {
        userIds: "", //ID精确检索字段,
        fieldId: "",
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      caseList: [], //id精确查找列表数据
      projectForm: {
        rsId: "",
        rgId: "",
        projectName: "", //项目名称
        projectInfo: "", //项目描述
        projectCode: "", //项目编号
        sdPatientInfo: "", //id精确检索条件
        resultSetMode: '1', //保存类型 0: 查询条件 1: 查询结果
      },
      topicList: [], //课题列表
      experimentalGroupList: [], //实验组列表
      customFormList: [], //自定义表单列表
      cgId: null, //项目ID,
      projectDetail: null,
      columnList: [],
    };
  },
  components: {
    addStudyCohortAlert,
    saveInitialQueueAlert
  },
  computed: {
    ...mapGetters(['currentSelectedDisease']),
    cascaderProps() {
      return {
        lazy: true,
        value: "value",
        label: "label",
        lazyLoad: this.lazyLoad,
      };
    }
  },
  watch: {
    // "projectForm.rsId": function (newVal, oldVal) {
    //   this.projectForm.rgId = "";
    //   this.experimentalGroupList = [];
    //   if (newVal) {
    //     this.getGrouplist();
    //   }
    // },
    // currentSelectedDisease: {
    //     handler(newVal, oldVal) {
    //       this.getTopicList();
    //     },
    //     deep: true,
    //     immediate: true
    // },
    	// 监听路由是否变化
    '$route' (to, from) {
      //当详情页进入此页面后, 在点击左侧菜单 则会进入if判断
      if(!to.query.cgId && this.cgId){
          this.cgId = ''; // 把最新id赋值给定义在data中的id
          this.resetForm();
          this.drag(); // 重新调用加载数据方法
      }
    }
  },
  activated() {
    let cgId = this.$route.query.cgId;
    if (cgId) {
      this.cgId = cgId;
      this.getProjectDetail(cgId);
    }
    this.drag();
  },
  methods: {
    //获取项目详情
    getProjectDetail(pId) {
      getCasegroup(pId).then((res) => {
        this.projectDetail = res.data;
        this.projectForm.rsId = res.data.rsId;
        //增加延时赋值 因为rsID赋值会触发监听事件,会将rgID进行清空处理
        setTimeout((_) => {
          this.projectForm.rgId = res.data.rgId;
        }, 2000);
        this.projectForm.projectName = res.data.projectName; //项目名称
        this.projectForm.projectInfo = res.data.projectInfo; //项目描述
        this.projectForm.projectCode = res.data.projectCode; //项目编号
        this.projectForm.resultSetMode = res.data.resultSetMode;
        this.searchType = res.data.searchType;
        let conditionSearchJson = JSON.parse(res.data.conditionSearchJson);
        this.searchByIdForm.userIds = conditionSearchJson.ids.join("\n");
        this.searchByIdForm.fieldId = [
          conditionSearchJson.tableName,
          conditionSearchJson.fieldName,
        ];
        this.search()
      });
    },
    view(row, type) {
      let query = {};
      if (this.searchType == 1) {
        query.empi = row.searchHit.sourceAsMap.EMPI;
      } else {
        query.empi = row.EMPI;
      }

      if (type == 'encount_id') {
        query.encountId = row.encountId
      }

      this.$jumpPatient360({ path: "/cdrView", query }, this.searchType == 1 ? row.searchHit.sourceAsMap : row);
    },
    //查询
    search() {
      this.searchById();
    },
   
    //加载ID精确检索选择id字段数据
    lazyLoad(node, resolve) {
      const { level } = node;
      if (level == 0) {
        selectMddElementList({ modelType: 1 }).then((response) => {
          let list = response.data;
          for(let i = list.length - 1; i >= 0; i--) {
            if(list[i].searchIdFlag != 1) {
              list.splice(i, 1);
            }else {
              list[i].label = list[i].aliasName;
              list[i].value = list[i].tableName;
            }
          }
          this.tableList = list
          resolve(list);
        });
      } else if (level == 1) {
        getTableFindElementList({
          tableName: node.value,
          searchIdFlag: 1,
        }).then((res) => {
          let fieldList = [];
          res.data.forEach((item, fIndex) => {
            fieldList.push({
              label: item.aliasName,
              value: item.elementName,
              leaf: true,
            });
          });
          resolve(fieldList);
        });
      }
    },
    //id精确查询
    searchById() {
      if (this.searchByIdForm.fieldId.length == 0) {
        this.$message.error("请选择编号字段");
        return false;
      } else if (this.searchByIdForm.fieldId.length == 1) {
        this.$message.error("您选择的编号字段无效, 请重新选择");
        return false;
      }
      let ids = this.searchByIdForm.userIds;
      ids = ids.replaceAll("\n", ",");
      if (!ids) {
        this.$message.error("请输入编号后在进行查询");
        return false;
      }
      ids = ids.split(",");
      this.showLoading = true;
      this.$modal.loading();
      let params = {
        ids: ids,
        tableName: this.searchByIdForm.fieldId[0],
        fieldName: this.searchByIdForm.fieldId[1],
        // diseaseSyscode: this.currentSelectedDisease.diseaseSyscode,
        pageNum: this.searchByIdForm.pageNum,
        pageSize: this.searchByIdForm.pageSize
      };
      searchById(params).then((res) => {
        this.total = res.total || 0;
        this.caseList = res.rows || [];
        this.showLoading = false;
        this.$modal.closeLoading();
        this.extra = res.extra || '';
      }).catch(err => {
        this.showLoading = false;
        this.$modal.closeLoading();
      });
    },
    // 动态展示检索字段
    handelTableCol(e) {
      let node = this.$refs.cascaderRef.getCheckedNodes()[0]
      let field = this.toCamelCase(node.value)
      if (field == 'empi') {
        field = 'EMPI'
      }
      this.columnList = [
        {
          label: node.label,
          value: field
        }
      ]
    },
    // 下划线转驼峰
    toCamelCase(str) {
      return str.replace(/_([a-z])/g, function (match, letter) {
        return letter.toUpperCase();
      });
    },
    //重置
    resetForm() {
      this.searchByIdForm.userIds = "";
      this.caseList = [];
      this.total = 0;
      this.projectForm = {
        rsId: "",
        rgId: "",
        projectName: "", //项目名称
        projectInfo: "", //项目描述
        projectCode: "", //项目编号
        sdPatientInfo: "", //id精确检索条件
        resultSetMode: "1", //保存类型 0: 查询条件 1: 查询结果
      }
      this.projectDetail = null;
    },
    //新增保存项目
    addProject() {
      // if (this.cgId && this.currentSelectedDisease.diseaseSyscode != this.projectDetail.diseaseSyscode) {
      //   this.$confirm(`当前病种已切换为‘${this.currentSelectedDisease.diseaseAlias}’，保存后此初筛队列所属病种将变更为‘${this.currentSelectedDisease.diseaseAlias}’，是否继续操作？`, '提示', {
      //    confirmButtonText: '是',
      //     cancelButtonText: '否',
      //     type: 'warning',
      //   }).then(res => {
      //     this.showSavaProjectAlert = true;
      //     this.$nextTick((_) => {
      //       this.$refs.projectForm.resetFields();
      //       this.projectForm.rsId = '';
      //       this.projectForm.rgId = '';
      //     });
      //   }).catch(_ => {})

      // }else {
      //   this.showSavaProjectAlert = true;
      //   this.$nextTick((_) => {
      //     if(!this.cgId) {
      //       this.$refs.projectForm.clearValidate();
      //     }
      //     if(this.projectDetail && this.projectDetail.rsId) {
      //       this.projectForm.rsId = this.projectDetail.rsId;
      //       //增加延时赋值 因为rsID赋值会触发监听事件,会将rgID进行清空处理
      //       setTimeout((_) => {
      //         this.projectForm.rgId = this.projectDetail.rgId;
      //       }, 1000);
      //     }
      //   });
      // }
      // this.getTopicList();
      // this.showSavaProjectAlert = true;
      // this.$nextTick(_ => {
      //   this.$refs.projectForm.resetFields();
      // })
      if (this.total > 1000) {
        this.$message.error('一次添加量不能超过1000，可分批次添加')
      } else {
        this.$refs.saveInitialQueueAlert.initData();
      }
    },

    //获取课题列表
    getTopicList() {
      getTopicList({diseaseSyscode: this.currentSelectedDisease.diseaseSyscode}).then((res) => {
        this.topicList = res.data || [];
      });
    },

    //获取实验组列表
    getGrouplist() {
      getGrouplist({ rs_id: this.projectForm.rsId }).then((res) => {
        this.experimentalGroupList = res.data || [];
      });
    },

    //保存项目
    saveProject(data, rgId) {
      // this.$refs.projectForm.validate((valid) => {
      //   if (valid) {
          // let subjectName = ""; //课题名称
          // this.topicList.forEach((item) => {
          //   if (item.rsId == this.projectForm.rsId) {
          //     subjectName = item.subjectName;
          //   }
          // });
          // let groupName = ""; //组名称
          // this.experimentalGroupList.forEach((item) => {
          //   if (item.rgId == this.projectForm.rgId) {
          //     groupName = item.groupName;
          //   }
          // });
          let obj = {
            // rsId: this.projectForm.rsId,
            // subjectName: subjectName,
            // rgId: this.projectForm.rgId,
            // groupName: groupName,
            ...data,
            projectName: this.projectForm.projectName,
            projectInfo: this.projectForm.projectInfo,
            searchType: this.searchType,
            projectCode: this.projectForm.projectCode,
            diseaseSyscode: this.currentSelectedDisease.diseaseSyscode,
            resultSetMode: this.projectForm.resultSetMode,
            keyMD5Data: this.extra
          };
          let ids = this.searchByIdForm.userIds;
          ids = ids.replaceAll("\n", ",");
          ids = ids.split(",");
          obj.patientTotal = this.total,
          obj.sdPatientInfo = {
            ids: ids,
            tableName: this.searchByIdForm.fieldId[0],
            fieldName: this.searchByIdForm.fieldId[1],
          };
          let confirmTipText = data.isAutoGroup == 1 ? `确定将查询的病例保存到${data.subjectName}中吗? 系统将随机把病例入组到课题下的队列中。` : `确定将查询的病例保存到“${data.groupName}”中吗? `
          this.$confirm(`${confirmTipText} 系统会自动过滤已添加的病例!`,"提示",{
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(_ => {
            // this.showSavaProjectAlert = false;
            this.$refs.saveInitialQueueAlert.close();
            const loading = this.$loading({
              lock: true,
              text: '正在保存...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            obj.varsTotal = 3;
            addProject(obj).then((res) => {
              loading.close();
              this.$confirm(`保存成功, 在队列记录中查看添加状态!`, '提示', {
                confirmButtonText: "确定",
                showCancelButton: false,
                showClose: false
              }).then(_ => {
                this.$router.push({ path: "/casegroup/initial", query: {cgId: data.isAutoGroup == 1 ? rgId : data.rgId}});
              })
            }).catch(_ => {
               loading.close();
            });
          }).catch(_ => {})
      //   }
      // });
    },

    //拖拽改变条件编辑器宽度
    drag() {
      let _this = this;
      $('.drag-icon').mousedown(function(e) {
        $(this).css({
          'background':'#6299eb'
        })
        let startX = e.clientX;
        let leftMarWidth = $('.sidebar-container').width() + 20;
        document.onmousemove = function (e) {
              let flWidth = e.clientX - leftMarWidth;
              if(flWidth > 570 && flWidth < 1130) {
                $('.fl-search-box').css({
                  'width': flWidth+'px'
                })
                $('.fr-content-box').css({
                  'margin-left': flWidth+30+'px'
                })
              }
        };
        // 鼠标松开事件
        document.onmouseup = function (evt) {
          document.onmousemove = null;
          document.onmouseup = null;
          $('.drag-icon').css('background', '');
        };
      })
    },
    //切换查询页面
    toPage(path) {
      this.$router.push({path})
    },
    //显示添加队列弹窗
    showAddAlert() {
      this.$refs.addStudyCohortAlert.init(this.projectForm.rsId)
    }
  },
};
</script>

<style lang="scss">
.searchpage2 {
  height: 100%;
  // padding-top: 10px;
  .head-box {
    padding: 0 0 15px 0;
    overflow: hidden;
    .total {
      float: left;
      padding: 10px 0;
      font-size: 16px;
      font-weight: bold;
      span {
        font-size: 15px;
        color: #666;
        padding: 0 0 0 10px;
        font-weight: normal;
      }
    }
    .fr-btn-group {
      text-align: right;
    }
  }
  .fl-search-box {
    float: left;
    width: 570px;
    height: 100%;
    border: 1px solid #ddd;
    min-height: calc(100vh - 140px);
    position: relative;
    // transition: .5s ease all;
    .search-box {
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    .tab {
      padding: 0 10px;
    }
    .search-field {
      padding: 0 10px 0;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      flex: 1;
      .btns {
        border-top: 1px solid #eee;
        text-align: center;
        padding: 12px;
        margin-top: 10px;
      }
      .textarea {
        flex: 1;
        textarea {
          height: 100% !important;
        }
      }
      .select-box {
        margin: 12px 0;
        .set-width {
          width: 300px;
        }
        span {
          font-size: 13px;
          padding-right: 5px;
          color: #555;
        }
      }
    }
    .arrow-icon {
      position: absolute;
      top: 49%;
      right: -23px;
      font-size: 23px;
      width: 22px;
      height: 52px;
      background: #0b5ca7;
      line-height: 52px;
      text-align: center;
      color: #fff;
      cursor: pointer;
    }
    .drag-icon {
      position: absolute;
      top: 47%;
      right: -10px;
      font-size: 23px;
      width: 11px;
      height: 52px;
      line-height: 52px;
      text-align: center;
      cursor: ew-resize;
      overflow: hidden;
      background: #ccc;
      background-image: url('~@/assets/images/disease/drag-icon1.png') !important;
      background-position: -6px 10px !important;
      background-repeat: no-repeat !important;
      .icon {
        position: relative;
        left: -6px;
        vertical-align: top;
        margin-top: 10px;
      }
    }
    .drag-icon:hover {
      background: #1890ff ;
    }
  }
  .fr-content-box {
    margin-left: 600px;
  }
  .add-group-btn {
    margin-left: 10px;
  }
}
</style>