<template>
  <div class="searchpage2 app-container">
    <div class="fl-search-box" >
      <div class="search-box">
        <!-- <el-tabs v-model="searchType" class="tab">
          <el-tab-pane label="高级查询" name="2"></el-tab-pane>
        </el-tabs> -->
        <!-- 条件筛选 -->
        <div class="search-field set-pad">
          <conditionEditor style="flex:1; width: 100%" ref="conditionEditor" @showEditor="showEditor" @search="search"></conditionEditor>
          <div class="btns">
            <div class="fl">
              <el-button icon="el-icon-plus" size="small" type="primary"  plain @click="showEditor(2)">选择基线字段</el-button>
            </div>
            <div class="fr">
              <OfflineSearch />
              <el-button
                :disabled="showLoading"
                type="primary"
                icon="el-icon-search"
                size="small"
                @click="search"
                >搜索</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetForm">重置</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="drag-icon"></div>
    </div>
    <div class="fr-content-box">
      <div class="head-box">
        <div class="total-box">
          筛选结果
          <span class="total-item"><span style="color: #1890FF">{{ patCount }}</span>名患者</span>
          <span class="total-item"><span style="color: #1890FF">{{ extra2 && total == extra2 ? total+'+' : total }}</span>次就诊</span>
        </div>
        <div class="fr-btn-group">
          <!-- <el-button v-if="cgId" type="primary" plain icon="el-icon-plus" size="small" :disabled="total == 0" @click="addProject" >保存修改</el-button> -->
          <el-button type="primary" plain icon="el-icon-collection" size="small" @click="onCollection" :disabled="!searchParams">收藏</el-button>
          <el-button type="primary" plain icon="el-icon-plus" size="small" :disabled="total == 0" @click="addProject">添加到初筛队列</el-button>
          <el-button class="btn" type="text" size="medium" icon="el-icon-search" @click="toPage('/biodata')">关键词检索</el-button>
          <el-button class="btn" type="text" size="medium" icon="el-icon-user-solid" @click="toPage('/precise')">患者编号检索</el-button>
        </div>
      </div>
      <el-table
        :data="caseList"
        border
        stripe
        @expand-change="load">
        <el-table-column
          prop="encountId"
          label="就诊号"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-link type="primary" @click="view(scope.row, 'encount_id')">{{
                scope.row.encountId
              }}</el-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="patName"
          label="患者姓名"
          align="center"
          show-overflow-tooltip
        >
        <template slot-scope="scope">
          <el-link type="primary" @click="view(scope.row, 'empi')">{{
              scope.row.patName
            }}</el-link>
        </template>
        </el-table-column>
        <el-table-column prop="gender" label="患者性别" width="80" align="center">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sd_dict_sex"
              :value="scope.row.gender"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="birthday"
          label="出生日期"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="phone"
          label="手机号"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="cureDate"
          label="就诊日期"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <!-- <el-table-column
          v-for="(value, key, index) in columnObj"
          :prop="value.fieldName"
          :label="value.fieldComment"
          :show-overflow-tooltip="true"
          :key="key">
          <template slot-scope="scope">
            <div v-if="scope.row[value.tableName+'__'+value.fieldName]">
              <el-link class="text-overhide" v-if="scope.row[value.tableName+'__'+value.fieldName].split('*').length > 1" type="primary" :underline="false" @click="detailList(value, scope.row)">{{scope.row[value.tableName+'__'+value.fieldName]}}</el-link>
              <span v-else >{{scope.row[value.tableName+'__'+value.fieldName]}}</span>
            </div>
            <div v-else>
              <span>{{scope.row[value.fieldName]}}</span>
            </div>
          </template>
        </el-table-column> -->
        <!-- <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="130"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="view(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column> -->
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="searchConditionForm.pageNum"
        :limit.sync="searchConditionForm.pageSize"
        @pagination="search('page')"
      />
    </div>
    <!-- <el-dialog
      title="添加到初筛队列"
      :visible.sync="showSavaProjectAlert"
      width="440px"
      :close-on-click-modal="false"
      @close="showSavaProjectAlert = false">
      <el-form
        :model="projectForm"
        ref="projectForm"
        label-width="90px"
        :inline="false"
        size="normal">
        <el-form-item
          label="课题"
          prop="rsId"
          :rules="[
            { required: true, message: '请选择课题', trigger: 'change' },
          ]"
        >
          <el-select
            v-model="projectForm.rsId"
            placeholder="请选择课题"
            filterable
          >
            <el-option
              v-for="item in topicList"
              :key="item.rsId"
              :label="item.subjectName"
              :value="item.rsId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="projectForm.rsId"
          prop="rgId"
          label="初筛队列"
          :rules="[
            { required: true, message: '请选择初筛队列', trigger: 'change' },
          ]"
        >
          <el-select
            v-model="projectForm.rgId"
            placeholder="请选择初筛队列"
            filterable
          >
            <el-option
              v-for="item in experimentalGroupList"
              :key="item.rgId"
              :label="item.groupName"
              :value="item.rgId"
            >
            </el-option>
          </el-select>
          <el-button class="add-group-btn" type="text" size="mini" icon="el-icon-plus" @click="showAddAlert">创建队列</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="showSavaProjectAlert = false">取消</el-button>
        <el-button type="primary" @click="saveProject">保存</el-button>
      </span>
    </el-dialog> -->
    <!-- 条件查询选择字段弹窗 -->
    <selectField
      ref="selectField"
      @getList="getFieldList"
      :selectFieldType="selectFieldType"
    ></selectField>
    <!-- 导出数据选择字段弹窗 -->
    <exportAlert ref="exportAlert" @sureExport="sureExport"></exportAlert>
    <!-- 添加队列弹窗 -->
    <!-- <addStudyCohortAlert ref="addStudyCohortAlert" :topicList="topicList" @getGrouplist="getGrouplist"></addStudyCohortAlert> -->
    <!-- 保存初筛队列弹窗 -->
    <saveInitialQueueAlert ref="saveInitialQueueAlert" :isShowSwitch="true" @saveProject="saveProject"></saveInitialQueueAlert>
    <!-- 点击td上的数字显示历史记录弹窗 -->
    <el-dialog
      title="记录详情"
      :visible.sync="showRecordList"
      width="700px"
      @close="showRecordList = false"
      top="8vh">
      <el-table
        :data="recordList"
        border
        stripe
        height="60vh"
        v-loading="showHistoryLoading">
        <el-table-column
          v-for="(col, index) in recordColumns"
          :prop="col.fieldName"
          :key="index"
          :label="col.aliseName">
          <template slot-scope="scope">
            <div>{{ scope.row[col.fieldName] }}</div>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer">
        <el-button type="primary" @click="showRecordList = false">关闭</el-button>
      </span>
    </el-dialog>
    <!-- 历史查询 -->
    <div class="history-search-box" title="历史查询" @click="onHistorySearch">
      <div class="history-icon"></div>
    </div>
    <SearchCollection ref="searchCollectionRef" :searchParams="searchParams"/>
    <HistorySearch ref="historySearchRef" @search="onHistorySearchC"/>
  </div>
</template>
<script>
import {
  caseSearch,
  getTopicList,
  getGrouplist,
  addProject,
  searchById,
  caseSearchByCondition,
  caseSearchConditionByCondition,
  customExport,
  updateProject,
  getEventDetails,
  autoAddGroup
} from "@/api/disease/caseManage";
import { getCasegroup } from "@/api/disease/casegroup";
import { formatDate } from "@/utils";
import { downloadFile } from "@/utils/request";
import exportAlert from "./exportAlert";
import selectField from "../components/selectField";
import conditionEditor from "./conditionEditor";
import { selectMddElementList } from "@/api/system/mddmodel";
import { getTableFindElementList } from "@/api/system/mddelement";
import {mapGetters} from 'vuex'
import $ from 'jquery'
import addStudyCohortAlert from './addStudyCohortAlert'
import saveInitialQueueAlert from './saveInitialQueueAlert'
import HistorySearch from "./historySearch.vue";
import SearchCollection from "./searchCollection.vue";
import OfflineSearch from './offlineSearch.vue'
export default {
  dicts: ["sd_dict_sex"],
  name: 'Advanced',
  data() {
    return {
      formatDate,
      showLoading: false, //加载中弹窗
      showSavaProjectAlert: false, //保存项目弹窗
      searchType: "2", //搜索类型: 1快速查询 2条件筛选 3.ID精确检索
      showSearch: false,
      checkedIds: [], //列表选中的id
      searchConditionForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      caseList: [], //条件筛选列表
      projectForm: {
        rsId: "",
        rgId: "",
        projectName: "", //项目名称
        projectInfo: "", //项目描述
        projectCode: "", //项目编号
        conditionSearchObject: "", //条件查询条件
        resultSetMode: '1', //保存类型 0: 查询条件 1: 查询结果
      },
      topicList: [], //课题列表
      experimentalGroupList: [], //实验组列表
      ageList: [],
      conditionSearchRowList: [], //条件筛选勾选的条件字段
      conditionSearchBaselineDate: {}, // 条件筛选勾选的基线字段
      conditionText: "", //条件查询自然文本
      selectFieldType: 1, //1选择结构化字段 2选择基线字段
      cgId: null, //项目ID,
      projectDetail: null,
      isFirstLoading: true, //是否第一次加载
      extra2: '',
      searchWay: '', //1同就诊 2同患者
      columnObj: {}, //表格的列 除患者基本信息4个字段以及重复的字段 所有的查询条件
      showRecordList: false, //查看多条记录详情弹窗
      recordList: [], //记录列表
      recordColumns: [], //记录表头
      showHistoryLoading: false,
      searchParams: null,
      historyId: '',
      patCount: 0,
    };
  },
  components: {
    exportAlert,
    selectField,
    conditionEditor,
    addStudyCohortAlert,
    saveInitialQueueAlert,
    HistorySearch,
    SearchCollection,
    OfflineSearch
  },
  computed: {
    ...mapGetters(['currentSelectedDisease']),
  },
  watch: {
    // "projectForm.rsId": function (newVal, oldVal) {
    //   this.projectForm.rgId = "";
    //   this.experimentalGroupList = [];
    //   if (newVal) {
    //     this.getGrouplist();
    //   }
    // },
    // searchType: function (newVal, oldVal) {
    //   this.checkedIds = [];
    // },
    // currentSelectedDisease: {
    //     handler(newVal, oldVal) {
    //         this.getTopicList();
    //     },
    //     deep: true,
    //     immediate: true
    // },
    	// 监听路由是否变化
    // '$route' (to, from) {
    //   //当详情页进入此页面后, 在点击左侧菜单 则会进入if判断
    //   if(!to.query.cgId && this.cgId){
    //       this.cgId = ''; // 把最新id赋值给定义在data中的id
    //       this.resetForm();
    //       this.drag(); // 重新调用加载数据方法
    //   }
    // }
  },
  mounted() {
    let cgId = this.$route.query.cgId;
    if (cgId) {
      this.cgId = cgId;
      this.getProjectDetail(cgId);
    }else {
      this.isFirstLoading = false;
    }
    this.drag();
  },

  activated() {
    let cgId = this.$route.query.cgId;
    if (cgId && cgId != this.cgId && !this.isFirstLoading) {
      this.cgId = cgId;
      this.getProjectDetail(cgId);
    }
    this.drag();
  },
  methods: {
    //获取项目详情
    getProjectDetail(pId) {
      getCasegroup(pId).then((res) => {
        this.projectDetail = res.data;
        this.projectForm.rsId = res.data.rsId;
        //增加延时赋值 因为rsID赋值会触发监听事件,会将rgID进行清空处理
        setTimeout((_) => {
          this.projectForm.rgId = res.data.rgId;
        }, 2000);
        this.projectForm.projectName = res.data.projectName; //项目名称
        this.projectForm.projectInfo = res.data.projectInfo; //项目描述
        this.projectForm.projectCode = res.data.projectCode; //项目编号
        this.projectForm.resultSetMode = res.data.resultSetMode;
        this.searchType = res.data.searchType;
        this.isFirstLoading = false;
        let conditionSearchJson = JSON.parse(res.data.conditionSearchJson);
        this.$nextTick((_) => {
          this.$refs.conditionEditor.conditionSearchRanderData(conditionSearchJson.conditionSearchGroupList, conditionSearchJson.searchWay);
          // this.search()
        });
      });
    },
    view(row, type) {
      let query = {
        empi: row.EMPI || row.empi
      }
      if (type == 'encount_id') {
        query.encountId = row.encountId
      }
      this.$jumpPatient360({ path: "/cdrView", query }, row)
      
    },
    //查询
    search(type, isOfflineSearch, isGetParams, historyId) {
      this.historyId = historyId
      let data = this.$refs.conditionEditor.getParams();
      if(data.bool) {
        if(type != 'page') {
          this.searchConditionForm.pageNum = 1;
          let list = JSON.parse(JSON.stringify(data.params));
          let obj = {};
          //患者信息固定显示的四个字段排除
          let exclusionField = ['patName', 'age', 'gender', 'phone', 'pat_name'];
          list.forEach(item => {
            item.conditionSearchRowList.forEach(fieldItem => {
              if(exclusionField.indexOf(fieldItem.fieldName) === -1 && !obj[fieldItem.fieldName]) {
                obj[fieldItem.fieldName] = fieldItem
              }
            })
          })
          this.columnObj = obj
        }
        this.conditionSearchRowList = data.params;
        this.searchWay = data.searchWay;
        this.conditionSearch(isOfflineSearch, isGetParams);
      }
    },
    //重置
    resetForm() {
      this.$refs.conditionEditor.reset();
      this.caseList = [];
      this.total = 0;
      this.projectForm = {
        rsId: "",
        rgId: "",
        projectName: "", //项目名称
        projectInfo: "", //项目描述
        projectCode: "", //项目编号
        conditionSearchObject: "", //条件查询条件
        resultSetMode: "1", //保存类型 0: 查询条件 1: 查询结果
      }
      this.projectDetail = null;
      this.searchParams = null
    },
    //获取选择的字段
    getFieldList(fieldList) {
      let data = JSON.parse(JSON.stringify(fieldList));
      this.$refs.conditionEditor.getFieldList(data, this.selectFieldType);
    },
    //条件查询
    conditionSearch(isOfflineSearch, isGetParams) {
      if (this.conditionSearchRowList.length == 0) {
        this.$message.error("请选择字段后在进行查询");
        return false;
      }
      let TIME = null
      if (!isOfflineSearch) {
        this.showLoading = true;
        // zrd loading进度
        let loadingText = 1;
        TIME = setInterval(() => {
          if (loadingText == 99) {
            clearInterval(TIME)
            return
          }
          loadingText++
          document.querySelector(".el-loading-spinner .el-loading-text").innerHTML = `数据查询中${loadingText}%`;
        }, 500)
        this.$modal.loading(`数据查询中${loadingText}%`);
      }
      // 
      let params = {
        pageNum: this.searchConditionForm.pageNum,
        pageSize: this.searchConditionForm.pageSize,
        conditionSearchGroupList: this.conditionSearchRowList,
        diseaseSyscode: this.currentSelectedDisease.diseaseSyscode,
        searchWay: this.searchWay,
        historyId: this.historyId
      };
      this.searchParams = params
      if (isGetParams) return
      caseSearchByCondition(params).then((res) => {
        this.total = res.total || 0;
        this.patCount = res.patCount || 0
        if (res.rows && res.rows.length > 0) {
          res.rows.forEach((item) => {
            item.detailData = null;
          });
        }
        this.caseList = res.rows || [];
        this.extra = res.extra || '';
        this.extra2 = res.extra2 || '';
        this.extra3 = res.extra3 || '';
        if (!isOfflineSearch) {
          this.showLoading = false;
          clearInterval(TIME)
          document.querySelector(".el-loading-spinner .el-loading-text").innerHTML = `数据查询中100%`;
          this.$modal.closeLoading();
        }
      }).catch(err => {
        if (!isOfflineSearch) {
          this.showLoading = false;
          clearInterval(TIME)
          this.$modal.closeLoading();
        }
      });
    },

    //查看数据的详情
    detailList(item, row) {
      console.log(item);
      console.log(row);
      this.showRecordList = true;
      this.showHistoryLoading = true;
      let idsKey = item.tableName + '__'+ item.fieldName + '__ids';
      let ids = row[idsKey];
      let params = {
        tableName: item.tableName,
        fieldName: item.fieldName,
        ids: ids,
        baselineFieldName: item.baselineFieldName || '',
        dictCode: item.dictCode || '',
        fieldType: item.fieldType
      }
      this.recordColumns = [
        {fieldName: item.fieldName, aliseName: item.fieldComment},
        {fieldName: item.baselineFieldName, aliseName: item.baselineFieldAliseName}
      ]
      getEventDetails(params)
      .then(res => {
        this.recordList = res.rows;
        this.showHistoryLoading = false;
      })
    },
    //条件查询表格 点击行展开时获取详细信息
    load(row, b) {
      if (row.detailData) return false;
      let params = {
        empi: row.empi,
        conditionSearchGroupList: this.conditionSearchRowList,
      };
      caseSearchConditionByCondition(params).then((res) => {
        row.detailData = res.rows;
      });
    },
    //新增保存项目
    addProject() {
      // if (this.cgId && this.currentSelectedDisease.diseaseSyscode != this.projectDetail.diseaseSyscode) {
      //   this.$confirm(`当前病种已切换为‘${this.currentSelectedDisease.diseaseAlias}’，保存后此初筛队列所属病种将变更为‘${this.currentSelectedDisease.diseaseAlias}’，是否继续操作？`, '提示', {
      //    confirmButtonText: '是',
      //     cancelButtonText: '否',
      //     type: 'warning',
      //   }).then(res => {
      //     this.showSavaProjectAlert = true;
      //     this.$nextTick((_) => {
      //       this.$refs.projectForm.resetFields();
      //       this.projectForm.rsId = '';
      //       this.projectForm.rgId = '';
      //     });
      //   }).catch(_ => {})
      // }else {
      //   this.showSavaProjectAlert = true;
      //   this.$nextTick((_) => {
      //     if(!this.cgId) {
      //       this.$refs.projectForm.clearValidate();
      //     }
      //     if(this.projectDetail && this.projectDetail.rsId) {
      //       this.projectForm.rsId = this.projectDetail.rsId;
      //       //增加延时赋值 因为rsID赋值会触发监听事件,会将rgID进行清空处理
      //       setTimeout((_) => {
      //         this.projectForm.rgId = this.projectDetail.rgId;
      //       }, 1000);
      //     }
      //   });
      // }

      // this.getTopicList();
      // this.showSavaProjectAlert = true;
      // this.$nextTick(_ => {
      //   this.$refs.projectForm.resetFields();
      // })
      this.$refs.saveInitialQueueAlert.initData();
    },
    //获取课题列表
    getTopicList() {
      getTopicList({diseaseSyscode: this.currentSelectedDisease.diseaseSyscode}).then((res) => {
        this.topicList = res.data || [];
      });
    },
    //获取实验组列表
    getGrouplist() {
      getGrouplist({ rs_id: this.projectForm.rsId }).then((res) => {
        this.experimentalGroupList = res.data || [];
      });
    },
    //保存项目
    saveProject(data, rgId, isAutomatically) {
      // this.$refs.projectForm.validate((valid) => {
        // if (valid) {
          // let subjectName = ""; //课题名称
          // this.topicList.forEach((item) => {
          //   if (item.rsId == this.projectForm.rsId) {
          //     subjectName = item.subjectName;
          //   }
          // });
          // let groupName = ""; //组名称
          // this.experimentalGroupList.forEach((item) => {
          //   if (item.rgId == this.projectForm.rgId) {
          //     groupName = item.groupName;
          //   }
          // });
          let obj = {
            // rsId: this.projectForm.rsId,
            // subjectName: subjectName,
            // rgId: this.projectForm.rgId,
            // groupName: groupName,
            ...data,
            projectName: this.projectForm.projectName,
            projectInfo: this.projectForm.projectInfo,
            searchType: this.searchType,
            projectCode: this.projectForm.projectCode,
            diseaseSyscode: this.currentSelectedDisease.diseaseSyscode,
            resultSetMode: this.projectForm.resultSetMode,
            keyMD5Data: this.extra,
            showLimit: this.extra2,
            keyMD5Where: this.extra3
          };
          obj.patientTotal = this.total,
          obj.conditionSearchObject = {
            conditionSearchGroupList: this.conditionSearchRowList,
            searchWay: this.searchWay
          };

          let confirmTipText = data.isAutoGroup == 1 ? `确定将查询的病例保存到${data.subjectName}中吗? 系统将随机把病例入组到课题下的队列中。` : `确定将查询的病例保存到“${data.groupName}”中吗? `

          // `确定将查询的${this.total}例病例保存到“${groupName}”中吗?
          this.$confirm(`${confirmTipText} 系统会自动过滤已添加的病例!`,"提示",{
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(_ => {
            // this.showSavaProjectAlert = false;
            this.$refs.saveInitialQueueAlert.close();
            let loading = this.$loading({
              lock: true,
              text: '正在保存...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            obj.varsTotal = 3;
            addProject(obj).then((res) => {
              loading.close();
              if (isAutomatically) {
                let autoParams = {
                  diseaseSyscode: this.currentSelectedDisease.diseaseSyscode,
                  diseaseName: this.currentSelectedDisease.diseaseName,
                  affiliatedSystem: 'disease',
                  queueType: '01',
                  rgId: data.rgId,
                  advancedSearch: JSON.stringify(this.searchParams),
                  queueCondition: JSON.stringify(obj)
                }
                autoAddGroup(autoParams)
              }
              // `计划入组病例数${this.total}，实际入组病例数${res.data}`
              this.$confirm(`保存成功, 在队列记录中查看添加状态!`, '提示', {
                confirmButtonText: "确定",
                showCancelButton: false,
                showClose: false
              }).then(_ => {
                this.$router.push({ path: "/casegroup/initial", query: {cgId: data.isAutoGroup == 1 ? rgId : data.rgId}});
              })
            }).catch(_ => {
               loading.close();
            });
          }).catch(_ => {})
        // }
      // });
    },
    //拖拽改变条件编辑器宽度
    drag() {
      let _this = this;
      $('.drag-icon').mousedown(function(e) {
        $(this).css({
          'background':'#6299eb'
        })
        let startX = e.clientX;
        let leftMarWidth = $('.sidebar-container').width() + 20;
        document.onmousemove = function (e) {
              let flWidth = e.clientX - leftMarWidth;
              if(flWidth > 570 && flWidth < 1130) {
                $('.fl-search-box').css({
                  'width': flWidth+'px'
                })
                $('.fr-content-box').css({
                  'margin-left': flWidth+30+'px'
                })
                _this.$refs.conditionEditor.changeUseBaselineDate();
              }
        };
        // 鼠标松开事件
        document.onmouseup = function (evt) {
          document.onmousemove = null;
          document.onmouseup = null;
          $('.drag-icon').css('background', '');
        };
      })
    },

    //显示条件查询编辑器 type: 1选择结构化字段 2: 选择基线字段
    showEditor(type) {
      this.selectFieldType = type;
      this.$nextTick(_ => {
        this.$refs.selectField.init();
      })
    },

    //导出
    sureExport(type, obj) {
      let arr = Object.values(obj);
      let params = {
        exportFieldTableList: arr,
        conditionSearchRowList: this.conditionSearchRowList,
      };
      downloadFile("disease/conditionsearch/customExport",{...params}, `表格_${new Date().getTime()}.xlsx`)
    },
    //切换查询页面
    toPage(path) {
      this.$router.push({path})
    },
    //显示添加队列弹窗
    showAddAlert() {
      this.$refs.addStudyCohortAlert.init(this.projectForm.rsId)
    },
    // 查询收藏
    onCollection() {
      this.$refs.searchCollectionRef.show()
    },
    // 历史查询
    onHistorySearch() {
      this.$refs.historySearchRef.show()
    },
    onHistorySearchC(params) {
      // console.log(params,'params');
      this.$refs.conditionEditor.conditionSearchRanderData(params.conditionSearchGroupList, params.searchWay, params.historyId, true)
    }
  }
};
</script>

<style lang="scss">
.searchpage2 {
  height: 100%;
  // padding-top: 20px;
  overflow: hidden;
  .head-box {
    padding: 0 0 15px 0;
    overflow: hidden;
    .total-box {
      float: left;
      padding: 10px 0;
      font-size: 16px;
      font-weight: bold;
      .total-item {
        font-size: 15px;
        color: #666;
        // padding: 0 0 0 10px;
        margin-left: 10px;
        font-weight: normal;
      }
    }
    .fr-btn-group {
      text-align: right;
    }
  }
  .fl-search-box {
    float: left;
    width: 570px;
    border: 1px solid #ddd;
    // min-height: calc(100vh - 140px);
    height: 100%;
    border-radius: 4px;
    position: relative;
    .search-box {
      overflow: hidden;
      height: 100%;
    }
    .tab {
      padding: 0 10px;
    }
    .search-field {
      height: 100%;
      padding: 0 10px 0;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .btns {
        border-top: 1px solid #eee;
        text-align: center;
        padding: 12px;
        margin-top: 10px;
        overflow: hidden;
      }
    }
    .set-pad {
      padding: 0 0px 0 10px;
    }
    .arrow-icon {
      position: absolute;
      top: 49%;
      right: -23px;
      font-size: 23px;
      width: 22px;
      height: 52px;
      background: #0b5ca7;
      line-height: 52px;
      text-align: center;
      color: #fff;
      cursor: pointer;
    }
    .drag-icon {
      position: absolute;
      top: 47%;
      right: -10px;
      font-size: 23px;
      width: 11px;
      height: 52px;
      line-height: 52px;
      text-align: center;
      cursor: ew-resize;
      overflow: hidden;
      background: #ccc;
      background-image: url('~@/assets/images/disease/drag-icon1.png') !important;
      background-position: -6px 10px !important;
      background-repeat: no-repeat !important;
      .icon {
        position: relative;
        left: -6px;
        vertical-align: top;
        margin-top: 10px;
      }
    }
    .drag-icon:hover {
      background: #1890ff ;
    }
  }
  .fr-content-box {
    margin-left: 600px;
    height: 100%;
    overflow: auto;
    .detail-list {
      padding: 0 20px 5px;
    }
  }
  .add-group-btn {
    margin-left: 10px;
  }
  .text-overhide {
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding: 0 5px;
  }
}
.history-search-box {
  position: fixed;
  right: 20px;
  bottom: 36px;
  box-shadow: 0 0 10px rgba(0,0,0,0.2);
  padding: 4px;
  border-radius: 4px;
  background: #fff;
  .history-icon {
    background: url('../../../assets/images/historySearch.svg') no-repeat;
    background-size: 100% 100%;
    width: 32px;
    height: 32px;
    cursor: pointer;
    &:hover{
      background: url('../../../assets/images/historySearchActive.svg') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
