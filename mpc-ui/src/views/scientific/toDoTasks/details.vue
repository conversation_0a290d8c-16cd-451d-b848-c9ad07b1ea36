<template>
  <div>
    <el-drawer class="ms-drawer" title="申请信息" :visible.sync="drawer" size="60%" @opened="onOpen" @closed="onClosed">
      <div class="content" v-if="row">
        <el-collapse class="ms-collapse" v-model="activeNames">
          <el-collapse-item title="基本信息" name="1">
            <div class="base-info">
              <div class="info-item">
                <div class="label">申请人</div>
                <div class="value">{{ row.createBy }}</div>
              </div>
              <div class="info-item">
                <div class="label">申请时间</div>
                <div class="value">{{ row.createTime }}</div>
              </div>
              <div class="info-item">
                <div class="label">任务名称</div>
                <div class="value">{{ row.queueName }}</div>
              </div>
              <div class="info-item">
                <div class="label">任务状态</div>
                <div class="value">
                  <el-tag size="small"
                    :type="row.approvalStatus == '1' ? 'primary' : row.approvalStatus == '2' ? 'success' : 'warning'">{{
      approvalStatusOptions.find(a => a.value == row.approvalStatus).label }}</el-tag>
                </div>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item title="申请内容" name="2" style="flex: 1" class="info-box"
            :class="type == 'view' ? 'is-view' : ''">
            <div class="info">
              <div class="left">
                <div v-for="(item, index) in tabList" :key="index" class="model-item"
                  :class="tabIndex == index ? 'active' : ''" @click="handleClick(item, index)" :title="item.aliasName">
                  {{ item.aliasName }}</div>
              </div>
              <div class="right">
                <el-table header-row-class-name="ms-table-header" v-if="tabList.length && tabList[tabIndex].tableHeader"
                  :data="tabList[tabIndex].list" tooltip-effect="dark" height="calc(100% - 54px)" v-loading="loading"
                  :key="tabList[tabIndex].tableName + tabList[tabIndex].tableHeader.length" style="width: 100%">
                  <el-table-column v-for="(item, index) in tabList[tabIndex].tableHeader" :key="index"
                    :prop="item.elementName" :label="item.aliasName" :fixed="item.isDefault"
                    :min-width="headerWidth(item.aliasName)" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <!-- 字典 -->
                      <span v-if="item.dictCode">
                        {{ dictLabel(item.dictCode, scope.row[item.elementName]) }}
                      </span>
                      <el-link v-else-if="item.elementName === 'pat_name'" type="primary"
                        @click="goToView(scope.row)">{{ scope.row[item.elementName] }}</el-link>
                      <span v-else>{{ scope.row[item.elementName] }}</span>
                    </template>
                  </el-table-column>
                </el-table>
                <pagination v-if="tabList.length && tabList[tabIndex].total > 0" :total="tabList[tabIndex].total"
                  :page.sync="tabList[tabIndex].page" :limit.sync="tabList[tabIndex].pageSize" @pagination="getList" />
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="drawer-footer" v-if="type != 'view'">
        <el-button type="success" size="small" @click="onAdopt" :loading="adoptLoading">通 过</el-button>
        <el-button type="warning" size="small" @click="dialogVisible = true">不通过</el-button>
        <el-button size="small" @click="drawer = false">取 消</el-button>
      </div>
    </el-drawer>
    <el-dialog title="原因" :visible.sync="dialogVisible" width="30%">
      <el-input type="textarea" :rows="4" placeholder="请输入内容" clearable v-model="textarea" />
      <template slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="onSubmit" :loading="adoptLoading">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listModel } from "@/api/system/mddmodel";
import { listElement } from "@/api/system/mddelement";
import { searchDataAssetsByQueue } from "@/api/scientific/cohortManage"
import { approval } from "@/api/scientific/cohortManage"
export default {
  name: 'DrawerDetails',
  props: {
    type: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: null
    },
    approvalStatusOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      drawer: false,
      activeNames: ['1', '2'],
      tabList: [],
      tabIndex: 0,
      tableHeight: 500,
      loading: false,
      defaultField: [
        {
          elementName: 'pat_name',
          aliasName: '患者姓名',
          listFlag: '1',
          fillRate: '100%',
          isDefault: true
        },
        {
          elementName: 'gender',
          aliasName: '患者性别',
          listFlag: '1',
          fillRate: '100%',
          dictCode: "sd_dict_sex",
          isDefault: true
        },
        {
          elementName: 'age',
          aliasName: '患者年龄',
          listFlag: '1',
          fillRate: '100%',
          isDefault: true
        },
        {
          elementName: 'id_card',
          aliasName: '身份证号',
          listFlag: '1',
          fillRate: '100%',
          isDefault: true
        }
      ],
      dialogVisible: false,
      textarea: '',
      adoptLoading: false
    }
  },
  computed: {
    headerWidth() {
      return (str) => {
        return str.length * 20 >= 80 ? str.length * 20 : 80
      }
    },
    dictLabel() {
      return (dictCode, val) => {
        let obj = this.$getDictOptions(dictCode).find(item => item.value == val)
        if (obj) {
          return obj.label
        } else {
          return ''
        }
      }
    }
  },
  methods: {
    // 抽屉打开时
    onOpen() {
      this.getModelList()
      this.$nextTick(() => {
        let height = document.querySelector('.content').clientHeight
        this.tableHeight = height - 62 - 48 - 24
      })

    },
    // 
    onClosed() {
      this.tabList = []
      this.tabIndex = 0
    },
    // 获取模型列表
    getModelList() {
      listModel({ modelType: 1 }).then(res => {
        this.tabList = res.rows.map(item => {
          return {
            aliasName: item.aliasName,
            tableName: item.tableName,
            list: null,
            tableHeader: [...this.defaultField, ...this.row.applicationFields.filter(v => v.tableName == item.tableName)],
            page: 1,
            pageSize: 10,
            total: 0
          }
        });
        this.getList('search')
      })
    },
    // 
    handleClick(item, index) {
      this.tabIndex = index;
      this.getList('search')
      // if (!item.tableHeader) {
      //   // this.getTableHeader(item.tableName)
      // }
    },
    // 获取展示表头
    getTableHeader(tableName) {
      let defaultF = this.defaultField.map(d => d.elementName)
      listElement({ tableName: tableName }).then(res => {
        let list = res.data.sort((a, b) => a.sortNum - b.sortNum).filter(v => !defaultF.includes(v.elementName))
        this.tabList[this.tabIndex].tableHeader = [...this.defaultField, ...list.filter(item => item.listFlag == '1')];
        this.tabList[this.tabIndex].list = [
          {
            pat_name: '张某某',
            gender: '1',
            age: '20',
            id_card: '***************'
          }
        ]
        this.tabList[this.tabIndex].total = 12;
        this.$forceUpdate();
      })
    },
    // 获取列表数据
    getList(type) {
      if (type == 'search' && this.tabList[this.tabIndex].list) return
      this.loading = true
      let tabViewFields = this.tabList[this.tabIndex].tableHeader.map(item => item.elementName)
      let params = {
        tabViewFields,
        tabViewName: this.tabList[this.tabIndex].tableName,
        page: this.tabList[this.tabIndex].page,
        pageSize: this.tabList[this.tabIndex].pageSize,
        qid: this.row.qid,
        version: this.row.version,
        searchList: []
      }
      searchDataAssetsByQueue(params).then(res => {
        this.tabList[this.tabIndex].list = res.data.dataAssetsList;
        this.tabList[this.tabIndex].total = res.data.total;
        this.$forceUpdate();
      }).finally(() => {
        this.loading = false
      })
    },
    // 页面跳转
    goToView(row) {
      this.$jumpPatient360({ path: "/scientificCdrView", query: { empi: row.empi } }, row);
    },
    // 通过
    onAdopt() {
      this.$confirm('请确认是否通过?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.adoptLoading = true
        let params = {
          qvId: this.row.qvId,
          approvalStatus: '2'
        }
        approval(params).then(res => {
          this.$message({
            type: 'success',
            message: '通过成功!'
          });
          this.drawer = false
          this.$emit('reload')
        })
      }).catch(() => {
        // this.$message({
        //   type: 'warning',
        //   message: '已取消'
        // });
      }).finally(() => {
        this.adoptLoading = false
      })
    },
    // 不通过原因提交
    onSubmit() {
      let params = {
        qvId: this.row.qvId,
        approvalStatus: '3',
        approvalRemark: this.textarea
      }
      this.adoptLoading = true
      approval(params).then(res => {
        this.dialogVisible = false
        this.textarea = ""
        this.drawer = false
        this.$emit('reload')
      }).finally(() => {
        this.adoptLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// ::v-deep .el-drawer__header {
//   margin-bottom: 0;
// }

::v-deep .el-drawer__body {
  padding: 10px;
}

.title {
  color: #333;
  font-weight: bold
}

.content {
  height: 100%;
  // padding: 12px;
  overflow: hidden;

  .el-collapse {
    // border: 0;
    height: 100%;
    // display: flex;
    // flex-direction: column;
    overflow: hidden;
  }

  .info-box {
    overflow: hidden;

    ::v-deep .el-collapse-item__wrap {
      height: calc(100% - 56px - 48px);
    }
  }

  .is-view {
    ::v-deep .el-collapse-item__wrap {
      height: calc(100% - 48px) !important;
    }
  }

  ::v-deep .el-collapse-item__content {
    padding-bottom: 12px;
    height: 100%;
  }

  .info {
    font-size: 14px;
    padding: 0 12px;
    display: flex;
    height: 100%;
    width: 100%;

    .left {
      min-width: 152px;
      border-right: solid #f5f5f5 12px;
      overflow: auto;

      .model-item {
        margin-bottom: 6px;
        cursor: pointer;
        padding: 4px 12px;
        border-radius: 4px;
        border: solid 1px #f5f5f5;
        margin-right: 12px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;

        &.active {
          border: solid 1px #409EFF;
          background: #409EFF;
          color: #fff;
        }
      }
    }

    .right {
      // flex: 1;
      padding-left: 12px;
      width: calc(100% - 152px);
    }
  }

  .pagination-container {
    margin: 12px 0;
  }

  ::v-deep .el-table__empty-block {
    width: 100% !important;
    // position: absolute
  }
}

.drawer-footer {
  height: 60px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: 0;
  width: 100%;
  margin-left: -20px !important;
  box-shadow: 0px -1px 6px rgba(0, 0, 0, 0.1);
}
</style>