<template>
  <div class="app-container box">
    <div class="left">
      <div class="searc">
        <el-input prefix-icon="el-icon-search" placeholder="请输入队列名称" clearable size="small" v-model="searchValue"
          @input="onSearchL">
        </el-input>
      </div>
      <div class="list-box">
        <div class="l-item" v-for="(item, index) in queueData" :key="index">
          <div class="costom-header" :class="queueIndex == index ? 'is-active' : ''" @click="onChange(index + 1)">
            <div class="h-left">
              <span class="q-title" :title="`${item.name ? item.name : ''}V${item.version}`" v-if="!item.isEditor">{{ item.name }} {{ `V${item.version}` }}</span>
              <div class="q-title" v-else @click.stop>
                <el-input :ref="'input' + index" size="small" placeholder="请输入队列名称" v-model="item.name"
                  @blur="handleBlur(item)" @keyup.enter.native="item.isEditor = false" />
              </div>
              <el-tag v-if="item.approvalStatus && false"
                :type="item.approvalStatus == '1' ? 'primary' : item.approvalStatus == '2' ? 'success' : 'warning'"
                size="mini">{{ item.approvalStatus == '1' ? '申请中' : item.approvalStatus == '2' ? '已通过' :
          '已拒绝' }}</el-tag>
            </div>
            <div class="more" @click.stop>
              <el-dropdown trigger="hover" placement="bottom" class="dropdown-menu">
                <i class="el-icon-more" />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <span @click="handleEditor(item, index)">重命名</span>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <span @click="handleDel(item, index)">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <ModelTree v-show="queueIndex == index" class="l-content" ref="modelTree" :modelData="item.children"
            :key="index" @getTabData="getTabData" @tableHeaderAdd="tableHeaderAdd" />
        </div>
      </div>
    </div>
    <div class="right">
<!--      <div class="search">
        <el-form :model="formData" inline ref="formRef">
          <el-form-item label="患者姓名">
            <el-input v-model="formData.name" size="small" placeholder="请输入患者姓名" clearable />
          </el-form-item>
          <el-form-item label="患者性别">
            <el-select v-model="formData.sex" filterable placeholder="请选择性别" size="small" clearable>
              <el-option v-for="item in $getDictOptions('sd_dict_sex')" :key="item.value" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" style="float: right">
            <el-button size="small" icon="el-icon-refresh" @click="onReset">重置</el-button>
            <el-button size="small" icon="el-icon-search" type="primary" @click="getTableData('search')">查询</el-button>
          </el-form-item>
        </el-form>
      </div>-->
      <div class="content">
        <div class="c-header">
          <div class="tab-box">
            <el-tabs v-model="activeName" @tab-click="onTabClick" @tab-remove="onTabRemove">
              <el-tab-pane v-for="(item, index) in tabList" :key="index + item.tableName" :label="item.aliasName" :name="item.tableName"
                :closable="!!index" />
            </el-tabs>
          </div>
          <div class="data-btn">
            <el-button size="small" @click="onDataSharing">数据共享</el-button>
            <el-button size="small" @click="onDataApplication">数据申请</el-button>
            <el-dropdown trigger="hover" placement="bottom" style="margin-left: 10px" :disabled="!downLoadList.filter(d => d.approvalStatus == 2).length">
              <el-button size="small" :loading="downLoading">{{ downLoading ? '下载中...' : '数据下载' }}</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :disabled="!item.dataFilePath" v-for="(item, index) in downLoadList" :key="index">
                  <div v-if="item.approvalStatus == '2'">
                    <span v-if="!item.dataFilePath">V{{ item.version }}数据生成中</span>
                    <span v-else @click="onDownLoad(item)">V{{ item.version }} - {{ item.updateTime }}</span>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <el-table header-row-class-name="ms-table-header" v-if="tabList.length" :data="tabList[tabIndex].tableData"
          tooltip-effect="dark" :height="tableHeight" v-loading="loading"
          :key="tabList[tabIndex].tableName + tabList[tabIndex].tableHeader.length" style="width: 100%">
          <el-table-column v-for="(item, index) in tabList[tabIndex].tableHeader" :key="index" :prop="item.elementName"
            :label="item.aliasName" :fixed="item.isDefault" :min-width="headerWidth(item.aliasName)"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <!-- 字典 -->
              <span v-if="item.dictCode">
                {{ dictLabel(item.dictCode, scope.row[item.elementName]) }}
              </span>
              <!-- 患者名称 -->
              <el-link v-else-if="item.elementName === 'pat_name'" type="primary" @click="goToView(scope.row)">{{
          scope.row[item.elementName]
        }}</el-link>
              <span v-else>{{ scope.row[item.elementName] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-if="tabList.length && tabList[tabIndex].total > 0 && tabList[tabIndex].tableData.length"
          :total="tabList[tabIndex].total" :page.sync="tabList[tabIndex].page" :limit.sync="tabList[tabIndex].pageSize"
          @pagination="getTableData" />
        <pagination v-else-if="tabList.length && tabList[tabIndex].total == 0" :total="tabList[tabIndex].total"
          :page.sync="tabList[tabIndex].page" :limit.sync="tabList[tabIndex].pageSize" @pagination="getTableData" />
      </div>
    </div>
    <DataApplication ref="dataApplication" :modelData="modelData"
      :queueRow="queueData[queueIndex] ? queueData[queueIndex] : {}" :downLoadList="downLoadList" @reloadDownLoadList="getCanBeDownloadExcelListByQId"/>
    <DataSharing ref="dataSharing" :qid="queueData[queueIndex] ? queueData[queueIndex].qId : ''" />
  </div>
</template>

<script>
import { listModel } from "@/api/system/mddmodel";
import { listElement } from "@/api/system/mddelement";
// import { listData } from "@/api/scientific/dataAssets";
import { selectCanBeDownloadExcelListByQId, updateCohort, delCohort, searchDataAssetsByQueue, getQueue, downloadExcel } from "@/api/scientific/cohortManage"
import ModelTree from "@/views/components/ModelTree/index.vue";
import DataApplication from './dataApplication.vue'
import DataSharing from './dataSharing.vue'
import { saveAs } from "file-saver";
export default {
  components: {
    ModelTree,
    DataApplication,
    DataSharing
  },
  data() {
    return {
      searchValue: null,
      formData: {},
      activeName: '',
      searchList: [],
      queueData: [],
      queueIndex: -1,
      modelData: [],
      tabList: [],
      tableHeight: 400,
      tabIndex: 0,
      paging: {
        page: 1,
        pageSize: 10
      },
      loading: false,
      downLoadList: [],
      downLoading: false
    };
  },
  computed: {
    headerWidth() {
      return (str) => {
        return str.length * 20 >= 80 ? str.length * 20 : 80
      }
    },
    dictLabel() {
      return (dictCode, val) => {
        let obj = this.$getDictOptions(dictCode).find(item => item.value == val)
        if (obj) {
          return obj.label
        } else {
          return ''
        }
      }
    }
  },
  mounted() {
    this.getQueueList()
    this.$nextTick(() => {
      let height = document.querySelector('.content').clientHeight
      this.tableHeight = height - 44 - 40 - 24 - 25
    })
  },
  methods: {
    // 获取可以下载的数据
    getCanBeDownloadExcelListByQId() {
      selectCanBeDownloadExcelListByQId({ qid: this.queueData[this.queueIndex].qId }).then(res => {
        this.downLoadList = res.data
      })
    },
    // 下载
    onDownLoad(item) {
      let name = `${this.queueData[this.queueIndex].name}_V${this.queueData[this.queueIndex].version}`
      this.downLoading = true
      downloadExcel({ qvId: item.qvId }).then(res => {
        const blob = new Blob([res]);
        saveAs(blob, `${name}.xlsx`)
      }).finally(() => {
        this.downLoading = false
      })
    },
    // 数据共享
    onDataSharing() {
      this.$refs.dataSharing.openDialog()
    },
    // 数据申请
    onDataApplication() {
      this.$refs.dataApplication.openDialog()
    },
    // 获取队列数据
    getQueueList() {
      getQueue().then(res => {
        this.queueData = res.rows.map(item => {
          return {
            ...item,
            name: item.queueName,
            isEditor: false
          }
        })
        this.getModelList()
      })
    },
    // 获取表
    getModelList() {
      listModel({ modelType: 1 }).then(res => {
        this.modelData = res.rows
        this.modelData.forEach(item => {
          if (item.tableName == 'sd_patient_info') {
            item.isShow = true
            this.getFieldList(item)
          }
        })
      })
    },
    // 获取表字段
    getFieldList(item) {
      let defaultF = this.$refs.modelTree[0].defaultField.map(d => d.elementName)
      listElement({ tableName: item.tableName }).then(data => {
        let list = data.data.sort((a, b) => a.sortNum - b.sortNum).filter(v => !defaultF.includes(v.elementName))
        listElement({ tableName: item.tableName, searchFlag: 1 }).then((rate) => {
          rate.data.forEach(v => {
            if (list.find((l) => v.elementName == l.elementName)) {
              list.find((l) => v.elementName == l.elementName).fillRate = v.fillRate
            }
          })
          item.structured = rate.data
          item.children = [...this.$refs.modelTree[0].defaultField, ...list]
          this.queueData.forEach(q => {
            q.children = JSON.parse(JSON.stringify(this.modelData))
          })
          this.searchList = JSON.parse(JSON.stringify(this.queueData))
          let index = 0
          if (this.$route.query.qId) {
            index = this.queueData.findIndex(item => item.qId == this.$route.query.qId)
          }
          this.onChange(index + 1)
          this.$forceUpdate();
        });
      })
    },
    // 左侧队列搜索
    onSearchL(val) {
      if (!val) {
        this.queueData = this.searchList
      } else {
        this.queueIndex = 0
        this.queueData = this.searchList.filter((item) => item.name.includes(val))
      }
    },
    onChange(index) {
      if (index && this.queueIndex != index - 1) {
        this.tabIndex = 0
        this.queueIndex = index - 1
        this.tabList = this.queueData[index - 1].children.filter((item) => item.isShow).map(item => {
          return {
            tableHeader: item.children.filter(v => v.listFlag == '1'),
            tableName: item.tableName,
            aliasName: item.aliasName,
            ...this.paging
          }
        })
        this.activeName = this.tabList[0].tableName
        this.$refs.modelTree[this.queueIndex].updateView()
        // console.log(this.tabList)
        setTimeout(() => {
          this.getTableData()
          this.getCanBeDownloadExcelListByQId()
        }, 50);
      }
    },
    // 队列名称编辑
    handleEditor(item, index) {
      item.isEditor = true
      setTimeout(() => {
        // console.log(this.$refs['input' + index]);
        this.$refs['input' + index][0].focus()
      })
    },
    // 队列删除
    handleDel(item, index) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        delCohort(item.qId).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.queueData.splice(index, 1)
        })
      }).catch(() => {
        // this.$message({
        //   type: 'info',
        //   message: '已取消删除'
        // });
      });
    },
    // 重命名保存
    handleBlur(item) {
      updateCohort({ qId: item.qId, queueName: item.name }).then(res => {
        item.isEditor = false
      })
    },
    // 点击tab时
    onTabClick(val) {
      this.tabIndex = Number(val.index)
      this.getTableData()
    },
    // 删除tab
    onTabRemove(val) {
      let index = this.tabList.findIndex(item => item.tableName === val)
      this.tabList.splice(index, 1)
      if (index <= this.tabIndex) {
        this.tabIndex--
        this.activeName = this.tabList[this.tabIndex]?.tableName
      }
      this.$refs.modelTree[this.queueIndex].onCancel(val)
    },
    // 获取右侧数据
    getTabData(data) {
      if (!this.tabList.some(item => item.aliasName === data.aliasName)) {
        this.tabList.push({
          tableHeader: data.children.filter(v => v.listFlag == '1'),
          tableName: data.tableName,
          aliasName: data.aliasName,
          ...this.paging
        })
      }
      this.activeName = this.tabList[this.tabIndex]?.tableName
      // 删除
      if (data.isShow === false) {
        let index = this.tabList.findIndex(item => item.aliasName === data.aliasName)
        this.tabList.splice(index, 1)
      }
      // 更新模型数据 供弹窗选择使用
      this.modelData.forEach(item => {
        if (item.aliasName === data.aliasName) {
          item.children = data.children
          item.structured = data.structured
        }
      })
      // 更新队列数据
      this.queueData.forEach(q => {
        q.children.forEach(item => {
          if (item.aliasName === data.aliasName) {
            item.children = data.children
            item.structured = data.structured
          }
        })
      })

    },
    // 表头添加字段
    tableHeaderAdd(data) {
      console.log(data);
      let tableHeader = this.tabList.find(item => item.tableName === data.tableName).tableHeader
      if (data.listFlag === '1') {
        tableHeader.push(data)
      } else {
        let index = tableHeader.findIndex(item => item.elementName === data.elementName)
        tableHeader.splice(index, 1)
      }
      this.$forceUpdate()
    },
    // 重置
    onReset() {
      this.formData = {}
      this.getTableData('search')
    },
    // 获取列表数据
    getTableData(type) {
      let tabViewFields = this.tabList[this.tabIndex].tableHeader.map(item => item.elementName)
      // console.log(tabViewFields);
      if (type == 'search') {
        this.tabList[this.tabIndex].page = 1
      }
      let params = {
        tabViewFields,
        tabViewName: this.tabList[this.tabIndex].tableName,
        page: this.tabList[this.tabIndex].page,
        pageSize: this.tabList[this.tabIndex].pageSize,
        qid: this.queueData[this.queueIndex].qId,
        version: this.queueData[this.queueIndex].version
      }
      let searchList = []
      if (this.formData.name) {
        if (!searchList.some(item => item.fieldName == 'pat_name')) {
          searchList.push(
            {
              tableName: this.tabList[this.tabIndex].tableName,
              fieldName: "pat_name",
              conditionOperator: "contains",
              conditionStartValue: this.formData.name,
              tabViewFields: [],
              elementType: "varchar",
              dictCode: ""
            }
          )
        }
      }

      if (this.formData.sex) {
        if (!searchList.some(item => item.fieldName == 'gender')) {
          searchList.push(
            {
              tableName: this.tabList[this.tabIndex].tableName,
              fieldName: "gender",
              conditionOperator: "contains",
              conditionStartValue: this.formData.sex,
              tabViewFields: [],
              elementType: "char",
              dictCode: "sd_dict_sex"
            }
          )
        }
      }
      params.searchList = searchList
      // console.log(this.queueData[this.queueIndex])
      // console.log(params, 'params', this.formData)
      if (!this.tabList[this.tabIndex].tableData || type) {
        this.loading = true
        searchDataAssetsByQueue(params).then(res => {
          this.tabList[this.tabIndex].tableData = res.data.dataAssetsList
          this.tabList[this.tabIndex].total = res.data.total
          this.$forceUpdate()
        }).finally(() => {
          this.loading = false
        })
      }
    },
    // 页面跳转
    goToView(row) {
      this.$jumpPatient360({ path: "/scientificCdrView", query: { empi: row.empi } }, row);
    },
    // 数据申请
    onDataApplication() {
      this.$refs.dataApplication.openDialog()
    },
    // 数据共享
    onDataSharing() {
      this.$refs.dataSharing.openDialog()
    }
  },
};
</script>

<style lang="scss" scoped>
.box {
  background-color: #f5f5f5;
  height: 100%;
  display: flex;

  .left {
    width: 25%;
    background-color: #fff;
    padding: 12px;
    margin-right: 12px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .searc {
      margin-bottom: 12px;
    }

    .list-box {
      flex: 1;
      overflow: auto;

      .costom-header {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px;
        height: 46px;
        line-height: 42px;
        cursor: pointer;
        .h-left {
          display: flex;
          align-items: center;
          overflow: hidden;
        }

        .q-title {
          font-size: 14px;
          margin-right: 4px;
          color: #000 !important;
          font-weight: bold !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          white-space: nowrap !important;
        }
      }

      .l-item {
        border-bottom: solid 1px #dfe6ec;
        // &:last-child{
        //   border-bottom: 0;
        // }
      }

      .l-content {
        padding: 10px;
      }

      .is-active {
        background-color: #ebf3fb;
      }
    }
  }

  .right {
    flex: 1;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .search {
      background: #fff;
      padding: 12px;
      margin-bottom: 12px;

      .el-form-item {
        margin-bottom: 0;
      }
    }

    .content {
      flex: 1;
      padding: 12px;
      background: #fff;

      .c-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }

      .tab-box {
        width: calc(100% - 300px);

        ::v-deep .el-tabs__header {
          margin: 0;
          border: 0;
          height: 32px;

          .el-tabs__nav-wrap::after {
            height: 0 !important;
          }

          .el-tabs__nav {
            border: 0;

            .el-tabs__item {
              height: 32px;
              line-height: 32px;
            }
          }
        }
      }

      .data-btn {
        margin-left: 12px;
      }

      .pagination-container {
        margin: 12px 0;
      }

      ::v-deep .el-table__empty-block {
        width: 100% !important;
        // position: absolute
      }
    }
  }
}

.el-popper {
  // margin-top: 0 !important;
}
</style>
