<template>
  <div class="app-container dataAssertManage-page2">
    <i
      id="drag-button"
      @click="showSider = !showSider"
      :class="{
        'right-drag-button': true,
        'el-icon-s-unfold': !showSider,
      }"
      v-if="!showSider"
    ></i>
    <el-row class="box">
      <!-- 右侧图标 -->
      <el-col class="sider-content white-back" :span="showSider ? 6 : 0">
        <div class="sider-title">
          数据资产目录
          <span>
            <i
              @click="showSider = !showSider"
              style="cursor: pointer;font-size: 16px;"
              :class="{
                'el-icon-s-fold': showSider,
              }"
            ></i>
          </span>
        </div>
        <div class="sider-search">
          <el-input placeholder="请输入模型名/元数据名" size="small" v-model="searchValue">
            <el-button slot="append" @click="search()"
              ><i class="el-icon-search" />查询</el-button
            >
          </el-input>
        </div>
        <div class="sider-list">
          <div v-for="col in siderData" v-if="!col.display">
            <div class="parent-name" @click="expand(col)">
              <div style="width: 16px">
                <i
                  v-if="col.children"
                  :class="
                    col.isExpand
                      ? 'el-icon-caret-bottom'
                      : 'el-icon-caret-right'
                  "
                />
              </div>
              <span style="margin: 0 4px;">
                {{ col.aliasName }}
              </span>
              <span @click.stop v-if="col.tableName == 'sd_patient_info'" class="fixed-item"
                ><i class="el-icon-s-platform"
              /></span>
              <span
                v-else
                :class="{ normalView: true, normalDisplay: col.isShow }"
                @click.stop="addTabNum(col)"
                ><i class="el-icon-s-platform"
              /></span>
            </div>
            <div v-if="col.isExpand && defaultFixedCol[col.tableName]">
              <div
                class="child-name"
                v-for="fixed in defaultFixedCol[col.tableName].filter((item) => !item.display)"
                :key="fixed.elementName"
              >
                <div class="child-row child-title" :title="fixed.aliasName">
                  {{ fixed.aliasName }}
                </div>
                <div class="eye-view child-row" style="cursor: not-allowed">
                  <img src="@/assets/images/eye-light.png" />
                </div>
                <div class="fill-rate child-row">
                  填充率：{{ fixed.fillRate }}
                </div>
              </div>
              <div 
                v-for="child in col.children.filter(
                  (item) =>
                    !fixedCol.map(f => f.elementName).includes(item.elementName) &&
                    !item.display
                )"
                :key="child.meId"
               >
                <div class="child-name">
                  <div class="child-row child-title" :title="child.aliasName">
                    {{ child.aliasName }}
                  </div>
                  <div
                    class="eye-view eye-view-special child-row"
                    @click="addTable(col, child)"
                  >
                    <img
                      v-if="child.isShow"
                      src="@/assets/images/eye-light.png"
                      alt=""
                    /><img v-else src="@/assets/images/eye-offset.png" alt="" />
                    <!-- <i class="el-icon-view" /> -->
                  </div>
                  <div class="fill-rate child-row">
                    填充率：{{ child.fillRate }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="showSider ? 18 : 24" style="height: 100%">
        <div class="main-box" :style="{ 'margin-left': showSider ? '12px' : '' }">
          <div class="search-bar">
            <div class="content-title">
              <span>数据资产管理</span>
              <div>
                <el-tooltip
                  effect="dark"
                  content="非脱敏显示申请"
                  placement="top"
                >
                  <el-button
                    size="mini"
                    circle
                    icon="el-icon-view"
                    @click="getList('', true)"
                  />
                </el-tooltip>
                <el-tooltip
                  class="download-btn"
                  effect="dark"
                  content="导出申请"
                  placement="top"
                >
                  <el-button
                    size="mini"
                    circle
                    icon="el-icon-download"
                    @click="exportDialog = true"
                  />
                </el-tooltip>
                <right-toolbar
                  :showSearch.sync="showSearch"
                  @queryTable="getList"
                ></right-toolbar>
              </div>
            </div>
            <el-row>
              <el-col
                :span="24"
                :offset="0"
                v-for="(formItem, formIndex) in queryParams"
                :key="formIndex"
              >
                <el-form
                  :model="formItem"
                  ref="queryForm"
                  :inline="true"
                  v-show="showSearch"
                  label-width="60px"
                  style="margin-top: 12px"
                >
                  <el-form-item label="视图" prop="tableName">
                    <el-select
                      v-model="formItem.tableName"
                      placeholder="请选择"
                      filterable
                      size="small"
                      @change="chooseTable($event, formIndex)"
                    >
                      <el-option
                        v-for="item in modulTableList"
                        :key="item.tableName"
                        :label="item.aliasName"
                        :value="item.tableName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item prop="fieldName" label="字段">
                    <el-select
                      v-model="formItem.fieldName"
                      placeholder="请选择"
                      filterable
                      size="small"
                      @change="changeFieldName($event, formIndex)"
                    >
                      <el-option
                        v-for="item in searchFieldsObj[formItem.tableName]"
                        :key="item.elementName"
                        :label="item.aliasName"
                        :value="item.elementName"
                      >
                        <span>{{ item.aliasName }}</span>
                        <span
                          style="
                            color: #1890ff;
                            padding-left: 4px;
                            float: right;
                          "
                          >填充率{{ item.fillRate }}</span
                        >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item prop="conditionOperator" label="运算符">
                    <el-select
                      v-model="formItem.conditionOperator"
                      placeholder="请选择"
                      filterable
                      size="small"
                    >
                      <el-option
                        v-for="item in operativeSymbolList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="参数" prop="conditionStartValue">
                    <el-select
                      v-if="formItem.dictCode"
                      v-model="formItem.conditionStartValue"
                      placeholder="请选择"
                      size="small"
                      filterable
                      :disabled="
                        formItem.conditionOperator == 'empty' ||
                        formItem.conditionOperator == 'notEmpty'
                      "
                    >
                      <el-option
                        v-for="item in dictObj[formItem.dictCode]"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                    <el-date-picker unlink-panels 
                      v-else-if="formItem.elementType == 'datetime'"
                      v-model="formItem.conditionStartValue"
                      value-format="yyyy-MM-dd"
                      type="date"
                      size="small"
                      class="set-date-width"
                      :disabled="
                        formItem.conditionOperator == 'empty' ||
                        formItem.conditionOperator == 'notEmpty'
                      "
                      placeholder="选择日期"
                    >
                    </el-date-picker>
                    <el-input
                      v-else
                      v-model="formItem.conditionStartValue"
                      :disabled="
                        formItem.conditionOperator == 'empty' ||
                        formItem.conditionOperator == 'notEmpty'
                      "
                      placeholder="请输入"
                      clearable
                      size="small"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="">
                    <i
                      class="el-icon-remove-outline icon-minus"
                      v-if="queryParams.length > 1"
                      style="cursor: pointer;"
                      @click="delSearchLine(formIndex)"
                    />
                    <i
                      class="el-icon-circle-plus-outline icon-plus"
                      v-if="formIndex == queryParams.length - 1"
                      style="cursor: pointer;"
                      @click="addSearchLine"
                    />
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
            <div class="btn-box" v-show="showSearch">
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery"
                >重置</el-button
              >
              <el-button
                type="primary"
                icon="el-icon-search"
                size="small"
                @click="getList('search')"
                >搜索</el-button
              >
            </div>
          </div>
          <div class="information-content">
            <el-tabs
              v-model="currentTab"
              type="card"
              @tab-remove="removeTab"
              @tab-click="
                cleanTableRow();
                getStatistics(currentTab);
              "
            >
              <el-tab-pane
                v-for="(item, index) in tabList"
                :key="item.tableName"
                :label="getTabCount(item)"
                :name="item.tableName"
                :closable="index == 0 ? false : true"
              >
                <el-table
                  :data="item.tableList"
                  border
                  stripe
                  v-loading="item.loading"
                  @cell-dblclick="view"
                  @cell-click="handleCellClick"
                  @sort-change="onSortChange"
                  :default-sort="defaultSortObj[item.tableName]"
                  :key="currentTab"
                >
                  <el-table-column
                    show-overflow-tooltip
                    align="center"
                    v-for="col in getCols(item.tableName)"
                    :prop="col.elementName"
                    :key="col.elementName"
                    :label="col.aliasName"
                    :sortable="col.sortFlag == '1' ? 'custom' : false"
                    :min-width="getColumnWidth(col)"
                    :fixed="!!col.fixed"
                  >
                    <template slot="header" slot-scope="scope">
                      <div class="slot-header">
                        <span>{{  scope.column.label }}</span>
                        <el-tooltip effect="dark" :content="col.elementTraceability" placement="top">
                          <i class="el-icon-warning-outline" v-if="col.elementTraceability"></i>
                        </el-tooltip>
                      </div>
                    </template>
                    <template slot-scope="scope">
                      <div v-if="col.dictCode">
                        <dict-tag
                          :options="dictObj[col.dictCode]"
                          :value="scope.row[col.elementName]"
                        />
                      </div>
                      <span v-else-if="col.elementName == 'encount_id'" class="content-text">
                        <el-link type="primary" @click="view(scope.row, 'encountId')">{{
                        scope.row[col.elementName]
                      }}</el-link></span>
                      <span v-else-if="col.elementName == 'empi'">{{ scope.row.EMPI || scope.row.empi }}</span>
                      <span v-else-if="col.elementName == 'pat_name'" @click="view(scope.row)">
                        <el-link type="primary">{{ scope.row.pat_name }}</el-link>
                      </span>
                      <span v-else>{{ scope.row[col.elementName] }}</span>
                    </template>
                  </el-table-column>
                </el-table>
                <pagination
                  v-show="item.total > 0"
                  :total="item.total"
                  :page.sync="item.pageNum"
                  :limit.sync="item.pageSize"
                  :id="item.tableName"
                  @pagination="getList"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <el-dialog
          title="选择列表"
          :visible.sync="showAlert"
          width="500px"
          @close="showAlert = false"
          top="25vh"
        >
          <div>
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                class="cb-item"
                :label="item.tableName"
                v-for="(item, index) in modulTableList"
                :disabled="item.tableName == 'sd_patient_info'"
                :key="index"
                >{{ item.aliasName }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
          <span slot="footer">
            <el-button @click="showAlert = false">取消</el-button>
            <el-button type="primary" @click="sure">确认</el-button>
          </span>
        </el-dialog>
        <el-dialog
          :title="fieldModalTitle"
          :visible.sync="fieldModal"
          width="800px"
          @close="fieldModal = false"
          top="10vh"
        >
          <div class="field-modal-content">
            <div class="field-modal-information">
              <div class="field-modal-subtitle">基本信息</div>
              <div class="field-modal-information-content">
                <el-row :gutter="20">
                  <el-col :span="4" class="field-modal-subtitle-col"
                    >姓名：{{ currentFieldRow.pat_name }}</el-col
                  >
                  <el-col :span="3" class="field-modal-subtitle-col"
                  > 性别：{{ getSexLabel(currentFieldRow.gender) }}
                    </el-col
                  >
                  <el-col :span="3" class="field-modal-subtitle-col"
                    >年龄：{{ currentFieldRow.age }}</el-col
                  >
                  <el-col :span="5" class="field-modal-subtitle-col"
                    >病历号：{{ currentFieldRow.cure_no }}</el-col
                  >
                  <el-col :span="9" class="field-modal-subtitle-col"
                    >结构化时间：{{ currentFieldRow.CreateTime }}</el-col
                  >
                </el-row>
              </div>
            </div>
            <div class="field-modal-constructor">
              <div class="field-modal-subtitle">
                {{ fieldTitle }}
              </div>
              <div class="field-modal-constructor-content">
                <div v-html="fieldContent"></div>
              </div>
            </div>
            <!-- <div class="field-modal-reg">
              <div class="field-modal-subtitle">正则列表</div>
              <div class="field-modal-reg-content">
                <div class="reg-rol" v-for="reg in regexList" :key="reg">
                  <div class="reg-col-name">
                    <span
                      :class="{
                        'reg-rol-name-select': reg[2] == 'true',
                      }"
                      >{{ reg[0] }}</span
                    >
                  </div>
                  <div class="reg-col-value">
                    <span
                      :class="{
                        'reg-rol-value-select': reg[2] == 'true',
                      }"
                      >{{ reg[1] }}</span
                    >
                  </div>
                </div>
              </div>
            </div> -->
          </div>
          <span slot="footer">
            <el-button @click="fieldModal = false">关闭</el-button>
          </span>
        </el-dialog>
      </el-col>
    </el-row>

    <!-- 右侧图标 -->
    <i
      id="drag-button"
      @click="floatDragButton()"
      class="float-drag-button el-icon-s-data"
      v-if="!drawer"
    ></i>
    <!-- 右侧图标 -->
    <el-drawer
      title="统计分析"
      style="position: absolute"
      :append-to-body="false"
      :modal-append-to-body="false"
      :visible.sync="drawer"
      size="20%"
      :modal="false"
      :wrapperClosable="true"
    >
      <div class="drawer-content" v-if="drawer && !chartLoading">
        <div class="chart-single" v-for="pie in pieData">
          <div class="chart-title">{{ pie.title }}</div>
          <div class="chart-detail">
            <pieChart :dataset="pie.echartRightItemList" />
          </div>
        </div>
        <div class="chart-single" v-for="line in lineData">
          <div class="chart-title">{{ line.title }}</div>
          <div class="chart-detail">
            <lineChart :dataset="line.itemArray" />
          </div>
        </div>
        <div class="chart-single" v-for="category in categoryData">
          <div class="chart-title">{{ category.title }}</div>
          <div class="chart-detail">
            <categoryChart :value="category.echartRightItemList" />
          </div>
        </div>
        <div class="chart-single">
          <div class="chart-title">活跃度统计</div>
          <div class="chart-detail">
            <hydChart :value="hydData" />
          </div>
        </div>
      </div>
      <div class="no-data" v-if="noData">暂无数据</div>
    </el-drawer>
    <el-dialog title="导出申请" :visible.sync="exportDialog" width="600px" append-to-body>
        <div style="margin-top: 20px;">
            <span style="font-weight: 500;margin-right: 10px;">导出类型:</span>
            <el-radio-group v-model="desensitizationAuditObject">
                <el-radio label="data_export">脱敏导出</el-radio>
                <el-radio label="data_non_desensitization_export">非脱敏导出</el-radio>
            </el-radio-group>
        </div>
        <div style="margin-top: 20px;">
            <span style="font-weight: 500;margin-right: 10px;">导出格式:</span>
            <el-radio-group v-model="exportType">
                <el-radio label="1">csv</el-radio>
                <el-radio label="2">excel</el-radio>
            </el-radio-group>
        </div>
        <div style="font-weight: 500;margin-top: 20px;margin-bottom: 10px;">附件上传:</div>
        <Upload ref="uploadRef"/>
        <div slot="footer">
            <el-button @click="exportDialog = false">取消</el-button>
            <el-button type="primary" @click="exportFile">确定</el-button>
        </div>
    </el-dialog>
  </div>
</template>

<script>
import { listModel } from "@/api/system/mddmodel";
import { listElement } from "@/api/system/mddelement";
import { getModelRestrictionsByRole } from "@/api/system/role";
import {
  listData,
  exportDataAssets,
  addBatch,
  statistics,
  hydsStatistics,
  dataTraceSource
} from "@/api/scientific/dataAssets";
import { saveAs } from "file-saver";
import lineChart from "./lineChart.vue";
import pieChart from "./pieChart.vue";
import categoryChart from "./categoryChart.vue";
import hydChart from "./hydChart.vue";
import Upload from "../../disease/dataAssertManage/upload.vue";

export default {
  name: "/dataAssertManage",
  components: {
    lineChart,
    pieChart,
    categoryChart,
    hydChart,
    Upload
  },
  data() {
    return {
      showAlert: false,
      fieldModal: false,
      fieldTitle: "",
      fieldModalTitle: "",
      fieldContent: "",
      currentFieldRow: {},
      dataTraceSource: {},
      fieldConditionList: [],
      checkList: [], //选中的列表
      showSearch: true,
      queryParams: [
        {
          tableName: "",
          fieldName: "",
          conditionOperator: "",
          conditionStartValue: "",
          tabViewFields: [],
        },
      ],
      pieData: [],
      lineData: [],
      categoryData: [],
      hydData: [],
      chartLoading: false,
      noData: false,
      operativeSymbolList: [
        { label: "大于", value: "&gt;" },
        { label: "小于", value: "&lt;" },
        { label: "大于等于", value: "&gt;=" },
        { label: "小于等于", value: "&lt;=" },
        { label: "等于", value: "=" },
        { label: "不等于", value: "!=" },
        { label: "包含", value: "contains" },
        { label: "不包含", value: "notContains" },
        { label: "为空", value: "empty" },
        { label: "不为空", value: "notEmpty" },
      ],
      currentTab: null,
      tabList: [],
      modulTableList: [], //表
      fieldsObj: {}, //所有表下面的字段
      searchFieldsObj: {}, //查询下拉选项字段
      dictObj: {}, //数据字典集合
      sexIsDict: true, //患者信息表下面的性别是否配置了字典
      drawer: false,
      showSider: false,
      searchValue: "",
      siderData: [], //表
      //当前选中的table展示字段
      currentTableRow: [],
      statisticsData: [],
      props: {
        id: "",
        label: "name",
        children: "zones",
        isLeaf: "leaf",
      },
      // fixedCol: [],
      tabViewFields: [],
      tableHeight: 0,

      // 导出申请
      exportDialog: false,
      exportType: '1',
      desensitizationAuditObject: 'data_export',

      orderByParameters: undefined,

      // 默认固定显示的列
      fixedCol: [
        {
          elementName: "pat_name",
          aliasName: "患者姓名",
          display: false
        },
        {
          elementName: "gender",
          aliasName: "患者性别",
          display: false
        },
        {
          elementName: "birthday",
          aliasName: "出生日期",
          display: false
        },
        {
          elementName: "empi",
          aliasName: "主索引",
          display: false
        },
        {
          elementName: "encount_id",
          aliasName: "就诊号",
          display: false
        },
      ],
      defaultFixedCol: {},
      defaultSortObj: {},
      defaultOrderByParameters: '',
      dataConfigObj: {}
    };
  },
  computed: {
    sortFn() {
      return (val) => {
        let item = this.currentTableRow.find(c => c.elementName == val)
        if (item && item.sortFlag == '1') {
          return 'custom'
        } else {
          return false
        }
      }
    },
    getColumnWidth() {
      return (col) => {
        let width = 0
        if (col.sortFlag == '1' || col.elementTraceability) {
          width = (col.aliasName.length * 20) + 20
        } else {
          width = col.aliasName.length * 20
        }
        // 日期类型
        if (col.elementType == 'datetime' || col.elementType == 'date') {
          width = 160
        }

        return width >= 98 ? width : 98
      }
    },
    getCols() {
      return (tableName) => {
        let arr = this.currentTableRow.filter((col) => !this.fixedCol.map(f => f.elementName).includes(col.elementName))
        if (this.defaultFixedCol[tableName]) {
          return [...this.defaultFixedCol[tableName], ...arr]
        } else {
          return [...arr]
        }
      }
    },
    getTabCount() {
      return (item) => {
        return item.total == 0 ? item.aliasName : item.aliasName + '(' + (item.showLimit && item.showLimit == item.total ? `${item.showLimit}+` : item.total) +  ')'
      }
    },
    getSexLabel() {
      return (val) => {
        if (this.dictObj["sd_dict_sex"]) {
          return this.dictObj["sd_dict_sex"].find(item => item.dictValue == val)?.dictLabel
        } 
        return ''
      }
    }
  },
  watch: {
    currentTab(newVal) {
      if (newVal && newVal != 0) {
        let tabItem = this.tabList.filter((item) => {
          if (item.tableName == newVal) {
            return item;
          }
        })[0];
        //判断当前tab下是否存在tableList , 存在不进行请求操作
        if (!tabItem.tableList) {
          setTimeout(() => {
            this.getList();
          }, 500);
        }
      }
    },
    showSearch() {
      this.calculateTableHeight()
    },
    showSider() {
      this.calculateTableHeight()
    }
  },
  mounted() {
    this.getDataConfig()
    this.getModulList();
    this.currentRow = this.siderData.find(
      (data) => data.aliasName == "sd_patient_info"
    );
    this.getStatistics("sd_patient_info");

    window.addEventListener("resize", this.calculateTableHeight);
  },
  methods: {
    // 获取数据权限配置
    getDataConfig() {
      getModelRestrictionsByRole({affiliatedSystem: 'scientific', restrictionsType: '01'}).then(res => {
        this.dataConfigObj = res.data || {}
      })
    },
    // 排序
    onSortChange({column, prop, order}) {
      if (order) {
        this.orderByParameters = order === 'descending' ? `${prop} DESC` : `${prop} ASC`;
      } else {
        this.orderByParameters = undefined
      }
      this.getList()
    },
    //添加一行查询条件
    addSearchLine() {
      this.queryParams.push({
        tableName: "",
        fieldName: "",
        conditionOperator: "",
        conditionStartValue: "",
      });
      this.calculateTableHeight()
    },
    //删除一行查询条件
    delSearchLine(index) {
      this.queryParams.splice(index, 1);
      this.calculateTableHeight()
    },
    /** 查询数据模型列表 */
    getModulList() {
      this.$modal.loading();
      this.loading = true;
      listModel({ modelType: 1 }).then((response) => {
        let list = response.rows || [];
        list.forEach((item, index) => {
          item.total = 0;
          item.pageNum = 1;
          item.pageSize = 10;
          item.loading = false;
          if (item.tableName == "sd_patient_info") {
            this.chooseTable(item.tableName);
            this.checkList.push(item.tableName);
            this.tabList.push(item);
            this.currentTab = item.tableName;
          }
        });
        this.modulTableList = list;

        this.siderData = list;
        this.siderData.forEach((data) => {
          data.isShow = data.isExpand = data.tableName == "sd_patient_info";
        });

        this.calculateTableHeight()
      });
    },
    //选择表  获取表下面的字段
    chooseTable(tableName, formIndex = null) {
      let fieldList = [];
      if (!this.fieldsObj[tableName]) {
        listElement({ tableName: tableName }).then((res) => {
          let list = res.data || [];
          list.forEach((item, index) => {
            item.isShow = item.listFlag == 1;
            if (item.dictCode) {
              if (item.dictCode && !this.dictObj[item.dictCode]) {
                this.getDictData(item.dictCode);
              }
            }
            if (
              tableName == "sd_patient_info" &&
              item.elementName == "gender" &&
              !item.dictCode
            ) {
              this.sexIsDict = false;
            }
          });

          let temp = tableName == 'sd_patient_info' ? this.fixedCol.filter(item => item.elementName != 'encount_id') : this.fixedCol
          this.defaultFixedCol[tableName] = temp.map(item => {
            let col = list.find((_list) => _list.elementName == item.elementName);
            let sdInfo = this.defaultFixedCol.sd_patient_info?.find(s => s.elementName == item.elementName)
            return col ? { ...col, display: false, fixed: true } : { ...item, fillRate: sdInfo?.fillRate, fixed: true, dictCode: sdInfo?.dictCode };
          })

          //进行排序处理
          list.sort((a, b) => {
            if (a.sortNum > b.sortNum) {
              return 1;
            } else {
              return -1;
            }
          });
          this.$set(this.fieldsObj, tableName, list);
          let data = this.siderData.find((data) => data.tableName == tableName);
          data.children = list;
          this.cleanTableRow();

          if (formIndex != null) {
            this.queryParams[formIndex].fieldName = "";
          }
          let defaultSortField = list.find(item => item.defaultSort == '1')
          if (defaultSortField) {
            this.defaultSortObj[tableName] = { prop: defaultSortField.elementName, order: 'descending' }
          }
        });
        listElement({ tableName: tableName, searchFlag: 1 }).then((res) => {
          let list = res.data || [];
          this.$set(this.searchFieldsObj, tableName, list);
        });
      }
    },
    //获取数据字典
    getDictData(dictCode) {
      this.getDicts(dictCode).then((res) => {
        res.data.forEach((item) => {
          item.label = item.dictLabel;
          item.value = item.dictValue;
          item.raw = { listClass: null };
        });
        this.$set(this.dictObj, dictCode, res.data);
      });
    },
    getList(type, flag) {
      let tabItem = this.tabList.filter((item) => {
        if (item.tableName == this.currentTab) {
          return item;
        }
      })[0];
      if (type == "search") {
        tabItem.pageNum = 1;
      }
      // 判断查询数据权限
      if (this.dataConfigObj[this.currentTab] != '0') {
        this.initElPageForbiddenBtn(tabItem)
        if (tabItem.pageSize > this.dataConfigObj[this.currentTab]) {
          // tabItem.pageSize = Number(this.dataConfigObj[this.currentTab])
          this.$modal.msgError('您没有查询权限')
          return
        } else if (tabItem.pageNum > Math.ceil(this.dataConfigObj[this.currentTab] / tabItem.pageSize)) {
          // tabItem.pageNum = Math.ceil(this.dataConfigObj[this.currentTab] / tabItem.pageSize)
          this.$modal.msgError('您没有查询权限')
          return
        }
      }
      // return
      tabItem.loading = flag ? false : true;
      
      let defaultOrderByParameters = undefined
      if (this.defaultSortObj[this.currentTab]) {
        defaultOrderByParameters = `${this.defaultSortObj[this.currentTab].prop} DESC`
      }
      let searchFlag = this.modulTableList.find(item => item.tableName == this.currentTab)?.searchFlag
      let params = {
        page: tabItem.pageNum - 1,
        pageSize: tabItem.pageSize,
        tabViewName: this.currentTab,
        searchList: this.queryParams,
        orderByParameters: this.orderByParameters || defaultOrderByParameters,
        searchFull: searchFlag,
        desensitizationAuditObject: flag ? 'data_non_desensitization_show' : undefined,
      };
      let data = [];
      this.queryParams.forEach((_data) => {
        let param = {
          tableName: _data.tableName,
          element: _data.fieldName,
          logic: _data.conditionOperator,
          parameter: _data.conditionStartValue,
        };
        data.push(param);
      });

      addBatch({ queryCondition: data }).then((res) => {
        if (res.code == 200) {
          params.tabViewFields = this.getTableName();
          listData(params).then((res) => {
            // flag为true时用于非脱敏显示申请，无需页面刷新
            if (flag) return this.$message.success("非脱敏显示申请成功");
            let list = res.data.dataAssetsList || [];
            this.$set(tabItem, "tableList", list);
            tabItem.total = res.data.total || 0;
            // 权限配置的默认显示条数
            // if (this.dataConfigObj[this.currentTab]) {
            //   let limit = Number(this.dataConfigObj[this.currentTab]);
            //   if (limit) {
            //     tabItem.total = tabItem.total > limit ? limit : tabItem.total;
            //   }
            // }
            tabItem.showLimit = res.data.showLimit;
            // tabItem.loading = false;
            // this.$modal.closeLoading();
            this.dataTraceSource.fieldConditions = res.siModelFlag;
            let _field = this.fieldConditionList.find(
              (_res) => _res.tableName == this.currentTab
            );
            if (!_field) {
              this.fieldConditionList.push({
                tableName: this.currentTab,
                fieldConditions: res.siModelFlag,
              });
            }
            setTimeout(() => {
              this.initElPageForbiddenBtn(tabItem)
            }, 50)
          }).catch(_ => {
            // tabItem.loading = false;
            // this.$modal.closeLoading();
          }).finally(() => {
            tabItem.loading = false;
            this.$modal.closeLoading();
          });
        }
      });
    },
    // 根据每次请求的结果和最大可查询size动态设置分页按钮是否可以点击 操作了DOM
    initElPageForbiddenBtn(tabItem) {
      let maxSize = this.dataConfigObj[this.currentTab] != '0' ? Number(this.dataConfigObj[this.currentTab]) : tabItem.total;
      const tableDom = document.getElementById(this.currentTab);
      // const elPagerBtn = tableDom.querySelector('.el-pager');
      // 重置后判断
      const numberNodes2 = Array.from(tableDom.querySelectorAll('.forbidden-click'));
      numberNodes2.forEach(item => {
        item.className = 'number'
      })
      const numberNodes = Array.from(tableDom.querySelectorAll('.number'));
      numberNodes.forEach((item) => {
        const currentData = item.innerText;
        if (+currentData > maxSize / tabItem.pageSize) {
          item.className = 'forbidden-click';
        } else {
          item.className = 'number';
          if (currentData == tabItem.pageNum) {
            item.className = 'number active';
          }
        }
      });
    },
    //重置
    resetQuery() {
      this.queryParams = [
        {
          tableName: "",
          fieldName: "",
          conditionOperator: "",
          conditionStartValue: "",
        },
      ];
      this.getList("search");
      this.calculateTableHeight()
    },
    //显示弹窗
    addTab() {
      this.checkList = this.tabList.map((item) => {
        return item.tableName;
      });
      this.showAlert = true;
    },
    sure() {
      let arr = [];
      this.checkList.forEach((tableName) => {
        for (let i = 0; i < this.modulTableList.length; i++) {
          if (tableName == this.modulTableList[i].tableName) {
            arr.push(this.modulTableList[i]);
            this.chooseTable(tableName);
          }
        }
      });
      this.tabList = arr;
      this.showAlert = false;
    },
    //查询条件切换字段
    changeFieldName(elementName, formItemIndex) {
      let formItem = this.queryParams[formItemIndex];
      let fieldList = this.searchFieldsObj[formItem.tableName];
      fieldList.find((item) => {
        if (item.elementName == elementName) {
          if (item.dictCode && !this.dictObj[item.dictCode]) {
            this.getDictData(item.dictCode);
          }
          formItem.elementType = item.elementType;
          formItem.dictCode = item.dictCode || "";
        }
      });
    },
    removeTab(tableName) {
      let tabs = this.tabList;
      let activeName = this.currentTab;
      if (activeName == tableName) {
        tabs.forEach((tab, index) => {
          if (tab.tableName == tableName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.tableName;
            }
          }
        });
      }

      let data = this.siderData.find((_data) => _data.tableName == tableName);
      data.isShow = false;
      this.currentTab = activeName;
      this.cleanTableRow();
      this.tabList = tabs.filter((tab) => tab.tableName !== tableName);
    },
    //导出
    exportFile(type) {
      let tabItem = this.tabList.filter((item) => {
        if (item.tableName == this.currentTab) {
          return item;
        }
      })[0];
      let params = {
        page: tabItem.pageNum - 1,
        pageSize: tabItem.pageSize,
        tabViewName: this.currentTab,
        tabViewFields: this.getTableName(),
        searchList: this.queryParams,
        exportType: this.exportType,
        desensitizationAuditObject: this.desensitizationAuditObject
      };
      // 添加附件
      if (this.$refs.uploadRef?.fileList.length) {
        params.sysTaskFileInfoList = this.$refs.uploadRef?.fileList.map(item => {
          return {
            fileUrl: item.url,
            remark: JSON.stringify({size: item.size, name: item.name})
          }
        })
      }
      this.$prompt('自定义文件名称', '系统提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        params.customExportFileName = value
        exportDataAssets(params).then((res) => {
          this.$message.success('导出申请成功')
          this.exportDialog = false
        });
      }).finally(() => {
      });
    },
    //
    view(row, type) {
      let query = {
        empi: row.EMPI || row.empi
      }
      if (type == 'encountId') {
        query.encountId = row.encount_id
      }
      
      this.$jumpPatient360({ path: "/scientificCdrView", query }, row);
    },
    floatDragButton() {
      this.drawer = !this.drawer;
    },
    search() {
      let reg = new RegExp(this.searchValue);
      this.siderData.forEach((_data) => {
        //没有子项就只和父项对比
        if (!_data.children || !_data.isShow) {
          _data.display = !reg.test(_data.aliasName);
          return;
        }
        this.defaultFixedCol[_data.tableName].forEach(col => {
          col.display = !reg.test(col.aliasName);
        })
        let fixedDisplay = this.defaultFixedCol[_data.tableName].map((_col) => _col.display)
        .some((data) => data == false);

        //非固定项
        _data.children.forEach((_child) => {
          _child.display = !reg.test(_child.aliasName);
        });

        _data.display = !(
          reg.test(_data.aliasName) ||
          _data.children
            .map((_data) => _data.display)
            .some((_data) => _data == false) ||
            fixedDisplay
        );
      });

      this.siderData = this.siderData.slice();
    },
    //左侧元数据列表展开和收缩
    expand(row) {
      if (row.children) {
        let data = this.siderData.find(
          (_data) => _data.aliasName == row.aliasName
        );
        data.isExpand = !data.isExpand;
        this.siderData = this.siderData.slice();
      }
    },
    //选择展示的tab
    addTabNum(row) {
      let _row = this.siderData.find(
        (_data) => _data.aliasName == row.aliasName
      );
      _row.isShow = !_row.isShow;
      this.siderData = this.siderData.slice();

      //添加or删除选中的tab
      let tab = this.modulTableList.find(
        (_data) => _data.tableName == row.tableName
      );
      if (_row.isShow) {
        this.tabList.push(tab);
        //加载选中tab的字段
        this.chooseTable(row.tableName);
      } else {
        //如果删除的tab页为当前显示的tab页，则默认显示患者列表
        if (row.tableName === this.currentTab) {
          this.currentTab = "sd_patient_info";
          this.cleanTableRow();
        }
        this.tabList.splice(this.tabList.indexOf(tab), 1);
      }
    },
    //选择展示的表格列
    addTable(col, row) {
      let _col = this.siderData.find(
        (_data) => _data.aliasName == col.aliasName
      );
      let _row = _col.children.find(
        (_data) => _data.elementName == row.elementName
      );

      _row.isShow = !_row.isShow;
      this.siderData = this.siderData.slice();

      if (this.currentTab == col.tableName) {
        if (_row.isShow) {
          this.currentTableRow.push(_row);
        } else {
          let tableRow = this.currentTableRow.find(
            (_col_data) => _col_data.elementName == _row.elementName
          );

          if (tableRow) {
            this.currentTableRow.splice(
              this.currentTableRow.indexOf(tableRow),
              1
            );
          }
        }
      } else {
        return;
      }
    },
    cleanTableRow() {
      let item = this.siderData.find(
        (_data) => _data.tableName == this.currentTab
      );
      this.currentTableRow = item.children.filter((data) => data.isShow);
      if (
        null != this.currentTableRow &&
        undefined != this.currentTableRow &&
        this.currentTableRow.length > 0
      ) {
        this.dataTraceSource.tableName = this.currentTableRow[0].tableName;
      }
    },
    getStatistics(tableName) {
      this.chartLoading = true;
      statistics({ tableName }).then((res) => {
        this.pieData = res.data.filter(
          (_data) => _data.type == 0 && _data.echartRightItemList.length > 0
        );
        this.lineData = res.data.filter(
          (_data) => _data.type == 1 && _data.itemArray.length > 0
        );
        this.categoryData = res.data.filter(
          (_data) => _data.type == 2 && _data.echartRightItemList.length > 0
        );
        this.chartLoading = false;
      }).catch(_ => {
        this.$modal.closeLoading();
      });
      hydsStatistics({ tableName }).then((res) => {
        this.hydData = res.data.elements;
      });
    },
    //筛选当前选中的字段
    getTableName() {
      let searchCol = [];
      let cols = this.defaultFixedCol[this.currentTab]
      let col = []
      if (cols) {
        col = cols.map(item => item.elementName);
      }
      if (this.currentTableRow.length > 0) {
        let data = [];
        this.currentTableRow.forEach((col) => {
          if (!cols.map(item => item.elementName).includes(col.elementName)) {
            data.push(col.elementName);
          }
        });
        searchCol = col.concat(data);
        return searchCol;
      } else {
        searchCol = col;
        return searchCol;
      }
    },
    handleCellClick(row, column, cell, event) {
      // return;
      // console.log("输出row",JSON.stringify(row))
      // console.log("输出column",JSON.stringify(column))
      // console.log("输出cell",cell)
      let _field = this.fieldConditionList.find(
        (_res) => _res.tableName == this.currentTab
      );
      this.currentFieldRow = row;
      let request = {
        tableName: this.dataTraceSource.tableName,
        fieldZh: column.label,
        fieldConditions: _field.fieldConditions,
        fieldConditionsValue: row[_field.fieldConditions],
      };
      // console.log(column);
      // this.fieldModal = true;
      // let obj = {
      //     "title": "主诉 || 现病史",
      //     "content": "【主诉】右侧上肢局部肿物2月余 【现病史】患者12年于我院诊断出右侧小指恶性黑色素瘤，行右侧小指恶性肿瘤根治性切除术+右侧腋窝淋巴结清扫。15年右侧前臂腹侧局部黑色瘤复发，于我院行根治性切除术+右侧腋窝淋巴结探查；16年右侧前臂背侧肿瘤再次复发，于我院行恶性肿瘤根治术。现右侧前臂腹侧再次出现肿物，现肿物直径1cm左右，单发，边界欠清，突出皮肤表面，局部皮温正常，颜色深染，无破溃出血，局部无红肿，无痛痒，易于推动，质韧。患者为进一步治疗来我院就诊。  　　　自发病以来，患者精神可，食欲、睡眠正常，二便正常，体重无明显变化。 ",
      //     "regexList": [
      //         [
      //             "无[^，。;；]*呕吐",
      //             "否",
      //             "false"
      //         ],
      //         [
      //             "(呕吐)",
      //             "是",
      //             "false"
      //         ]
      //     ]
      // }
      // this.fieldModalTitle = `结构化字段：${column.label}`;
      // this.fieldTitle = obj.title;
      // this.fieldContent = obj.content;
      // this.regexList = obj.regexList;
      dataTraceSource(request).then((res) => {
        if (
          null == res.data.content ||
          undefined == res.data.content ||
          res.data.content.length < 1
        ) {
          // this.$message("无数据");
        } else {
          // this.$alert(res.data.content, res.data.title, {
          //   showConfirmButton: false,
          //   dangerouslyUseHTMLString: true,
          // });
          this.fieldModal = true;
          this.fieldModalTitle = `结构化字段：${column.label}`;
          this.fieldTitle = res.data.title;
          this.fieldContent = res.data.content;
          this.regexList = res.data.regexList;
        }
      });
    },
    // 计算表格高度
    calculateTableHeight() {
      setTimeout(() => {
        let height = document.querySelector('.app-container').clientHeight;
        let content = document.querySelector('.search-bar').clientHeight;
        this.tableHeight = height - content - 42 - 40 - 120
      }, 150)
    }
  },
};
</script>

<style lang="scss" scoped>
.dataAssertManage-page2 {
  height: 100%;
  background-color: #f5f5f5;
  position: relative;
  .box{
    height: 100%;
    overflow: hidden;
    .main-box{
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
  .white-back {
    background-color: #fff;
    padding: 12px;
  }

  .search-bar {
    padding: 12px;
    margin-bottom: 12px;
    background: #fff;

    .content-title {
      // padding: 10px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-bottom: 12px;
      .download-btn {
        margin-right: 10px;
      }
    }

    .btn-box {
      display: flex;
      justify-content: flex-end;
      // margin: 10px 0 0px 20px;
      margin-top: 12px;
    }
  }

  .information-content {
    overflow: auto;
    background-color: #fff;
    padding: 12px;
    flex: 1;
    .slot-header {
      display: inline-flex;
      align-items: center;
      .el-icon-warning-outline {
        cursor: pointer;
        margin-left: 4px;
        font-size: 16px;
      }
    }
  }

  .sider-content {
    height: 100%;
    .sider-title {
      // padding: 12px;
      font-weight: 600;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: space-between
    }

    .sider-search {
      margin: 12px 0;
      // padding: 0 10px 10px;
    }

    .sider-list {
      height: calc(100vh - 235px);
      overflow: auto;

      .parent-name {
        padding-bottom: 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: 16px;
        .fixed-item {
          color: #38bd38;
          cursor: not-allowed;
        }

        .normalView {
          color: #e5e5e5;
          cursor: pointer;
        }

        .normalDisplay {
          color: #38bd38;
          cursor: pointer;
        }
      }

      .child-name {
        padding-bottom: 4px;
        padding-left: 35px;
        font-size: 15px;
        color: #444;
        .child-title {
          width: calc(100% - 134px);
          padding-right: 5px;
          vertical-align: bottom;
          text-overflow: ellipsis; /* ellipsis:显示省略符号来代表被修剪的文本  string:使用给定的字符串来代表被修剪的文本*/
          white-space: nowrap; /* nowrap:规定段落中的文本不进行换行   */
          overflow: hidden;
        }

        .child-row {
          display: inline-block;
        }

        .fill-rate {
          padding-left: 5px;
          color: #1890ff;
          font-size: 14px;
          display: inline-block;
        }

        .eye-view img {
          margin-left: 5px;
          height: 15px;
          width: 17px;
        }

        .eye-view-special {
          cursor: pointer;
        }
      }
    }
  }
  .el-button--mini.is-circle {
    padding: 7px;
  }

  .cb-item {
    margin-bottom: 10px;
  }
  .el-form-item {
    margin-bottom: 4px;
  }
  .content-text {
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .set-date-width {
    width: 215px;
  }

  .float-drag-button {
    position: absolute;
    right: 0;
    top: 70px;
    z-index: 6666;
    padding: 13px;
    width: 39px;
    opacity: 1;
    background-color: #fff;
    border-radius: 8px 0px 0px 8px;
    box-shadow: 0px 2px 15px 0px rgba(9, 41, 77, 0.15);
    cursor: pointer;
  }

  .right-drag-button {
    position: absolute;
    top: 70px;
    z-index: 100;
    left: 0px;
    padding: 13px;
    width: 39px;
    opacity: 1;
    background-color: #fff;
    border-radius: 0px 8px 8px 0px;
    box-shadow: 0px 2px 15px 0px rgba(9, 41, 77, 0.15);
    cursor: pointer;
  }

  .chart-single {
    margin-bottom: 10px;

    .chart-title {
      padding: 5px;
      width: 100%;
      line-height: 30px;
      font-size: 14px;
      font-weight: 600;
    }

    .chart-detail {
      padding: 20px 0 0;
    }
  }
  .el-drawer__header {
    padding: 10px;
    margin-bottom: 5px;
    border-bottom: 1px solid #eee;
  }

  .icon-plus {
    font-size: 20px;
    color: #f56c6c;
  }

  .icon-minus {
    font-size: 20px;
    color: #1890ff;
    margin-right: 3px;
  }
}

.no-data {
  text-align: center;
  margin: 20px;
  color: #e3e3e3;
}

.drawer-content {
  overflow-x: hidden;
}

::v-deep .forbidden-click {
  cursor: not-allowed;
  color: #ccc !important;
  &:hover {
    color: #ccc !important;
  }
}
.field-modal-content {
  .field-modal-subtitle {
    padding: 5px 7px;
    background-color: #7fb8e5;
    color: #fff;
    font-weight: 700;
  }
  .field-modal-subtitle-col {
    padding: 5px 7px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .field-modal-information-content {
    margin: 10px;
  }

  .field-modal-constructor-content {
    max-height: 350px;
    overflow: auto;
    margin: 10px;
  }

  .field-modal-reg-content {
    background-color: #ebf6ff;
    border-right: 1px solid #d4eaff;
    border-left: 1px solid #d4eaff;

    .reg-rol {
      border-bottom: 1px solid #d4eaff;

      .reg-col-name,
      .reg-col-value {
        display: inline-block;
        width: 50%;
        padding: 5px 10px;

        span {
          padding: 2px 5px;
        }
      }

      .reg-col-name {
        border-right: 1px solid #d4eaff;
      }
    }

    .reg-rol-name-select {
      background-color: #7fb8e5;
      color: #fff;
    }

    .reg-rol-value-select {
      background-color: yellow;
    }
  }
}
</style>
