<template>
  <div class="searchpage app-container">
    <div class="fl-search-box" >
      <div class="search-box">
        <!-- <el-tabs v-model="searchType" class="tab">
          <el-tab-pane label="患者编号检索" name="3"></el-tab-pane>
        </el-tabs> -->
        <!-- ID精确检索 -->
        <div class="search-field">
          <div class="select-box">
            <span>选择编号字段: </span>
            <el-cascader
              class="set-width"
              :props="cascaderProps"
              v-model="searchByIdForm.fieldId"
              size="small"
              @change="handelTableCol"
              ref="cascaderRef"
            ></el-cascader>
          </div>
          <el-input
            class="textarea"
            type="textarea"
            v-model="searchByIdForm.userIds"
            resize="none"
            :placeholder="`每行输入一个编号, 按回车进行换行,例如:\n1111\n2222\n3333`"
            size="normal"
          ></el-input>
          <div class="btns">
            <el-button icon="el-icon-refresh" size="small" @click="resetForm">重置</el-button>
            <el-button
              :disabled="showLoading"
              type="primary"
              icon="el-icon-search"
              size="small"
              @click="search">搜索</el-button>
          </div>
        </div>
      </div>
      <div class="drag-icon"></div>
    </div>
    <div class="fr-content-box" >
      <div class="head-box">
        <div class="total">筛选结果 <span>共{{ total }}名患者</span></div>
        <div class="fr-btn-group">
          <el-button type="primary" icon="el-icon-plus" size="small" :disabled="total == 0" @click="addCohort">添加到队列</el-button>
          <el-button class="btn" type="text" size="small" icon="el-icon-search" @click="toPage('/bigDataCenter/keywordSearch')">关键词检索</el-button>
          <el-button class="btn" type="text" size="small" icon="el-icon-user-solid" @click="toPage('/bigDataCenter/advancedSearch')">高级查询</el-button>
        </div>
      </div>
      <el-table
        :data="caseList"
        border
        stripe
        v-loading="showLoading"
        element-loading-text="正在加载中">
        <el-table-column prop="patName" label="患者姓名" :key="1"></el-table-column>
        <el-table-column prop="age" label="患者年龄" :key="2"></el-table-column>
        <el-table-column prop="gender" label="患者性别" :key="3">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sd_dict_sex"
              :value="scope.row.gender"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column prop="phone" label="手机号" :key="4"></el-table-column> -->
        <el-table-column v-for="col in columnList" :prop="col.value" :label="col.label" :key="col.value"></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="130"
          :key="5">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="view(scope.row)"
              >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        layout="sizes, prev, pager, next, jumper"
        :page.sync="searchByIdForm.pageNum"
        :limit.sync="searchByIdForm.pageSize"
        @pagination="search"
      />
    </div>
    
    <!-- 保存队列弹窗 -->
    <saveCohortAlert ref="saveCohortAlert"  @saveCohort="saveCohort"></saveCohortAlert> 
  </div>
</template>
<script>
import { searchById } from "@/api/scientific/dataCenter";
import { getCasegroup } from "@/api/scientific/dataCenter";
import { selectMddElementList } from "@/api/system/mddmodel";
import { getTableFindElementList } from "@/api/system/mddelement";
import saveCohortAlert from './saveCohortAlert'
import $ from 'jquery'
import { addCohortData } from "@/api/scientific/cohortManage"
export default {
  dicts: ["sd_dict_sex"],
  name: 'NumberSearch',
  data() {
    return {
      showLoading: false, //加载中弹窗
      searchType: "3", //搜索类型: 1快速查询 2条件筛选 3.ID精确检索
      searchByIdForm: {
        userIds: "", //ID精确检索字段,
        fieldId: "",
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      caseList: [], //id精确查找列表数据
      projectForm: {
        rsId: "",
        rgId: "",
        projectName: "", //项目名称
        projectInfo: "", //项目描述
        projectCode: "", //项目编号
        sdPatientInfo: "", //id精确检索条件
        resultSetMode: '1', //保存类型 0: 查询条件 1: 查询结果
      },
      cgId: null, //项目ID,
      projectDetail: null,
      columnList: []
    };
  },
  computed: {
    cascaderProps() {
      return {
        lazy: true,
        value: "value",
        label: "label",
        lazyLoad: this.lazyLoad,
      };
    }
  },
  components: {
    saveCohortAlert
  },
  mounted() {
    this.resetForm();
    this.drag(); // 重新调用加载数据方法
  },
  activated() {
    let cgId = this.$route.query.cgId;
    if (cgId) {
      this.cgId = cgId;
      this.getProjectDetail(cgId);
    }
    this.drag();
  },
  methods: {
    //获取项目详情
    getProjectDetail(pId) {
      getCasegroup(pId).then((res) => {
        this.projectDetail = res.data;
        this.projectForm.rsId = res.data.rsId;
        //增加延时赋值 因为rsID赋值会触发监听事件,会将rgID进行清空处理
        setTimeout((_) => {
          this.projectForm.rgId = res.data.rgId;
        }, 2000);
        this.projectForm.projectName = res.data.projectName; //项目名称
        this.projectForm.projectInfo = res.data.projectInfo; //项目描述
        this.projectForm.projectCode = res.data.projectCode; //项目编号
        this.projectForm.resultSetMode = res.data.resultSetMode;
        this.searchType = res.data.searchType;
        let conditionSearchJson = JSON.parse(res.data.conditionSearchJson);
        this.searchByIdForm.userIds = conditionSearchJson.ids.join("\n");
        this.searchByIdForm.fieldId = [
          conditionSearchJson.tableName,
          conditionSearchJson.fieldName,
        ];
        this.search()
      });
    },
    view(row) {
      let empi = "";
      if (this.searchType == 1) {
        empi = row.searchHit.sourceAsMap.empi;
      } else {
        empi = row.empi;
      }
      this.$jumpPatient360({ path: "/scientificCdrView", query: { empi: empi } }, this.searchType == 1 ? row.searchHit.sourceAsMap : row);
    },
    //查询
    search() {
      this.searchById();
    },
   
    //加载ID精确检索选择id字段数据
    lazyLoad(node, resolve) {
      const { level } = node;
      if (level == 0) {
        selectMddElementList({ modelType: 1 }).then((response) => {
          let list = response.data;
          for(let i = list.length - 1; i >= 0; i--) {
            if(list[i].searchIdFlag != 1) {
              list.splice(i, 1);
            }else {
              list[i].label = list[i].aliasName;
              list[i].value = list[i].tableName;
            }
          }
          this.tableList = list
          resolve(list);
        });
      } else if (level == 1) {
        getTableFindElementList({
          tableName: node.value,
          searchIdFlag: 1,
        }).then((res) => {
          let fieldList = [];
          res.data.forEach((item, fIndex) => {
            fieldList.push({
              label: item.aliasName,
              value: item.elementName,
              leaf: true,
            });
          });
          resolve(fieldList);
        });
      }
    },
    //id精确查询
    searchById() {
      if (this.searchByIdForm.fieldId.length == 0) {
        this.$message.error("请选择编号字段");
        return false;
      } else if (this.searchByIdForm.fieldId.length == 1) {
        this.$message.error("您选择的编号字段无效, 请重新选择");
        return false;
      }
      let ids = this.searchByIdForm.userIds;
      ids = ids.replaceAll("\n", ",");
      if (!ids) {
        this.$message.error("请输入编号后在进行查询");
        return false;
      }
      ids = ids.split(",");
      this.showLoading = true;
      let params = {
        ids: ids,
        tableName: this.searchByIdForm.fieldId[0],
        fieldName: this.searchByIdForm.fieldId[1],
        pageNum: this.searchByIdForm.pageNum,
        pageSize: this.searchByIdForm.pageSize
      };
      searchById(params).then((res) => {
        this.total = res.total || 0;
        this.caseList = res.rows || [];
        this.showLoading = false;
        this.extra = res.extra || '';
      }).catch(err => {
        this.showLoading = false;
      });
    },
    // 动态展示检索字段
    handelTableCol(e) {
      let node = this.$refs.cascaderRef.getCheckedNodes()[0]
      let field = this.toCamelCase(node.value)
      if (field == 'empi') {
        field = 'EMPI'
      }
      this.columnList = [
        {
          label: node.label,
          value: field
        }
      ]
    },
    // 下划线转驼峰
    toCamelCase(str) {
      return str.replace(/_([a-z])/g, function (match, letter) {
        return letter.toUpperCase();
      });
    },

    //重置
    resetForm() {
      this.searchByIdForm.userIds = "";
      this.caseList = [];
      this.total = 0;
      this.projectForm = {
        rsId: "",
        rgId: "",
        projectName: "", //项目名称
        projectInfo: "", //项目描述
        projectCode: "", //项目编号
        sdPatientInfo: "", //id精确检索条件
        resultSetMode: "1", //保存类型 0: 查询条件 1: 查询结果
      }
      this.projectDetail = null;
    },
    

    //拖拽改变条件编辑器宽度
    drag() {
      let _this = this;
      $('.drag-icon').mousedown(function(e) {
        $(this).css({
          'background':'#6299eb'
        })
        let startX = e.clientX;
        let leftMarWidth = $('.sidebar-container').width() + 20;
        document.onmousemove = function (e) {
              let flWidth = e.clientX - leftMarWidth;
              if(flWidth > 570 && flWidth < 1130) {
                $('.fl-search-box').css({
                  'width': flWidth+'px'
                })
                $('.fr-content-box').css({
                  'margin-left': flWidth+30+'px'
                })
              }
        };
        // 鼠标松开事件
        document.onmouseup = function (evt) {
          document.onmousemove = null;
          document.onmouseup = null;
          $('.drag-icon').css('background', '');
        };
      })
    },
    //切换查询页面
    toPage(path) {
      this.$router.push({path})
    },
    //添加队列
    addCohort() {
      this.$refs.saveCohortAlert.initData();
    },

    //保存队列
    saveCohort(obj) {
      let params = {
        qId: obj.qId,
        keyMD5Data: this.extra,
        varsTotal: 3,
        searchType: this.searchType,
        patientTotal: this.total,
        resultSetMode: "1"
      }
      let ids = this.searchByIdForm.userIds;
      ids = ids.replaceAll("\n", ",");
      ids = ids.split(",");
      params.sdPatientInfo = {
        ids: ids,
        tableName: this.searchByIdForm.fieldId[0],
        fieldName: this.searchByIdForm.fieldId[1],
      };

      this.$confirm(`确定将查询的病例保存到“${obj.queueName}”中吗?`,"提示",{
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(() => {
            addCohortData(params).then(res => {
              this.$message.success('保存成功');
              this.$refs.saveCohortAlert.close();
            })
          })
    }
  }
};
</script>

<style lang="scss" scoped>
.searchpage {
  height: 100%;
  padding-top: 20px;
  .head-box {
    padding: 0 0 15px 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .total {
      // float: left;
      // padding: 10px 0;
      font-size: 16px;
      font-weight: bold;
      span {
        font-size: 14px;
        color: #666;
        padding: 0 0 0 10px;
        font-weight: normal;
      }
    }
    .fr-btn-group {
      text-align: right;
    }
  }
  .fl-search-box {
    float: left;
    width: 570px;
    border: 1px solid #ddd;
    border-radius: 4px;
    // height: calc(100vh - 140px);
    height: 100%;
    position: relative;
    // transition: .5s ease all;
    .search-box {
      overflow: hidden;
      height: 100%;
      padding: 12px;
    }
    .tab {
      padding: 0 10px;
    }
    .search-field {
      // padding: 0 10px 0;
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .btns {
        border-top: 1px solid #eee;
        text-align: center;
        padding-top: 12px;
        margin-top: 12px;
      }
      .textarea {
        flex: 1;
        ::v-deep textarea {
          height: 100% !important;
        }
      }
      .select-box {
        margin-bottom: 12px;
        .set-width {
          width: 300px;
        }
        span {
          font-size: 13px;
          padding-right: 5px;
          color: #555;
        }
      }
    }
    .arrow-icon {
      position: absolute;
      top: 49%;
      right: -23px;
      font-size: 23px;
      width: 22px;
      height: 52px;
      background: #0b5ca7;
      line-height: 52px;
      text-align: center;
      color: #fff;
      cursor: pointer;
    }
    .drag-icon {
      position: absolute;
      top: 47%;
      right: -10px;
      font-size: 23px;
      width: 11px;
      height: 52px;
      line-height: 52px;
      text-align: center;
      cursor: ew-resize;
      overflow: hidden;
      background: #ccc;
      background-image: url('~@/assets/images/disease/drag-icon1.png') !important;
      background-position: -6px 10px !important;
      background-repeat: no-repeat !important;
      .icon {
        position: relative;
        left: -6px;
        vertical-align: top;
        margin-top: 10px;
      }
    }
    .drag-icon:hover {
      background: #1890ff ;
    }
  }
  .fr-content-box {
    margin-left: 600px;
  }
  .add-group-btn {
    margin-left: 10px;
  }
}
</style>
