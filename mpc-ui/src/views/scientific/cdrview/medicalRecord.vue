<template>
    <div class="medicalRecord-container" v-loading="loading">
        <el-form :inline="true" :model="searchForm" size="small" class="demo-form-inline">
          <el-form-item label="就诊日期">
            <el-date-picker unlink-panels 
              v-model="searchForm.startDate"
              type="date"
              placeholder="选择就诊开始日期"
              value-format="yyyy-MM-dd"
              @change="changeDate($event,1)"
              :clearable="false"
            >
            </el-date-picker>
             - 
            <el-date-picker unlink-panels 
              v-model="searchForm.endDate"
              type="date"
              placeholder="选择就诊结束日期"
              value-format="yyyy-MM-dd"
               @change="changeDate($event,2)"
               :clearable="false"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="init" :disabled="!searchForm.startDate || !searchForm.endDate">查询</el-button>
          </el-form-item>
        </el-form>
        <div class="legend-box">
            <div class="legend-item" v-for="(item, index) in dictList" :key="index">
                <!-- {{item.label}} <span :style="`background: ${colorList[index]}`"></span> -->
                {{item.dictLabel}}<span :style="`background: ${getColor(item.dictValue)}`"></span>
            </div>
        </div>
        <div class="chart-box" id="chart" ref="chart"></div>
         <div class="legend-box">
            <div class="legend-item" v-for="(item, index) in type" :key="index">
                {{item}} <span :style="`background: ${colorList1[index]}`"></span>
            </div>
        </div>
        <div class="chart-box chart-box1" id="chart1" ref="chart1"></div>
    </div>
</template>

<script>
    require('echarts/theme/macarons') // echarts theme
    import * as echarts from 'echarts';
    import {
        getHospitalizeList,
        getTimeAxisSurgeryList,
        getTimeAxisOrderList,
        getTimeAxisChemList,
        getTimeAxisTechList,
        getTimeAxisDiagnosticList
    } from "@/api/scientific/cdrview";
    import {formatDate} from '@/utils/index'
    import $ from 'jquery'
    import {mapGetters} from 'vuex'

    export default {
    dicts: ["sd_dict_cureType", "sd_diagnostic_type"],
        data() {
            return {
                searchForm: {
                    startDate: '',
                    endDate: ''
                },
                hospitalData: [], //就诊记录
                colorList: ['#e0bc78','#ee6666','#15a54e', '#75d874', '#FFC0CB', '#6b8aeb','#dd741a','#FFA500','#FFE4B5','#78c3e0','#4B0082'],
                colorObj:{},
                type: ['用药', '手术', '检验', '检查', '诊断'],
                colorList1: ['#768dd1','#95ce7b', '#78c3e0', '#e0bc78', '#dd741a'],
                differenceValue: 600, // 用于将时间戳计算为分钟
                dictList:[],
                loading:false,
            }
        },
        props: {
            startDate: {
                type: String,
            },
            endDate: {
                type: String,
            },
        },
        computed: {
            ...mapGetters(['currentSelectedDisease'])
        },
        watch: {
            currentSelectedDisease: {
                handler(newVal, oldVal) {
                    this.init();
                },
                deep: true
            }
        },
        async mounted() {
            this.loading = true
            this.searchForm.startDate = this.startDate && this.startDate.substr(0,10) + ' 00:00:00';
            this.searchForm.endDate = this.endDate && this.endDate.substr(0,10) + ' 23:59:59';
            this.empi = this.$route.query.empi || "";
            // 先处理颜色对应
                // 1.找到字典
            try {
                let dictArr = []
                const { data } = await this.getDicts('sd_dict_cureType');            
                if(data) {
                    dictArr = data.map(item => item.dictValue)
                    // 2字典对应颜色值
                    dictArr.forEach((item, index) => {
                        this.colorObj[item] = this.colorList[index]
                    })
                    this.dictList = data;
                }                
            }finally  {
                 this.loading = false
            } 
            if(this.empi) {
                this.init();
            }
        },
        methods: {
            getColor(item){
                return this.colorObj[item];
            },
            //切换时间进行数据拼接
            changeDate(e, type) {
                if(!e) return;
                if(type ==1) {
                    this.searchForm.startDate = e + ' 00:00:00';
                }else {
                    this.searchForm.endDate = e + ' 23:59:59';
                }
            },
            init() {
                let params = {
                    empi: this.empi,
                    startDate: this.searchForm.startDate,
                    endDate: this.searchForm.endDate,
                };
                let allValue = []; //就诊数据
                getHospitalizeList(params).then((res) => {
                    //获取数据，并按照就诊日期升序排序
                    this.hospitalData = res.rows.sort(function (a, b) {
                        return new Date(a.cureDate) - new Date(b.cureDate);
                    });
                    // 获取住院字典对应的字典值value（项目上字典配置不一样）
                    let cureTypeDicts = this.dict.type.sd_dict_cureType
                    let cureTypeDictVals = cureTypeDicts.filter(item => item.label.includes('住院'))
                    let cureTypeDictVal = cureTypeDictVals.map(item => item.value)
                    this.hospitalData.forEach((item, index) => {
                        // 处理未出院的情况
                        if (cureTypeDictVal.includes(item.cureType) && !item.dischargeDate) {
                            item.dischargeDate = formatDate(new Date())
                        }
                        let obj = {
                            "name": this.selectDictLabel(this.dict.type.sd_dict_cureType, item.cureType),
                            "value": [0],
                            "itemStyle": {
                                "normal": {
                                    "color": this.getColor(item.cureType)
                                }
                            },
                            itemInfo: item
                        }
                        if(cureTypeDictVal.includes(item.cureType)) {
                            obj.value.push(+new Date(item.cureDate) / this.differenceValue);
                            obj.value.push(+new Date(item.dischargeDate) / this.differenceValue);
                        }else {
                            let date = item.cureDate.substr(0,10)
                            obj.value.push(+new Date(date+' 00:00:00') / this.differenceValue)
                            obj.value.push(+new Date(date+' 23:59:59')/ this.differenceValue)
                        }
                        allValue.push(obj)
                    })
                    this.$nextTick(_ => {
                        this.initChart(allValue);
                    })
                });

                let list1 = [];
                let list2 = [];
                let list3 = [];
                let list4 = [];
                let list5 = [];
                //获取手术记录 and 获取用药记录 and 获取检验 and 获取检查
                getTimeAxisSurgeryList(params)
                .then(res => {
                    list1 = res.data || [];
                    return getTimeAxisOrderList(params)
                }).then(res => {
                    list2 = res.data || [];
                    return getTimeAxisChemList(params)
                }).then(res => {
                    list3 = res.data || [];
                    return getTimeAxisTechList(params)
                }).then(res => {
                    list4 = res.data || [];
                    params.diseaseSyscode = this.currentSelectedDisease.diseaseSyscode;
                    return getTimeAxisDiagnosticList(params)
                }).then(res => {
                    list5 = res.data || [];
                    let dataArr1 = [];
                    let dataArr2 = [];
                    let dataArr3 = [];
                    let dataArr4 = [];
                    let dataArr5 = [];
                    list1.forEach((item, index) => {
                        let time = +new Date(item.beginTime) / this.differenceValue;
                        dataArr1.push([time, 1, 12, this.colorList1[1], item]);
                    })
                    list2.forEach((item, index) => {
                        let time = +new Date(item.startDate) / this.differenceValue;
                        dataArr2.push([time, 0, 12, this.colorList1[0], item]);
                    })
                    list3.forEach((item, index) => {
                        let time = +new Date(item.checkTime) / this.differenceValue;
                        dataArr3.push([time, 2, 12, this.colorList1[2], item]);
                    })
                    list4.forEach((item, index) => {
                        let time = +new Date(item.beginTime) / this.differenceValue;
                        dataArr4.push([time, 3, 12, this.colorList1[3], item]);
                    })
                    list5.forEach((item, index) => {
                        let time = +new Date(item.diagnosticDate) / this.differenceValue;
                        dataArr4.push([time, 4, item.firstDiagnosticFlag == 1 ? 24 : 12, this.colorList1[4], item]);
                    })
                    this.initChart1([...dataArr1, ...dataArr2, ...dataArr3, ...dataArr4, ...dataArr5])
                })
            },



            initChart(value) {
                let _this = this;
                var chartDom = document.getElementById('chart');
                this.myChart = echarts.init(chartDom);
                var data = value;
                let minValue = +new Date(this.searchForm.startDate) / this.differenceValue;
                var categories = ['就诊记录'];
                function renderItem(params, api) {
                    var categoryIndex = api.value(0);
                    var start = api.coord([+new Date(api.value(1)) , categoryIndex]);
                    var end = api.coord([+new Date(api.value(2)), categoryIndex]);
                    var height = 70;
                    var rectShape = echarts.graphic.clipRectByRect(
                        {
                            x: start[0],
                            y: start[1] - height / 2,
                            width: end[0] - start[0],
                            height: height
                        },
                        {
                            x: params.coordSys.x,
                            y: params.coordSys.y,
                            width: params.coordSys.width,
                            height: params.coordSys.height
                        }
                    );
                    return (
                        rectShape && {
                        type: 'rect',
                        transition: ['shape'],
                        shape: rectShape,
                        style: api.style()
                        }
                    );
                }
                var option = {
                    tooltip: {
                        position: 'top',
                        formatter: function (params) {
                            let arr = [];
                            if(params.data.itemInfo.dischargeDate) {
                                arr = [
                                    '入院时间: '+params.data.itemInfo.cureDate,
                                    '出院时间: '+params.data.itemInfo.dischargeDate,
                                    '就诊类型: '+params.data.name,
                                    '就诊科室: '+params.data.itemInfo.deptName,
                                    '主治医生: '+params.data.itemInfo.attendingDr
                                ]
                            } else {
                                arr = [
                                    '就诊时间: '+params.data.itemInfo.cureDate,
                                    '就诊类型: '+params.data.name,
                                    '就诊科室: '+params.data.itemInfo.deptName,
                                    '主治医生: '+params.data.itemInfo.attendingDr
                                ]
                            }
                            return arr.join('<br/>');
                        }
                    },
                    dataZoom: [
                        {
                            type: 'slider',
                            filterMode: 'weakFilter',
                            showDataShadow: false,
                            top: 110,
                            labelFormatter: ''
                        },
                        {
                            type: 'inside',
                            filterMode: 'weakFilter'
                        }
                    ],
                    grid: {
                        height: 60,
                        top: 10,
                        left: 60,
                        right: 60,
                        containLabel: true
                    },
                    xAxis: {
                        min:  +new Date(this.searchForm.startDate) / this.differenceValue,
                        max: +new Date(this.searchForm.endDate) / this.differenceValue,
                        scale: true,
                        minInterval: 86400000 / this.differenceValue, //24小时的毫秒数
                        axisLabel: {
                            formatter: function (val) {
                                let date = formatDate(val * _this.differenceValue);
                                return date.substr(0,4) +'\n' + date.substr(5,14);
                            }
                        },
                        splitLine: {
                            show: false
                        },
                        axisLine: {
                            show: true
                        },
                    },
                    yAxis: {
                        data: categories,
                        show: false
                    },
                    series: [
                        {
                            type: 'custom',
                            renderItem: renderItem,
                            itemStyle: {
                                opacity: 1
                            },
                            encode: {
                                x: [1, 2],
                                y: 0
                            },
                            data: data
                        }
                    ]
                };
                this.myChart.setOption(option);
                this.myChart.on('datazoom', (params) => {
                    this.myChart1.dispatchAction({
                        type: 'dataZoom',
                        start: params.start,
                        end: params.end,
                    })
                })
            },

            initChart1(dataArr) {
                let _this = this;

                var chartDom = document.getElementById('chart1');
                this.myChart1 = echarts.init(chartDom, 'macarons');
                const data = dataArr
                // [0, 0, 5] 第一个值表示类型  第二个值表示要显示的位置  第三个值表示显示的大小
                var option = {
                    tooltip: {
                        position: 'top',
                        formatter: function (params) {
                            let obj = params.data[4]
                            let arr = []
                            if(params.data[1] == 0) {
                                arr = [
                                    '<span style="font-size: 16px; font-weight:bold">用药:</span>',
                                    '医嘱类别: ' + obj.orderCategory,
                                    '开医嘱时间: ' + obj.opTime,
                                    '药品名称: ' + (obj.drugName.length > 18 ? obj.drugName.substr(0,18) + '...' : obj.drugName),
                                ]
                            }else if(params.data[1] == 1) {
                                arr = [
                                    '<span style="font-size: 16px; font-weight:bold">手术:</span>',
                                    '手术名称: ' + obj.surgeryName,
                                    '手术时间: ' + obj.beginTime,
                                    '麻醉方式: ' + obj.anaesthesia,
                                    '手术级别: ' + obj.surgeryLevel,
                                    '手术体位: ' + obj.surgeryPosition
                                ]
                            }else if(params.data[1] == 2) {
                                arr = [
                                    '<span style="font-size: 16px; font-weight:bold">检验:</span>',
                                    '检验类型: ' + (obj.itemName.length > 18 ? obj.itemName.substr(0, 18) + '...' : obj.itemName),
                                    '样本类型: ' + obj.sampleType,
                                    '检验项目: ' + (obj.subitemName.length > 18 ? obj.subitemName.substr(0, 18) + '...' : obj.subitemName),
                                    '检验时间: ' + obj.checkTime,
                                    '报告时间: ' + obj.reportDate
                                ]
                            }else if(params.data[1] == 3){
                                arr = [
                                    '<span style="font-size: 16px; font-weight:bold">检查:</span>',
                                    '检查类型: ' + (obj.itemName.length > 18 ? obj.itemName.substr(0, 18) + '...' : obj.itemName),
                                    '检查项目: ' + (obj.subitemName.length > 18 ? obj.subitemName.substr(0, 18) + '...' : obj.subitemName),
                                    '检查时间: ' + obj.beginTime,
                                    '报告时间: ' + obj.reportDate
                                ]
                            }else if(params.data[1] == 4){
                                arr = [
                                    '<span style="font-size: 16px; font-weight:bold">'+(obj.firstDiagnosticFlag == 1 ? '首次诊断' : '诊断')+':</span>',
                                    '就诊类型: ' + _this.dict.label.sd_dict_cureType[obj.cureType],
                                    '诊断编码: ' + obj.icdCode,
                                    '诊断名称: ' + (obj.diagnosticName.length > 18 ? obj.diagnosticName.substr(0, 18) + '...' : obj.diagnosticName),
                                    '诊断时间: ' + obj.diagnosticDate
                                ]
                            }
                            return arr.join('<br/>');
                        }
                    },
                    grid: {
                        left: 60,
                        right: 60,
                        top: 20,
                        containLabel: true
                    },
                    xAxis: {
                        min: +new Date(this.searchForm.startDate) / this.differenceValue,
                        max: +new Date(this.searchForm.endDate) / this.differenceValue,
                        scale: true,
                        minInterval: 86400000 / this.differenceValue, //24小时的毫秒数
                        axisLabel: {
                            formatter: function (val) {
                                let date = formatDate(val * _this.differenceValue);
                                return date.substr(0,4) +'\n' + date.substr(5,14);
                            }
                        },
                        splitLine: {
                            show: false
                        },
                        splitArea: {
                            show: false
                        }
                    },
                    yAxis: {
                        type: 'category',
                        data: this.type,
                        axisLine: {
                            show: false
                        },
                        splitLine: {
                            show: true
                        }
                    },

                    dataZoom: [
                        // {
                        //     type: 'inside'
                        // },
                        // {
                        //     type: 'slider',
                        //     bottom:0
                        // }
                        {
                            type: 'slider',
                            filterMode: 'weakFilter',
                            showDataShadow: false,
                            bottom: -300,
                            labelFormatter: ''
                        },
                        {
                            type: 'inside',
                            filterMode: 'weakFilter'
                        }
                    ],
                    series: [
                        {
                            type: 'scatter',
                            symbolSize: function (val) {
                                return val[2];
                            },
                            symbol: function (val) {
                                if(val[1] == 4 && val[4].firstDiagnosticFlag == 1) {
                                    let src = require('@/assets/images/disease/img-icon.png')
                                    return 'image://'+src
                                }
                            },
                            data: data,
                            animationDelay: function (idx) {
                                return idx * 5;
                            },
                            itemStyle: {
                                color: function(param) {
                                    return param.data[3]
                                }
                            }
                        }
                    ]
                }
                this.myChart1.setOption(option);
                // this.myChart1.on('datazoom', (params) => {
                //     this.myChart.dispatchAction({
                //         type: 'dataZoom',
                //         start: params.start,
                //         end: params.end,
                //     })
                // })
            }
        }
    }
</script>

<style lang="scss" scoped>
    .medicalRecord-container {
        .legend-box {
            overflow: hidden;
            text-align: center;
            margin: 40px 0 0;
            .legend-item {
                display: inline-block;
                font-size: 14px;
                color: #666;
                margin: 0 20px 0 0;
                span {
                    width: 34px;
                    height: 16px;
                    display: inline-block;
                    border-radius: 3px;
                    // margin: 0 0 0 5px;
                    vertical-align: middle;
                }
            }
        }
        .chart-box {
            height: 150px;
            margin:20px 0 50px 0;
        }
        .chart-box1 {
            height: 300px;
            margin-top: 20px;
        }
    }
</style>
