<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" :inline="true"  label-width="68px">
            <el-form-item label="所属病种" prop="diseaseType">
                <el-select v-model="queryParams.diseaseType" placeholder="请选择病种" clearable size="small" style="width: 240px">
                    <el-option v-for="item in depDiseaseList" :key="item.diseaseCode" :label="item.diseaseName" :value="item.diseaseSyscode" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-table  v-loading="loading"  :data="dataList">
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column label="批次号" align="center" prop="batchNumber" />
            <el-table-column label="执行时间" align="center" prop="checkStartDate" />
            <el-table-column label="所属病种" align="center" prop="diseaseType" />
            <el-table-column label="同步患者数" align="center" prop="sdPatientInfo" >
              <template slot-scope="scope">
                <div>
                  <el-link @click="getPatientData(scope.row)" type="primary" v-if="scope.row && scope.row.sdPatientInfo > 0" >{{scope.row.sdPatientInfo}}</el-link>
                  <span v-else-if="scope.row">{{scope.row.sdPatientInfo}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="同步就诊数" align="center" prop="sdHospitalizeInfo" />
            <el-table-column label="同步诊断数" align="center" prop="sdDiagnosticInfo" />
            <el-table-column label="同步检查数" align="center" prop="sdTechcheckDoc" />
            <el-table-column label="同步检验数" align="center" prop="sdChemcheckDoc" />
            <el-table-column label="同步医嘱数" align="center" prop="sdOrderDetail" />
            <el-table-column label="同步手术记录数" align="center" prop="sdSurgeryRecord" />
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList"/>


          <!-- 查看同步患者明细 -->
          <el-dialog
            title="同步患者明细"
            :visible.sync="showPatientAlert"
            width="900px"
            @close="showPatientAlert = false">
              <el-table  :data="patientDataList">
                <el-table-column label="患者姓名" align="center" prop="patName" >
                  <template slot-scope="scope">
                    <el-link type="primary" @click="view(scope.row)">{{ scope.row.patName }}</el-link>
                  </template>
                </el-table-column>
                <el-table-column label="出生日期" align="center" prop="birthday" />
                <el-table-column label="年龄" align="center" prop="age" />
                <el-table-column label="民族" align="center" prop="anationge" />
                <el-table-column label="电话" align="center" prop="phone" />
                <el-table-column label="地址" align="center" prop="address" :show-overflow-tooltip="true"/>
              </el-table>
              <pagination v-show="patientTotal>0" :total="patientTotal" :page.sync="queryPatientParams.pageNum" :limit.sync="queryPatientParams.pageSize" @pagination="getPatientData()"/>
            <span slot="footer">
              <el-button type="primary" @click="showPatientAlert = false">关闭</el-button>
            </span>
          </el-dialog>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
import {getSdNaPaiInfo, getNaPaiPatient} from '@/api/scientific/dataStorage'
export default {
  name: "ReceiveSort",
  data() {
    return {
        loading: false,
        total: 0,
        dataList: [],
        // 查询参数
        queryParams: {
            diseaseType: null,
            pageNum: 1,
            pageSize: 10
        },
        //患者明细弹窗
        showPatientAlert: false,
        //查询患者的参数
        queryPatientParams: {
          pageNum: 1,
          pageSize: 10,
          batchFlag: '',
          diseaseSyscode: ''
        },
        patientTotal: 0, //患者总数
        patientDataList: [
          // {
          //   name: '张三',
          //   age: 80,
          //   sex: '男',
          //   mobile: '13333333333'
          // }
        ],
        currentClickRecord: {}, //当前点击的这条记录
    };
  },
  computed: {
    ...mapGetters(["depDiseaseList"]),
    },
  mounted() {
    if(this.depDiseaseList.length > 0) {
      this.queryParams.diseaseType = this.depDiseaseList[0].diseaseSyscode;
    }
    this.getList();
  },
  methods: {
    getList() {
    //   this.loading = true;
        getSdNaPaiInfo(this.queryParams)
        .then(res => {
            this.dataList = res.data.content;
            this.total = res.data.totalElements;
        })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      if(this.depDiseaseList.length > 0) {
        this.queryParams.diseaseType = this.depDiseaseList[0].diseaseSyscode;
      }
      this.handleQuery();
    },

    /**
     * 获取同步患者明细
     */
    getPatientData(row = this.currentClickRecord) {
      if(row) {
        this.currentClickRecord = row;
      }
      let params = {
        pageNum: this.queryPatientParams.pageNum,
        pageSize: this.queryPatientParams.pageSize,
        batchFlag: row.batchNumber,
        diseaseSyscode: row.diseaseType
      }
      this.showPatientAlert = true;
      getNaPaiPatient(params)
      .then(res => {
        this.patientTotal = res.total || 0 //患者总数
        this.patientDataList = res.rows || [];
      })
    },
    //查看360
    view(row) {
      this.$jumpPatient360({ path: "/scientificCdrView", query: { empi: row.empi } }, row);
    }
  }
};


</script>
