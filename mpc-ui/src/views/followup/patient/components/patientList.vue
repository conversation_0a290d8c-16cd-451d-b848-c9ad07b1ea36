<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="患者来源" prop="source">
        <el-select v-model="queryParams.source" size="small" placeholder="请选择">
          <el-option
            v-for="item in dict.type.patient_source"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="患者姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入患者姓名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年龄" prop="minAge">
        <div class="range-input-container">
          <el-input
            v-model="queryParams.minAge"
            type="number"            
            size="small"
            placeholder="请输入最小值"
          ></el-input>
          <span class="range-separator">-</span>
          <el-input
            v-model="queryParams.maxAge"
            type="number"            
            size="small"
            @change="handleMaxAge"
            placeholder="请输入最大值"
          ></el-input>
        </div>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          v-hasPermi="['followup:info:query']"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" style="margin-bottom: 8px;">
      <el-col :span="1.5">
        <el-dropdown
          @command="(command) => handleCommand(command)"            
        >
          <el-button type="primary" plain size="mini" v-hasPermi="['followup:info:add']" :disabled="!queueId"> 新增 </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                command="handleAdd"
                >添加新患者</el-dropdown-item
              >
              <el-dropdown-item
                command="addFormQueue"
                >从队列中添加</el-dropdown-item
              >
              <el-dropdown-item
                @click.native="handleImport"
                >
                <upload-add ref="uploadRef" :queueId="queueId" @reload="getList(queueId)"></upload-add>
                </el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList(queueId)"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="infoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="患者姓名" align="center">
        <template slot-scope="scope">
          <el-link type="primary" @click="view(scope.row)">{{
            scope.row.name
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="性别" align="center">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_user_sex"
            :value="scope.row.gender"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="出生日期"
        align="center"
        prop="birthday"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.birthday, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="年龄" align="center" prop="age" />
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="证件类型" align="center">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.id_type"
            :value="scope.row.idType"
          />
        </template>
      </el-table-column>
      <el-table-column label="证件号" align="center" prop="idNumber"  width="200"/>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-hasPermi="['followup:info:edit']"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            v-hasPermi="['followup:info:remove']"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList(queueId)"
    />

    <!-- 添加或修改患者信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" :disabled="title == '查看患者信息'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="患者姓名" prop="name">
              <el-input v-model="form.name" size="small" placeholder="请输入患者姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="form.gender" size="small" placeholder="请选择" style="width:240px;">
                <el-option
                  v-for="item in dict.type.sys_user_sex"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker unlink-panels 
                clearable
                size="small"
                v-model="form.birthday"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择出生日期"
                style="width:240px;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="form.phone" size="small" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="证件类型" prop="idType">
              <el-select v-model="form.idType" size="small" placeholder="请选择" style="width:240px;">
                <el-option
                  v-for="item in dict.type.id_type"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号" prop="idNumber">
              <el-input v-model="form.idNumber" size="small" placeholder="请输入证件号" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="title == '查看患者信息'">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 队列添加患者弹框 -->
    <queue-add-dialog :open="queueShow" v-if="queueId" :queueId="queueId" @reload="getList(queueId)" @cancel="queueShow = false"></queue-add-dialog>
  </div>
</template>

<script>
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
} from "@/api/followup/patient";
import queueAddDialog  from "./queueDialog.vue";
import uploadAdd from "./upload.vue"
export default {
  name: "PatientList",
  dicts: ["sys_user_sex", "patient_source",'id_type'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 患者信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        source:null,
        name: null,
        phone: null,
        minAge: null,
        maxAge: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name:[
          {
            required: true, message: '请输入患者姓名', trigger: 'blur' 
          }
        ],
        gender:[
          {
           required: true, message: '请选择性别', trigger: 'change'
          }
        ],
        birthday:[
          {
           required: true, message: '请选择出生日期', trigger: 'change'
          }
        ],
        phone:[
          { required: true, validator: this.validatePhone, trigger: "blur" }
        ],
        idType:[
          {
            required: true, message: '请选择证件类型', trigger: 'change'
          }
        ],
        idNumber:[
          {
            required: true, message: '请输入证件号', trigger: 'blur'
          }
        ]
      },
      queueId: "" ,// 队列id
      queueShow:false
    };
  },
  components:{
    queueAddDialog,
    uploadAdd
  },
  methods: {
    /** 查询患者信息列表 */
    async getList(id) {
      this.queueId = id || '';
      this.loading = true;
      try {
        const param = { ...this.queryParams, patientQueueId: this.queueId };
        const response = await listInfo(param)
        this.infoList = response.rows;
        this.total = response.total  
      }finally{
        this.loading = false   
      }
    },
    // 跳转360
    view(row) {
      if(!row.empi) return this.$modal.msgWarning('患者empi不存在！')
      if(!row.diseaseSyscode) return this.$modal.msgWarning('患者病种信息不存在！')
      localStorage.setItem('followUp_diseaseSyscode',row.diseaseSyscode)
      this.$jumpPatient360({ path: "/cdrView", query: { empi: row.empi } }, row);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        gender: null,
        birthday:null,
        idType: null,
        phone: null,
        idNumber: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList(this.queueId);
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 年龄特殊
      this.queryParams.maxAge = ""
      this.resetForm("queryForm");
      this.handleQuery(this.queueId);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加患者信息";
    },
    /**查看 */
    handleView(row) {
      this.reset();
      const id = row.id;
      getInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "查看患者信息";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改患者信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const param = {
           ...this.form,
           patientQueueId:this.queueId
          }
          if (this.form.id != null) {
            updateInfo(param).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList(this.queueId);
            });
          } else {
            addInfo(param).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList(this.queueId);
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id;
      this.$modal
        .confirm('是否确认删除该患者？')
        .then(function () {
          return delInfo(ids);
        })
        .then(() => {
          this.getList(this.queueId);
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // 更多操作触发
    handleCommand(command) {
      switch (command) {
        case "handleAdd":
          this.handleAdd();
          break;
        case "addFormQueue":
          this.queueShow = true
          break;
      }
    },
    // 自定义手机号验证规则
    validatePhone(rule, value, callback){
      const phoneRegex = /^1[3456789]\d{9}$/;
      if (!value) {
        callback(new Error("手机号不能为空"));
      } else if (this.title != "修改患者信息" && !phoneRegex.test(value)) {
        callback(new Error("请输入正确的手机号"));
      } else {
        callback();
      }
    },
    handleMaxAge(value) {
      if (parseFloat(value) < parseFloat(this.queryParams.minAge)) {
        this.queryParams.maxAge = "";
        return this.$modal.msgWarning("请输入合理值");
      }
    },
    // 打开上传弹框
    handleImport() {
     this.$refs.uploadRef.open()
    }
  },
};
</script>
<style scoped>
.range-input-container {
  display: flex;
  align-items: center;
}

.range-separator {
  margin: 0 8px;
}
::v-deep .el-form-item {
  margin-bottom: 10px;
}
</style>
