<template>
  <div class="app-container">
    <MsCrud 
      ref="crudRef" 
      :columns="columns" 
      :isShowAdd="false"
      :operateWidth="84"
      :operateBtns="['view','del']"
      primaryKey="id"
      api="followup/ae/reminder/tasks/list"
      delApi="followup/ae/reminder/tasks"
    >
      <template #search_before="{ row }">
        <el-form-item label="触发规则">
          <!-- <el-input v-model="row.approvalRoleNames" size="small" clearable placeholder="请选择" /> -->
          <el-select
							clearable
							v-model="row.pfPlanAeRulesId"
							placeholder="请选择"
							style="width: 100%"
              size="small"
              :disabled='ruleNameISDisabled'
						>
							<el-option
								v-for="item in ruleNameList"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							>
							</el-option>
						</el-select>
        </el-form-item>
      </template>
      <template #name="{ row }">
        <el-button type="text" size="small" @click="view(row)">{{ row.name }}</el-button>
      </template>
      <template #operateView="{ row }">
        <el-link type="primary" :underline="false" @click="handelView(row)">查看</el-link>
      </template>
    </MsCrud>
  </div>
</template>

<script>
import MsCrud from '@/components/MsCrud'
import { listAe,getAeAll } from "@/api/followup/ae.js"; 
export default {
  components: { MsCrud },
  data() {
    return {
      columns: [
        {
          aliasName: "患者姓名",
          elementName: "name",
          htmlType: "input",
          required: false,
          searchFlag: '1',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "性别",
          elementName: "gender",
          htmlType: "select",
          searchFlag: '0',
          listFlag: '1',
          dictType: "sys_user_sex",
          required: false,
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "年龄",
          elementName: "age",
          htmlType: "input",
          required: true,
          searchFlag: '0',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "患者电话",
          elementName: "phone",
          htmlType: "input",
          required: true,
          searchFlag: '0',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "触发规则",
          elementName: "ruleName",
          htmlType: "input",
          dictType: '',
          required: false,
          searchFlag: '0',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "触发时间",
          elementName: "createTime",
          htmlType: "date",
          dictType: '',
          required: false,
          searchFlag: '0',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '0',
        }
      ],
      ruleNameList:[]
    }
  },
  computed:{
    ruleNameISDisabled() {      
      return this.$route.query.ruleName ? true : false
    }
  },
  mounted() {    
    this.getAeList()
  },
  activated() {
    // 解决keep-alive缓存后表格样式错乱问题
    this.$refs.crudRef.$refs.tableRef.doLayout()
  },
  methods: {
    handelView(row) {
      this.$router.push({
        path: "/tasks/details",
        query: {
          patientId: row.pfPatientInfoId,
          taskFormId: row.taskFormId,
          fieldName:row.fieldName, // 字段id
          readOnly:true,
          isAe:true
        },
      });
    },
    // 跳转360
    view(row) {
      if(!row.empi) return this.$modal.msgWarning('患者empi不存在！')
      if(!row.diseaseSyscode) return this.$modal.msgWarning('患者病种信息不存在！')
      localStorage.setItem('followUp_diseaseSyscode',row.diseaseSyscode)
      this.$jumpPatient360({ path: "/cdrView", query: { empi: row.empi } }, row);
    },
    // 请求ae
    getAeList() {
      getAeAll().then(res => {
        this.ruleNameList = res.data.map(item => {
          return {
            label:item.ruleName,
            value:item.id
          }
        })
        // ae规则跳转 ，获取路由参数、显示当前规则触发的不良事件
        if (this.$route.query.ruleName) {
          const ruleNameId = this.ruleNameList.find(item => item.label == this.$route.query.ruleName)?.value
          this.$refs.crudRef.formData.pfPlanAeRulesId = ruleNameId
        }
        this.$nextTick(() => {
          this.$refs.crudRef.onSearch()
        })
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.app-container {
  background: #f5f5f5;
  height: 100%;
}
</style>