import Vue from "vue";

import Cookies from "js-cookie";

import Element from "element-ui";
import "./assets/styles/element-variables.scss";

import "@/assets/styles/index.scss"; // global css
import "@/assets/styles/ruoyi.scss"; // ruoyi css
import App from "./App";
import store from "./store";
import router from "./router";
import directive from "./directive"; // directive
import plugins from "./plugins"; // plugins
import { download } from "@/utils/request";

import "./assets/icons"; // icon
import "./permission"; // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  handleTree,
} from "@/utils/ruoyi";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar";
// 富文本组件
import Editor from "@/components/Editor";
// 文件上传组件
import FileUpload from "@/components/FileUpload";
import FileUploadSimple from "@/components/FileUploadSimple";
// 图片上传组件
import ImageUpload from "@/components/ImageUpload";
// 图片预览组件
import ImagePreview from "@/components/ImagePreview";
// 字典标签组件
import DictTag from "@/components/DictTag";
// 头部标签组件
import VueMeta from "vue-meta";
// 字典数据组件
import DictData from "@/components/DictData";
//全局引入echarts
import * as echarts from "echarts";
//引入vForm
import VForm from "../lib/vform/VFormDesigner.umd.min";
import "../lib/vform/VFormDesigner.css";
//打印插件
import Print from "vue-print-nb";
// 获取字典数据
import { getDictOptions } from "@/utils/dictOptions";
import uploader from "vue-simple-uploader";

import request from '@/utils/request'
window.msRequest = request
import jumpPatient360 from "@/utils/jumpPatient360";
// 全局方法挂载
Vue.prototype.getDicts = getDicts;
Vue.prototype.getConfigKey = getConfigKey;
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.selectDictLabels = selectDictLabels;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;
Vue.prototype.$getDictOptions = getDictOptions;
Vue.prototype.$jumpPatient360 = jumpPatient360;
//需要挂载到Vue原型上
Vue.prototype.$echarts = echarts;

// 全局组件挂载
Vue.component("DictTag", DictTag);
Vue.component("Pagination", Pagination);
Vue.component("RightToolbar", RightToolbar);
Vue.component("Editor", Editor);
Vue.component("FileUpload", FileUpload);
Vue.component("ImageUpload", ImageUpload);
Vue.component("ImagePreview", ImagePreview);
Vue.component("FileUploadSimple", FileUploadSimple);

Vue.use(directive);
Vue.use(plugins);
Vue.use(VueMeta);
DictData.install();
Vue.use(VForm);
Vue.use(Print);
Vue.use(uploader);

import dataV from '@jiaminghi/data-view'
Vue.use(dataV)

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

// 监听el-table滚动
Vue.directive("loadmore", {
  bind(el, binding) {
    const selectWrap = el.querySelector(".el-table__body-wrapper");
    selectWrap.addEventListener("scroll", function () {
      let sign = 0;
      const scrollDistance =
        this.scrollHeight - this.scrollTop - this.clientHeight - 1;
      if (scrollDistance <= sign) {
        binding.value();
      }
    });
  },
});

Vue.use(Element, {
  size: Cookies.get("size") || "medium", // set element-ui default size
});

Vue.config.productionTip = false;
Vue.config.devtools = true;
new Vue({
  el: "#app",
  router,
  store,
  render: (h) => h(App),
});
