import { login, logout, getInfo, refreshToken } from "@/api/login";
import { getToken, setToken, setExpiresIn, removeToken } from "@/utils/auth";
import { listConfig } from "@/api/system/config"
import cache from "@/plugins/cache";
import { getCaseTotal } from "@/api/scientific/chartStatistics";
const colors = ["#FE6058", "#D81E06", "#EB641C", "#F5B5FD", "#4BAEAF"];
const CryptoJS = require('crypto-js');
function aes256Encrypt(data)  {
    let f = "AB34567890789MPC"
const secretKey = CryptoJS.enc.Utf8.parse(f);
const encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(data), secretKey, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
});
    return encrypted.toString();
}
/**
 *
 * 随访系统设置中心id与项目联动
 */
function setDeptId(state,commit) {
  if (state.projectInfoId) {
    const deptsObj = state.projectInfos.find(
      (item) => item.projectInfoId == state.projectInfoId
    );
    if (deptsObj) {
      commit("SET_FOLLOWUP_DEPTS", deptsObj.children);
      const deptId =
          sessionStorage.getItem("followup_deptId") ||
          (deptsObj.children &&
            deptsObj.children.length > 0 &&
            deptsObj.children[0].deptId) ||
          "";
        commit("SET_DEPTID", deptId ? Number(deptId) : "");
    }
  }
}
const user = {
  state: {
    token: getToken(),
    name: "",
    nickName: "",
    avatar: "",
    roles: [],
    permissions: [],
    depDiseaseList: [], //用户所属科室下的病种列表
    currentSelectedDisease: null,
    fileDomain: "",
    projectInfos: [], // 项目列表
    depts: [], // 中心列表
    projectInfoId: "", // 项目id
    deptId: "", // 中心id
    currentRoute: null,
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_EXPIRES_IN: (state, time) => {
      state.expires_in = time;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_NICK_NAME: (state, name) => {
      state.nickName = name;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions;
    },
    SET_DEP_DISEASE_LIST: (state, list) => {
      state.depDiseaseList = list;
    },
    SET_CURRENT_DISEASE: (state, obj) => {
      state.currentSelectedDisease = obj;
      localStorage.setItem("diseaseModule_currentDisease", JSON.stringify(obj));
    },
    SET_FILE_DOMAIN: (state, domain) => {
      state.fileDomain = domain;
      window.fileDomain = domain
    },
    SET_FOLLOWUP_PROJECTINFO: (state, projectInfos) => {
      state.projectInfos = projectInfos;
    },
    SET_FOLLOWUP_DEPTS: (state, depts) => {
      state.depts = depts;
    },
    SET_PROJECTID: (state, id) => {
      state.projectInfoId = id;
      sessionStorage.setItem("followup_projectInfoId", id);
    },
    SET_DEPTID: (state, id) => {
      state.deptId = id;
      sessionStorage.setItem("followup_deptId", id);
    },
    REMOVE_DEPTID:(state,id) => {
      state.deptId = id
      sessionStorage.removeItem("followup_deptId");
    },
    SET_ROUTE(state, route) {
      state.currentRoute = route;
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim();
      const password = aes256Encrypt(userInfo.password);
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid)
          .then((res) => {
            let data = res.data;
            setToken(data.access_token);
            commit("SET_TOKEN", data.access_token);
            setExpiresIn(data.expires_in);
            commit("SET_EXPIRES_IN", data.expires_in);
            // 获取水印开关状态
            listConfig().then(res => {
              if (res.rows && res.rows.length) {
                res.rows.forEach(item => {
                  if (item.configKey == 'waterMarkStatus') {
                    sessionStorage.setItem('waterMarkStatus', item.configValue)
                  } else if (item.configKey == 'diseaseFilterStatus') {
                    sessionStorage.setItem('diseaseFilterStatus', item.configValue)
                  } else if (item.configKey == 'systemTheme') {
                    localStorage.setItem('systemTheme', item.configValue)
                  } else if (item.configKey == 'mapJsonConfig') {
                    localStorage.setItem('mapJsonConfig', item.configValue)
                  } else if (item.configKey == 'patient360Path') {
                    localStorage.setItem('patient360Path', item.configValue)
                  }
                })
              }
            })
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            const user = res.user;
            // 本地存储用户信息
            localStorage.setItem("userInfo", JSON.stringify(user));
            const avatar =
              user.userName == "admin"
                ? require("@/assets/images/manadmin.png")
                : user.avatar == ""
                ? user.sex == 0
                  ? require("@/assets/images/man.png")
                  : require("@/assets/images/woman.png")
                : user.avatar;
            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit("SET_ROLES", res.roles);
              commit("SET_PERMISSIONS", res.permissions);
            } else {
              commit("SET_ROLES", ["ROLE_DEFAULT"]);
            }
            commit("SET_NAME", user.userName);
            commit("SET_NICK_NAME", user.nickName);
            commit("SET_AVATAR", avatar);
            commit("SET_FILE_DOMAIN", res.domain);
            commit("SET_FOLLOWUP_PROJECTINFO", res.projectInfos);

            // 随访系统,处理项目和中心,默认选择第一项
            const projectInfoId =
              sessionStorage.getItem("followup_projectInfoId") ||
              (res.projectInfos &&
                res.projectInfos.length > 0 &&
                res.projectInfos[0].projectInfoId) ||
              "";
            commit("SET_PROJECTID", projectInfoId ? Number(projectInfoId) : "");
            // 随访系统项目、中心联动选择
            if (
              res.projectInfos &&
              res.projectInfos.length > 0 &&
              cache.local.get("currentMenuId") == 2231
            ) {
              setDeptId(state, commit);
            }
            if (res.diseaseList && res.diseaseList.length > 0) {
              if (
                (cache.local.get("currentMenuId") == 2576 ||
                  cache.local.get("currentMenuId") == 2041) &&
                state.currentRoute != "/index"
              ) {
                // 全院科研平台、科研专病系统增加病例数
                const diseaseSyscodes = res.diseaseList.map(
                  (item) => item.diseaseSyscode
                );
                const diseaseSyscodesString = diseaseSyscodes.join(',');
                const params = {
                  diseaseSyscodes: diseaseSyscodesString
                }
                getCaseTotal(params).then((result) => {
                  res.diseaseList.forEach((item, index) => {
                    for (var key in result.data) {
                      if (item.diseaseSyscode == key) {
                        item.total = result.data[key];
                        item.color = colors[index];
                      }
                    }
                  });
                });
              }
              commit("SET_DEP_DISEASE_LIST", res.diseaseList);
              let diseaseModule_currentDisease =
                localStorage.getItem("diseaseModule_currentDisease") || null;
              if (diseaseModule_currentDisease) {
                let exists = res.diseaseList.filter((item) => {
                  return (
                    item.diseaseSyscode ==
                    JSON.parse(diseaseModule_currentDisease).diseaseSyscode
                  );
                });
                //判断返回的数据是否包含本地存储的病种, 如果包含则使用本地的病种 , 否则默认选中第一条
                if (exists.length > 0) {
                  commit("SET_CURRENT_DISEASE", exists[0]);
                } else {
                  commit("SET_CURRENT_DISEASE", res.diseaseList[0]);
                }
              } else {
                commit("SET_CURRENT_DISEASE", res.diseaseList[0]);
              }
            } else {
              localStorage.removeItem("diseaseModule_currentDisease");
            }
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 刷新token
    RefreshToken({ commit, state }) {
      return new Promise((resolve, reject) => {
        refreshToken(state.token)
          .then((res) => {
            setExpiresIn(res.data);
            commit("SET_EXPIRES_IN", res.data);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit("SET_TOKEN", "");
            commit("SET_ROLES", []);
            commit("SET_PERMISSIONS", []);
            commit("SET_EXPIRES_IN", []);
            removeToken();
            localStorage.removeItem("currentMenuId");
            localStorage.removeItem("isChangeMenuId");
            localStorage.removeItem("userInfo");
            sessionStorage.removeItem("followup_projectInfoId");
            sessionStorage.removeItem("followup_deptId");
            localStorage.clear();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit("SET_TOKEN", "");
        removeToken();
        resolve();
      });
    },
    // 随访系统处理项目改变时，处理中心
    setFollowupDeptId({ commit, state },val) {
      return new Promise((resolve) => {
        commit("REMOVE_DEPTID","");
        if(state.projectInfos.length > 0) {
            setDeptId(state, commit);
          }
       });
    },
  },
};

export default user;
