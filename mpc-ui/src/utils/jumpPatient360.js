export default function (defaultQuery, row) {
  function renderTemplate(template, context) {
    return template.replace(/\$\{([^}]+)\}/g, function (match, expression) {
      // 简单解析 a || b 形式的表达式
      if (expression.includes("||")) {
        const [left, right] = expression.split("||").map((s) => s.trim());
        // 从 context 对象中获取值
        const leftVal = context[left] !== undefined ? context[left] : "";
        const rightVal = context[right] !== undefined ? context[right] : "";
        return leftVal || rightVal;
      } else {
        // 处理简单的变量名
        return context[expression] !== undefined ? context[expression] : "";
      }
    });
  }
  if (localStorage.getItem("patient360Path")) {
    let path = renderTemplate(localStorage.getItem("patient360Path"), row);
    this.$confirm(`是否跳转外部患者360，请确认?`, '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      window.open(path, "_blank");
    }).catch((e) => {
      if (e == 'cancel') {
        this.$router.push(defaultQuery);
      }
    });
    
  } else {
    this.$router.push(defaultQuery);
  }
}
