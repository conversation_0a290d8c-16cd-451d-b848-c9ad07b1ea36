import axios from "axios";
import { Notification, MessageBox, Message, Loading } from "element-ui";
import store from "@/store";
import { getToken } from "@/utils/auth";
import { formDataToMd5 } from "@/utils/md5";
import errorCode from "@/utils/errorCode";
import { tansParams, blobValidate } from "@/utils/ruoyi";
import cache from "@/plugins/cache";
import { saveAs } from "file-saver";
import router from "@/router";
import SparkMD5 from "spark-md5";

let downloadLoadingInstance;
// 是否显示重新登录
let isReloginShow;

axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API,
  // 超时
  timeout: 600000,
});

// request拦截器
service.interceptors.request.use(
  (config) => {
    // 方便本地联调
    config.baseURL = config.localUrl || process.env.VUE_APP_BASE_API;
    //
    let modelRouterPath = router.currentRoute.meta.modelRouterPath;
    if (modelRouterPath) {
      modelRouterPath =
        modelRouterPath.indexOf("/") === -1
          ? `/${modelRouterPath}`
          : modelRouterPath;
    }
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false;
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
    if (getToken() && !isToken) {
      config.headers["Authorization"] = "Bearer " + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    let currentDisease =
      localStorage.getItem("diseaseModule_currentDisease") || null;
    // let currentPath = localStorage.getItem("currentPath");

    // console.log(" 当前PATH",config)
    // console.log(" 当前currentPath",currentPath)
    //  !config.headers["diseaseSyscode"] && modelRouterPath == "/disease"
    if (modelRouterPath == "/disease") {
      config.headers["diseaseSyscode"] =
        currentDisease && currentDisease != "{}"
          ? JSON.parse(currentDisease).diseaseSyscode
          : "";
    }
    // 随访系统
    if (modelRouterPath == "/followup") {
      config.headers["diseaseSyscode"] =
        localStorage.getItem("followUp_diseaseSyscode") || "";
      config.headers["projectInfoId"] = store.getters.projectInfoId;
      config.headers["deptId"] = store.getters.deptId;
    }
    setSign(config);
    if (config.method === "get" && config.params) {
      let tansparams = tansParams(config.params);
      tansparams = tansparams.endsWith("&")
        ? tansparams.slice(0, -1)
        : tansparams;
      let url = "";
      if (tansparams.length < 1) {
        url = config.url;
      } else {
        url = config.url + "?" + tansparams;
      }
      config.params = {};
      config.url = url;
    }
    if (
      !isRepeatSubmit &&
      (config.method === "post" || config.method === "put")
    ) {
      const requestObj = {
        url: config.url,
        data:
          typeof config.data === "object"
            ? JSON.stringify(config.data)
            : config.data,
        time: new Date().getTime(),
      };
      let md5 = SparkMD5.hash(requestObj.url + requestObj.data);
      if (config.data instanceof FormData) {
        md5 = formDataToMd5(config.data);
        console.log("formdata md5", md5);
      }
      requestObj.md5 = md5;
      const sessionObjKey = "sessionObj_" + md5;
      const sessionObj = cache.session.getJSON(sessionObjKey);
      const interval = 1000; // 间隔时间(ms)，小于此时间视为重复提交
      if (!!!sessionObj) {
        cache.session.setJSON(sessionObjKey, requestObj, interval);
      } else {
        const s_url = sessionObj.url; // 请求地址
        const s_time = sessionObj.time; // 请求时间
        if (requestObj.time - s_time < interval) {
          const message = "数据正在处理，请勿重复提交";
          console.warn(`[${s_url}]: ` + message);
          return Promise.reject(new Error(message));
        } else {
          cache.session.setJSON(sessionObjKey, requestObj);
        }
      }
      //
      setTimeout(() => {
        sessionStorage.removeItem(sessionObjKey);
      }, 1200);
    }
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);


// 响应拦截器
service.interceptors.response.use(
  (res) => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200;
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode["default"];
    // 二进制数据则直接返回
    if (
      res.request.responseType === "blob" ||
      res.request.responseType === "arraybuffer"
    ) {
      return res.data;
    }
    if (code === 401) {
      if (!isReloginShow) {
        isReloginShow = true;
        MessageBox.confirm(
          "登录状态已过期，您可以继续留在该页面，或者重新登录",
          "系统提示",
          {
            confirmButtonText: "重新登录",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            isReloginShow = false;
            store.dispatch("LogOut").then(() => {
              // 如果是登录页面不需要重新加载
              if (window.location.hash.indexOf("#/login") != 0) {
                location.href = "/index";
              }
            });
          })
          .catch(() => {
            isReloginShow = false;
          });
      }
      return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
    } else if (code === 500) {
      if (!res.config.url.includes("pearsonDataCheck")) {
        Message({
          message: msg,
          type: "error",
          showClose: true,
        });
      };
      return Promise.reject(msg);
    } else if (code !== 200) {
      Notification.error({
        title: msg,
      });
      return Promise.reject("error");
    } else {
      return res.data;
    }
  },
  (error) => {
    if (!axios.isCancel(error)) {
      if (error.response && error.response.data.path.includes("areas_v3")) return
      let { message } = error;
      if (message == "Network Error") {
        message = "后端接口连接异常";
      } else if (message.includes("timeout")) {
        message = "系统接口请求超时";
      } else if (message.includes("Request failed with status code")) {
        message = "系统接口" + message.substr(message.length - 3) + "异常";
      }
      Message({
        message: message,
        type: "error",
        duration: 3 * 1000,
        showClose: true,
      });
      return Promise.reject(error);
    }
  }
);

// 通用下载方法
export function download(url, params, filename) {
  downloadLoadingInstance = Loading.service({
    text: "正在下载数据，请稍候",
    spinner: "el-icon-loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  return service
    .post(url, params, {
      transformRequest: [
        (params) => {
          return tansParams(params);
        },
      ],
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      responseType: "blob",
    })
    .then(async (data) => {
      const isLogin = await blobValidate(data);
      if (isLogin) {
        const blob = new Blob([data]);
        saveAs(blob, filename);
      } else {
        const resText = await data.text();
        const rspObj = JSON.parse(resText);
        const errMsg =
          errorCode[rspObj.code] || rspObj.msg || errorCode["default"];
        Message.error(errMsg);
      }
      downloadLoadingInstance.close();
    })
    .catch((r) => {
      console.error(r);
      Message.error("下载文件出现错误，请联系管理员！");
      downloadLoadingInstance.close();
    });
}


//通用下载方法 - 请求参数不需要转换时用此方法
export function downloadFile(url, params, filename) {
  downloadLoadingInstance = Loading.service({
    text: "正在下载数据，请稍候",
    spinner: "el-icon-loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  return service
    .post(url, params, {
      // transformRequest: [(params) => { return tansParams(params) }],
      headers: { "Content-Type": "application/json" },
      responseType: "blob",
    })
    .then(async (data) => {
      const isLogin = await blobValidate(data);
      if (isLogin) {
        const blob = new Blob([data]);
        saveAs(blob, filename);
      } else {
        const resText = await data.text();
        const rspObj = JSON.parse(resText);
        const errMsg =
          errorCode[rspObj.code] || rspObj.msg || errorCode["default"];
        Message.error(errMsg);
      }
      downloadLoadingInstance.close();
    })
    .catch((r) => {
      console.error(r);
      Message.error("下载文件出现错误，请联系管理员！");
      downloadLoadingInstance.close();
    });
}
// 签名认证
function setSign(config) {
  if (config.method === "get") {
    let tansparams=  ""
    if(config.params){
      tansparams = tansParams(config.params);
      tansparams = tansparams.endsWith('&') ? tansparams.slice(0, -1) : tansparams
    }
    let sigId =  GetUUID()
    config.headers["SigId"]= sigId
    config.headers["Sign"] = dataToMd5(sigId+tansparams)
  }
  if(config.method=="post"){
    let sigId =  GetUUID()
    config.headers["SigId"]= sigId
    config.headers["Sign"] = dataToMd5(sigId+JSON.stringify(config.data))
  }
}

function dataToMd5(params) {
  let spark = new SparkMD5();
  spark.append(params); // 计算字符串
  let sum = spark.end(); // 结束计算并返回结果
  // console.info("打印计算MD5 tansparams",tansparams)
  // console.info("打印计算MD5",sum)
  return sum
}
function GetUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
    return v.toString(16);
  });
}


export default service;
