<template>
  <div class="chart-box" :style="{ height, width }">
    <div
      ref="chart"
      style="width: 100%; height: 100%"
    ></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import "echarts-liquidfill";
import resize from "./resize";
export default {
  mixins: [resize],
  props: {
    height: {
      type: String,
      default: "80%",
    },
    width: {
      type: String,
      default: "100%",
    },
  },
  data() {
    return {
      chart: null,
      colors: [
        "#4992ff",
        "#7cffb2",
        "#fddd60",
        "#ff6e76",
        "#58d9f9",
        "#05c091",
        "#ff8a45",
        "#8d48e3",
        "#dd79ff",
        "#4992ff",
        "#7cffb2",
        "#fddd60",
        "#ff6e76",
        "#58d9f9",
        "#05c091",
        "#ff8a45",
        "#8d48e3",
        "#dd79ff",
      ],
    };
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(data = {diskUsage: 50, diskWarning: "G"}) {
      if (this.chart) {
        this.chart.dispose();
        this.chart = null
      }
      this.chart = echarts.init(this.$refs.chart);
      let color = {
        G: '#00baad',
        Y: '#ff8d1a',
        R: '#ff1010',
      }
      this.chart.setOption({
        series: [
          {
            type: "liquidFill", //设置图表类型
            data: [data.diskUsage * 0.01], // 设置水位，值为0到1之间
            waveAnimation: true, //是否开启水波动画,
            amplitude: 4, // 设置振幅，值越大波形越尖
            waveLength: 60, //水波的长度，值越大水波越长
            shape: "circle", //设置形状，可选为'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow'
            direction: "right", //设置方向，可选为'left', 'right', 'top', 'bottom',
            radius: "80%", // 设置图的大小  默认为50%
            color: [color[data.diskWarning]], // 设置颜色，可以设置多个值，用来设置多个水位
            center: ["50%", "50%"], //中心点的位置
            animationEasing: "linear",
            outline: {
              borderDistance: 2,
              itemStyle: {
                borderWidth: 1, // 外边框宽度
                borderColor: color[data.diskWarning], // 外边框颜色
              },
            },
             // 水波样式
            itemStyle:{
                opacity:0.5,                        // 透明度
                color: color[data.diskWarning],      // 统一配置水波的颜色
                shadowBlur:10,                      // 水波阴影大小
                shadowColor: color[data.diskWarning],// 阴影颜色
                shadowOffsetX:10,                   // 阴影水平偏移量
                shadowOffsetY:10,                   // 阴影竖直偏移量
            },
            backgroundStyle: {
              color: "#fff" // 背景色
            },
            label: {
              normal: {
                textStyle: {
                  insideColor: "#fff",
                  fontSize: 30,
                },
              },
            },
          },
        ],
      });
    },
    // 颜色转换
    hexToRgb(hex, opacity) {
      // 去掉开头的 # 符号
      hex = hex.replace("#", "");
      // 提取 R、G、B 部分
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);
      // 返回 RGB 格式
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    },
  },
};
</script>
<style lang="scss" scoped>
.chart-box {
  padding: 10px;
}
</style>
