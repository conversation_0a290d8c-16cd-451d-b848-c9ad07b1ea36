# template部分
<template>
  <div class="file-uploader">
    <uploader 
      ref="uploader"
      :options="uploaderOptions"
      :autoStart="false"
      @files-added="onFilesAdded"
      @file-success="onFileSuccess"
      @file-error="onFileError"
      @file-removed="onFileRemoved"
      :file-status-text="fileStatusText"
    >
      <!-- 不支持HTML5的提示 -->
      <uploader-unsupport></uploader-unsupport>
      
      <!-- 上传按钮 -->
      <div class="upload-btns">
        <uploader-btn class="btn" ref="fileBtn">选择文件</uploader-btn>
        <uploader-btn class="btn" :directory="true" ref="dirBtn">选择文件夹</uploader-btn>
      </div>

      <!-- 拖拽区域 -->
      <uploader-drop>
        <div class="drop-area">
          <i class="el-icon-upload"></i>
          <p class="drop-tips">
            将文件拖拽到此处，或
            <span class="link" @click="triggerUpload('fileBtn')">上传文件</span>
            /
            <span class="link" @click="triggerUpload('dirBtn')">上传文件夹</span>
          </p>
        </div>
      </uploader-drop>

      <!-- 文件列表 -->
      <uploader-list>
        <template v-slot:default="props">
          <div v-if="props.fileList.length" class="file-list">
            <div class="list-header">
              <span>上传列表 ({{ props.fileList.length }})</span>
            </div>

            <ul class="list-content">
              <li v-for="file in props.fileList" :key="file.id" class="list-item">
                <uploader-file :file="file" :list="true">
                  <template v-slot:default="fileProps">
                    <!-- 进度条 -->
                    <div class="progress-bar"
                      :class="{'uploading': fileProps.status === 'uploading'}"
                      :style="{
                        width: fileProps.progressStyle.progress,
                        backgroundColor: fileProps.status === 'error' ? '#ffebeb' : '#e8f3ff'
                      }"
                    ></div>

                    <!-- 文件信息 -->
                    <div class="file-info">
                      <div class="file-name">
                        <i class="el-icon-document"></i>
                        {{ fileProps.file.name }}
                      </div>
                      
                      <div class="file-size">{{ fileProps.formatedSize }}</div>

                      <!-- 计算MD5时的状态 -->
                      <div v-if="hashProgress[file.id]" class="file-status">
                        {{ hashProgress[file.id] }}
                      </div>

                      <!-- 上传状态 -->
                      <div v-else class="file-status">
                        <template v-if="fileProps.status === 'uploading'">
                          <span>{{ fileProps.progressStyle.progress }}</span>
                          <em>{{ fileProps.formatedAverageSpeed }}</em>
                          <span v-if="fileProps.formatedTimeRemaining">
                            预计剩余 {{ fileProps.formatedTimeRemaining }}
                          </span>
                        </template>
                        <template v-else>
                          {{ fileStatusText[fileProps.status] }}
                        </template>
                      </div>

                      <!-- 操作按钮 -->
                      <div class="file-actions">
                        <el-button 
                          v-if="fileProps.status === 'error'"
                          type="text"
                          @click="retryFile(fileProps.file)"
                        >
                          重试
                        </el-button>
                        <el-button
                          v-if="!hashProgress[file.id]"
                          type="text"
                          @click="removeFile(fileProps.file)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </template>
                </uploader-file>
              </li>
            </ul>
          </div>
        </template>
      </uploader-list>

    </uploader>
  </div>
</template>

<script>
import SparkMD5 from 'spark-md5'
import { getToken } from '@/utils/auth'
import { mergeChunks } from '@/api/file/file'

// 分片大小20MB
const CHUNK_SIZE = 20 * 1024 * 1024

export default {
  name: 'FileUploader',
  
  props: {
    // 上传时需要的额外参数
    uploadParams: {
      type: Object,
      default: () => ({})
    },
    // 允许上传的文件类型
    accept: {
      type: String,
      default: '.zip,.rar'
    }
  },

  data() {
    return {
      // 上传配置
      uploaderOptions: {
        target: process.env.VUE_APP_BASE_API + '/file/chunk',
        chunkSize: CHUNK_SIZE,
        fileParameterName: 'file',
        maxChunkRetries: 3,
        testChunks: true,
        headers: {
          Authorization: 'Bearer ' + getToken()
        },
        
        // 检查分片是否已上传
        checkChunkUploadedByResponse: (chunk, response) => {
          const res = JSON.parse(response)
          if (res.code && res.code !== 200) {
            chunk.uploader.pause()
            return false
          }
          return (res.chunkNumbers || []).indexOf(chunk.offset + 1) >= 0
        },

        // 格式化剩余时间显示
        parseTimeRemaining: (timeRemaining, parsed) => {
          return parsed
            .replace(/\syears?/, '年')
            .replace(/\sdays?/, '天') 
            .replace(/\shours?/, '小时')
            .replace(/\sminutes?/, '分钟')
            .replace(/\sseconds?/, '秒')
        }
      },

      // 文件状态文本
      fileStatusText: {
        success: '上传成功',
        error: '上传失败',
        uploading: '上传中',
        paused: '已暂停',
        waiting: '等待中'
      },

      // 记录文件计算hash的进度
      hashProgress: {},

      // 文件列表
      fileList: []
    }
  },

  methods: {
    // 触发上传按钮点击
    triggerUpload(btnRef) {
      this.$refs[btnRef].$el.click()
    },

    // 添加文件
    onFilesAdded(files) {
      this.fileList.push(...files)
      this.$emit('files-added', this.fileList)
      
      // 为每个文件计算hash
      files.forEach(file => {
        this.computeHash(file)
      })
    },

    // 移除文件
    onFileRemoved(file) {
      this.fileList = this.fileList.filter(item => item.id !== file.id)
      this.$emit('files-removed', this.fileList)
    },

    // 上传成功
    async onFileSuccess(rootFile, file, response) {
      const res = response ? JSON.parse(response) : null
      
      // 上传成功但需要合并分片
      if (res && res.code === 205) {
        const formData = new FormData()
        formData.append('identifier', file.uniqueIdentifier)
        formData.append('filename', file.name)
        formData.append('relativePath', file.relativePath)
        formData.append('totalSize', file.size)

        try {
          const mergeRes = await mergeChunks(formData)
          
          // 更新文件状态
          this.$emit('file-merged', {
            file,
            url: mergeRes.data.fileUrl
          })

          // 从列表中移除
          this.fileList = this.fileList.filter(item => item.id !== file.id)
          
          // 发送通知
          if (Notification.permission === 'granted') {
            new Notification('上传完成', {
              body: file.name
            })
          }

        } catch (err) {
          this.$message.error('文件合并失败')
        }
      }
    },

    // 上传出错
    onFileError(rootFile, file, response) {
      this.$message.error(`文件 ${file.name} 上传失败: ${response}`)
    },

    // 计算文件hash(MD5)
    computeHash(file) {
      return new Promise((resolve) => {
        this.$set(this.hashProgress, file.id, '计算文件特征值 0%')
        
        const fileReader = new FileReader()
        const spark = new SparkMD5.ArrayBuffer()
        const chunks = Math.ceil(file.size / CHUNK_SIZE)
        let currentChunk = 0
        
        // 暂停上传,等待hash计算完成
        file.pause()
        
        const loadNext = () => {
          const start = currentChunk * CHUNK_SIZE
          const end = Math.min(start + CHUNK_SIZE, file.size)
          fileReader.readAsArrayBuffer(file.file.slice(start, end))
        }

        fileReader.onload = (e) => {
          spark.append(e.target.result)
          currentChunk++

          if (currentChunk < chunks) {
            this.$set(
              this.hashProgress,
              file.id,
              `计算文件特征值 ${((currentChunk / chunks) * 100).toFixed(0)}%`
            )
            loadNext()
          } else {
            const hash = spark.end()
            this.$set(this.hashProgress, file.id, '')
            file.uniqueIdentifier = hash
            
            // 检查文件是否已存在
            this.checkFileExists(file).then(() => {
              file.resume()
              resolve()
            })
          }
        }

        fileReader.onerror = () => {
          this.$message.error(`文件 ${file.name} 读取失败`)
          file.cancel()
          resolve()
        }

        loadNext()
      })
    },

    // 检查文件是否已存在
    async checkFileExists(file) {
      try {
        const res = await this.$http.post('/api/check-file', {
          hash: file.uniqueIdentifier,
          ...this.uploadParams
        })

        if (res.data.exists) {
          this.$message.warning('文件已存在,跳过上传')
          file.cancel()
        }
      } catch (err) {
        this.$message.error('检查文件失败')
      }
    },

    // 开始上传
    async startUpload() {
      const uploader = this.$refs.uploader.uploader
      if (uploader.isUploading()) return

      for (const file of this.fileList) {
        if (!file.completed) {
          await this.computeHash(file)
        }
      }
    },

    // 重试上传
    retryFile(file) {
      file.retry()
    },

    // 删除文件
    removeFile(file) {
      file.cancel()
    }
  }
}
</script>

<style lang="scss" scoped>
.file-uploader {
  .upload-btns {
    display: none;
  }
  
  .drop-area {
    padding: 30px;
    border: 2px dashed #e9e9e9;
    border-radius: 6px;
    text-align: center;
    
    &:hover {
      border-color: #409eff;
    }

    i {
      font-size: 48px;
      color: #c0c4cc;
    }

    .drop-tips {
      color: #606266;
      margin: 10px 0 0;

      .link {
        color: #409eff;
        cursor: pointer;
        
        &:hover {
          color: #66b1ff;
        }
      }
    }
  }

  .file-list {
    margin-top: 16px;

    .list-header {
      margin-bottom: 8px;
      color: #606266;
    }

    .list-item {
      position: relative;
      padding: 8px 12px;
      border: 1px solid #e9e9e9;
      border-radius: 4px;
      margin-bottom: 8px;

      .progress-bar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        transition: width 0.3s;
        opacity: 0.1;

        &.uploading {
          opacity: 0.2;
        }
      }

      .file-info {
        position: relative;
        display: flex;
        align-items: center;

        .file-name {
          flex: 2;
          @include ellipsis;

          i {
            margin-right: 6px;
          }
        }

        .file-size {
          width: 100px;
          text-align: right;
          color: #909399;
        }

        .file-status {
          width: 300px;
          text-align: right;
          color: #606266;

          em {
            color: #909399;
            font-style: normal;
            margin: 0 6px;
          }
        }

        .file-actions {
          width: 100px;
          text-align: right;
        }
      }
    }
  }
}
</style>
